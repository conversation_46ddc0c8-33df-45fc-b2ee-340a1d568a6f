#!/usr/bin/env python3
"""
AdhocLog - Import Utility
Handles Excel/CSV import functionality with flexible column detection
"""

import pandas as pd
import json
import os
import re
from datetime import datetime
from typing import List, Dict, Optional, Any
from pathlib import Path
import getpass
from app.config import Config


class ImportUtility:
    """Utility class for importing tasks from Excel/CSV files"""

    def __init__(self):
        self.config = Config()
        self.username = getpass.getuser()

        # Column mapping - flexible header detection (case insensitive)
        self.column_mapping = {
            'date': ['date', 'task date', 'date created', 'created date'],
            'team_member': ['team member', 'team_member', 'member', 'employee', 'user', 'person'],
            'title': ['task title', 'title', 'task', 'task name', 'name', 'description title'],
            'classification': ['classification', 'type', 'task type', 'category type', 'task classification'],
            'description': ['actions taken / description', 'description', 'actions taken', 'actions', 'details', 'notes', 'comments'],
            'est_time': ['estimated time (minute)', 'estimated time', 'est time', 'time', 'minutes', 'duration', 'est_time'],
            'category': ['category', 'task category', 'main category']
        }

    def detect_file_type(self, file_path: str) -> str:
        """Detect if file is Excel or CSV"""
        file_ext = Path(file_path).suffix.lower()
        if file_ext in ['.xlsx', '.xls']:
            return 'excel'
        elif file_ext in ['.csv']:
            return 'csv'
        else:
            raise ValueError(f"Unsupported file type: {file_ext}. Supported types: .xlsx, .xls, .csv")

    def load_file(self, file_path: str, sheet_name: Optional[str] = None) -> pd.DataFrame:
        """Load Excel or CSV file into pandas DataFrame"""
        file_type = self.detect_file_type(file_path)

        try:
            if file_type == 'excel':
                # For Excel files, read first sheet by default
                df = pd.read_excel(file_path, sheet_name=sheet_name or 0)
            else:  # CSV
                # Try different encodings for CSV files
                for encoding in ['utf-8', 'latin-1', 'cp1252']:
                    try:
                        df = pd.read_csv(file_path, encoding=encoding)
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    raise ValueError("Could not decode CSV file with any supported encoding")

            return df

        except Exception as e:
            raise ValueError(f"Error reading file: {str(e)}")

    def normalize_column_name(self, column_name: str) -> str:
        """Normalize column name for matching"""
        if pd.isna(column_name):
            return ""
        return str(column_name).strip().lower()

    def map_columns(self, df: pd.DataFrame) -> Dict[str, str]:
        """Map DataFrame columns to expected task fields"""
        column_map = {}
        df_columns = [self.normalize_column_name(col) for col in df.columns]

        for field, possible_names in self.column_mapping.items():
            matched_column = None

            # Try exact matches first
            for possible_name in possible_names:
                normalized_possible = possible_name.lower().strip()
                if normalized_possible in df_columns:
                    original_column = df.columns[df_columns.index(normalized_possible)]
                    column_map[field] = original_column
                    matched_column = original_column
                    break

            # If no exact match, try partial matches
            if not matched_column:
                for possible_name in possible_names:
                    normalized_possible = possible_name.lower().strip()
                    for i, df_col in enumerate(df_columns):
                        if normalized_possible in df_col or df_col in normalized_possible:
                            original_column = df.columns[i]
                            column_map[field] = original_column
                            matched_column = original_column
                            break
                    if matched_column:
                        break

        return column_map

    def parse_date(self, date_value: Any) -> str:
        """Parse date from various formats to YYYY-MM-DD"""
        if pd.isna(date_value) or date_value == "":
            return datetime.now().strftime('%Y-%m-%d')

        # If already a datetime object
        if isinstance(date_value, datetime):
            return date_value.strftime('%Y-%m-%d')

        # If it's a pandas Timestamp
        if hasattr(date_value, 'strftime'):
            return date_value.strftime('%Y-%m-%d')

        # Convert to string for parsing
        date_str = str(date_value).strip()

        # Try various date formats
        date_formats = [
            '%Y-%m-%d',  # 2025-07-28
            '%m/%d/%Y',  # 07/28/2025
            '%d/%m/%Y',  # 28/07/2025
            '%m-%d-%Y',  # 07-28-2025
            '%d-%m-%Y',  # 28-07-2025
            '%Y/%m/%d',  # 2025/07/28
            '%m/%d/%y',  # 07/28/25
            '%d/%m/%y',  # 28/07/25
            '%B %d, %Y', # July 28, 2025
            '%b %d, %Y', # Jul 28, 2025
            '%d %B %Y',  # 28 July 2025
            '%d %b %Y',  # 28 Jul 2025
        ]

        for fmt in date_formats:
            try:
                parsed_date = datetime.strptime(date_str, fmt)
                return parsed_date.strftime('%Y-%m-%d')
            except ValueError:
                continue

        # If no format works, try pandas to_datetime as last resort
        try:
            parsed_date = pd.to_datetime(date_str)
            return parsed_date.strftime('%Y-%m-%d')
        except:
            # Return current date as fallback
            return datetime.now().strftime('%Y-%m-%d')

    def parse_time(self, time_value: Any) -> int:
        """Parse estimated time to minutes"""
        if pd.isna(time_value) or time_value == "":
            return 30  # Default 30 minutes

        # If it's already a number
        try:
            return int(float(time_value))
        except:
            pass

        # If it's a string, try to extract numbers
        time_str = str(time_value).strip().lower()

        # Extract numbers from string
        numbers = re.findall(r'\d+', time_str)
        if numbers:
            time_num = int(numbers[0])

            # Check if it mentions hours
            if 'hour' in time_str or 'hr' in time_str:
                return time_num * 60  # Convert hours to minutes
            else:
                return time_num  # Assume minutes

        return 30  # Default fallback

    def validate_classification(self, classification: str) -> str:
        """Validate and normalize classification"""
        if pd.isna(classification) or classification == "":
            return 'Planning'  # Default classification

        classification_str = str(classification).strip()

        # Check for exact match (case insensitive)
        for valid_classification in self.config.CLASSIFICATIONS:
            if classification_str.lower() == valid_classification.lower():
                return valid_classification

        # Check for partial matches
        for valid_classification in self.config.CLASSIFICATIONS:
            if classification_str.lower() in valid_classification.lower() or \
               valid_classification.lower() in classification_str.lower():
                return valid_classification

        # Map common variations
        classification_variations = {
            'plan': 'Planning',
            'planning': 'Planning',
            'offline': 'Offline Processing',
            'processing': 'Offline Processing',
            'execute': 'Execution',
            'execution': 'Execution',
            'business': 'Business Support Activities',
            'support': 'Business Support Activities',
            'operational': 'Operational Project Involvement',
            'project': 'Operational Project Involvement'
        }

        for keyword, classification in classification_variations.items():
            if keyword in classification_str.lower():
                return classification

        return 'Planning'  # Default fallback

    def clean_text(self, text_value: Any) -> str:
        """Clean and normalize text values"""
        if pd.isna(text_value) or text_value == "":
            return ""

        # Convert to string and clean
        text = str(text_value).strip()

        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)

        # Limit length for titles and descriptions
        return text

    def process_dataframe(self, df: pd.DataFrame, column_map: Dict[str, str]) -> List[Dict]:
        """Process DataFrame and convert to task format"""
        tasks = []

        for row_num, (index, row) in enumerate(df.iterrows()):
            try:
                # Extract and process each field
                task = {}

                # Date processing
                if 'date' in column_map:
                    task['date'] = self.parse_date(row[column_map['date']])
                else:
                    task['date'] = datetime.now().strftime('%Y-%m-%d')

                # Title processing
                if 'title' in column_map:
                    task['title'] = self.clean_text(row[column_map['title']])[:200]  # Limit length
                    if not task['title']:
                        task['title'] = f"Imported Task {row_num + 1}"
                else:
                    task['title'] = f"Imported Task {row_num + 1}"

                # Classification processing
                if 'classification' in column_map:
                    classification_val = row[column_map['classification']]
                    task['classification'] = self.validate_classification(str(classification_val))
                else:
                    task['classification'] = 'Planning'

                # Description processing
                if 'description' in column_map:
                    task['description'] = self.clean_text(row[column_map['description']])[:1000]  # Limit length
                else:
                    task['description'] = ""

                # Time processing
                if 'est_time' in column_map:
                    task['est_time'] = self.parse_time(row[column_map['est_time']])
                else:
                    task['est_time'] = 30

                # Team member - always use current user
                task['team_member'] = self.username

                # Category - auto-map from classification
                task['category'] = self.config.CLASSIFICATION_MAPPING.get(
                    task['classification'], 'Other'
                )

                # Skip empty tasks
                if not task['title'] or task['title'].strip() == "":
                    continue

                tasks.append(task)

            except Exception as e:
                print(f"Warning: Skipping row {row_num + 1} due to error: {e}")
                continue

        return tasks

    def import_file(self, file_path: str, sheet_name: Optional[str] = None) -> Dict[str, Any]:
        """Main import function"""
        try:
            # Load file
            df = self.load_file(file_path, sheet_name)

            if df.empty:
                return {
                    'success': False,
                    'error': 'File is empty or contains no data',
                    'tasks': [],
                    'column_mapping': {}
                }

            # Map columns
            column_map = self.map_columns(df)

            # Check if we found essential columns
            if 'title' not in column_map and len(column_map) == 0:
                return {
                    'success': False,
                    'error': 'Could not identify any recognizable columns. Please check column headers.',
                    'tasks': [],
                    'column_mapping': column_map,
                    'available_columns': list(df.columns)
                }

            # Process data
            tasks = self.process_dataframe(df, column_map)

            if not tasks:
                return {
                    'success': False,
                    'error': 'No valid tasks found in file',
                    'tasks': [],
                    'column_mapping': column_map,
                    'available_columns': list(df.columns)
                }

            return {
                'success': True,
                'tasks': tasks,
                'column_mapping': column_map,
                'rows_processed': len(df),
                'tasks_imported': len(tasks),
                'available_columns': list(df.columns)
            }

        except Exception as e:
            return {
                'success': False,
                'error': f"Import failed: {str(e)}",
                'tasks': [],
                'column_mapping': {}
            }

    def save_imported_tasks(self, tasks: List[Dict]) -> Dict[str, Any]:
        """Save imported tasks to the user's data file"""
        try:
            # Load existing tasks
            data_file = os.path.join(self.config.DATA_DIR, f'tasks_{self.username}.json')

            if os.path.exists(data_file):
                with open(data_file, 'r', encoding='utf-8') as f:
                    existing_tasks = json.load(f)
            else:
                existing_tasks = []

            # Get next ID
            next_id = 1
            if existing_tasks:
                next_id = max(task.get('id', 0) for task in existing_tasks) + 1

            # Assign IDs to imported tasks
            for task in tasks:
                task['id'] = next_id
                next_id += 1

            # Combine and save
            all_tasks = existing_tasks + tasks

            # Ensure data directory exists
            os.makedirs(self.config.DATA_DIR, exist_ok=True)

            # Save to file
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(all_tasks, f, indent=2, ensure_ascii=False)

            return {
                'success': True,
                'tasks_added': len(tasks),
                'total_tasks': len(all_tasks)
            }

        except Exception as e:
            return {
                'success': False,
                'error': f"Failed to save tasks: {str(e)}"
            }


def test_import_utility():
    """Test function for import utility"""
    importer = ImportUtility()

    # Create sample data for testing
    test_data = {
        'Date': ['2025-07-28', '2025-07-29', '2025-07-30'],
        'Team Member': ['John Doe', 'Jane Smith', 'Bob Johnson'],
        'Task Title': ['Daily Stand-up', 'Code Review', 'Bug Fix'],
        'Classification': ['Business Support Activities', 'Execution', 'Planning'],
        'Actions Taken / Description': ['Attended daily standup meeting', 'Reviewed code changes', 'Fixed critical bug'],
        'Estimated Time (minute)': [15, 60, 120],
        'Category': ['Business Support Activities', 'Adhoc', 'Adhoc']
    }

    # Create test CSV
    import pandas as pd
    df = pd.DataFrame(test_data)
    test_file = 'test_import.csv'
    df.to_csv(test_file, index=False)

    # Test import
    result = importer.import_file(test_file)
    print("Import Result:", json.dumps(result, indent=2))

    # Clean up
    if os.path.exists(test_file):
        os.remove(test_file)


if __name__ == "__main__":
    test_import_utility()
