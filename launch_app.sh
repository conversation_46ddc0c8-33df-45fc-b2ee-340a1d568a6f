#!/bin/bash

# AdhocLog - Universal Launcher
# This script provides both GUI and command-line options

# Change to the script's directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "========================================"
echo "  [LAUNCH] AdhocLog"
echo "  Universal Launcher"
echo "========================================"

# Function to detect SharePoint/OneDrive environment
detect_sharepoint_environment() {
    local current_path="$PWD"
    local sharepoint_detected=false
    local sharepoint_type=""

    # Check for various SharePoint/OneDrive path patterns
    if [[ "$current_path" == *"OneDrive"* ]]; then
        sharepoint_detected=true
        if [[ "$current_path" == *"OneDrive - "* ]]; then
            sharepoint_type="OneDrive Business"
        else
            sharepoint_type="OneDrive Personal"
        fi
    elif [[ "$current_path" == *"SharePoint"* ]]; then
        sharepoint_detected=true
        sharepoint_type="SharePoint Site"
    elif [[ "$current_path" == *"Microsoft Teams"* ]]; then
        sharepoint_detected=true
        sharepoint_type="Teams Document Library"
    elif [[ "$current_path" == *"/Sites/"* ]] || [[ "$current_path" == *"/teams/"* ]]; then
        sharepoint_detected=true
        sharepoint_type="SharePoint/Teams"
    fi

    if [ "$sharepoint_detected" = true ]; then
        echo "🌐 SharePoint environment detected: $sharepoint_type"
        echo "📁 Path: $current_path"
        return 0
    else
        return 1
    fi
}

# Function to clean Python cache directories
cleanup_python_cache() {
    local cache_cleaned=false

    echo "🧹 Cleaning Python cache directories..."

    # Find and remove __pycache__ directories
    if find . -name "__pycache__" -type d 2>/dev/null | grep -q .; then
        find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || {
            # Fallback: try to remove individual directories
            find . -name "__pycache__" -type d 2>/dev/null | while read -r cache_dir; do
                if [ -d "$cache_dir" ]; then
                    rm -rf "$cache_dir" 2>/dev/null && echo "  ✅ Removed: $cache_dir" || echo "  ⚠️ Could not remove: $cache_dir"
                    cache_cleaned=true
                fi
            done
        }
        cache_cleaned=true
        echo "✅ Python cache directories cleaned"
    else
        echo "✅ No Python cache directories found"
    fi

    # Find and remove .pyc files
    if find . -name "*.pyc" -type f 2>/dev/null | grep -q .; then
        find . -name "*.pyc" -type f -delete 2>/dev/null || {
            echo "⚠️ Some .pyc files could not be removed (permission issue)"
        }
        echo "✅ Python .pyc files cleaned"
    fi

    return 0
}

# Function to setup cache isolation
setup_cache_isolation() {
    local username=$(whoami)
    local temp_cache_dir

    # Set environment variables to prevent cache creation in shared directories
    export PYTHONDONTWRITEBYTECODE=1
    echo "🔒 Cache isolation enabled (PYTHONDONTWRITEBYTECODE=1)"

    # Create user-specific temporary cache directory
    if [[ "$OSTYPE" == darwin* ]]; then
        temp_cache_dir="/tmp/adhoclog_cache_$username"
    else
        temp_cache_dir="/tmp/adhoclog_cache_$username"
    fi

    mkdir -p "$temp_cache_dir" 2>/dev/null
    if [ -d "$temp_cache_dir" ]; then
        export PYTHONPYCACHEPREFIX="$temp_cache_dir"
        echo "📁 User cache directory: $temp_cache_dir"
    else
        echo "⚠️ Could not create user cache directory, cache disabled"
    fi
}

# Function to detect platform and Python architecture
detect_platform_info() {
    PLATFORM_OS=$(uname -s)
    PLATFORM_ARCH=$(uname -m)
    PYTHON_ARCH=""

    if [ -n "$PYTHON_CMD" ]; then
        PYTHON_ARCH=$($PYTHON_CMD -c "import platform; print(platform.machine())" 2>/dev/null || echo "unknown")
    fi

    echo "🔍 Platform: $PLATFORM_OS $PLATFORM_ARCH"
    if [ -n "$PYTHON_ARCH" ] && [ "$PYTHON_ARCH" != "unknown" ]; then
        echo "🐍 Python architecture: $PYTHON_ARCH"
    fi
}

# Function to create virtual environment in user's home directory
setup_user_virtual_environment() {
    local username=$(whoami)
    local home_dir="$HOME"
    local venv_base_dir="$home_dir/.venvs"
    local venv_dir="$venv_base_dir/adhoc-log-app"
    local fallback_to_system=false

    echo "[SETUP] Setting up virtual environment in home directory..."
    echo "[INFO] User: $username"
    echo "[INFO] Home directory: $home_dir"
    echo "[INFO] Virtual environment: $venv_dir"
    echo "[INFO] Working directory: $PWD"

    # Create .venvs directory in home if it doesn't exist
    if ! mkdir -p "$venv_base_dir" 2>/dev/null; then
        echo "[ERROR] Cannot create .venvs directory in home (permission issue)"
        echo "[INFO] Falling back to system Python"
        return 1
    fi

    # Check if virtual environment already exists and is valid
    if [ -d "$venv_dir" ]; then
        echo "[INFO] Checking existing virtual environment..."
        local venv_python="$venv_dir/bin/python"

        if [ -f "$venv_python" ] && "$venv_python" --version >/dev/null 2>&1; then
            # Check if it's compatible with current Python and platform
            local venv_version=$("$venv_python" --version 2>/dev/null | cut -d' ' -f2)
            local venv_platform=$("$venv_python" -c "import platform; print(platform.machine())" 2>/dev/null || echo "unknown")

            if [ "$venv_platform" = "$PYTHON_ARCH" ] || [ "$venv_platform" = "unknown" ]; then
                echo "[SUCCESS] Existing virtual environment is compatible"
                echo "   Python: $venv_version"
                echo "   Platform: $venv_platform"

                # Set environment variables to use this venv - use absolute paths
                export VIRTUAL_ENV="$(cd "$venv_dir" && pwd)"
                export PATH="$VIRTUAL_ENV/bin:$PATH"
                PYTHON_CMD="$VIRTUAL_ENV/bin/python"

                # Verify requirements are installed by checking for Flask
                echo "[INFO] Checking installed packages..."
                if "$PYTHON_CMD" -c "import flask; print('Flask version:', flask.__version__)" >/dev/null 2>&1; then
                    echo "[SUCCESS] Required packages already installed"
                    return 0
                else
                    echo "[INSTALL] Need to install/update packages"
                    if install_venv_requirements "$venv_dir"; then
                        return 0
                    else
                        echo "[WARNING] Package installation failed, but virtual environment is usable"
                        echo "[INFO] Some features may not work properly"
                        return 0  # Continue with partial setup
                    fi
                fi
            else
                echo "[WARNING] Platform mismatch detected"
                echo "   Current: $PYTHON_ARCH"
                echo "   Virtual env: $venv_platform"
                echo "[SETUP] Recreating virtual environment for platform compatibility..."
                if ! rm -rf "$venv_dir" 2>/dev/null; then
                    echo "[ERROR] Cannot remove old virtual environment (permission issue)"
                    echo "[INFO] Falling back to system Python"
                    return 1
                fi
            fi
        else
            echo "[WARNING] Virtual environment exists but is broken"
            echo "[SETUP] Removing broken virtual environment..."
            if ! rm -rf "$venv_dir" 2>/dev/null; then
                echo "[ERROR] Cannot remove broken virtual environment (permission issue)"
                echo "[INFO] Falling back to system Python"
                return 1
            fi
        fi
    fi

    # Create new virtual environment
    echo "[INSTALL] Creating new virtual environment..."
    echo "[INFO] Command: $PYTHON_CMD -m venv '$venv_dir'"

    # Capture detailed error output for debugging
    local venv_error_log=$(mktemp)
    if "$PYTHON_CMD" -m venv "$venv_dir" 2>"$venv_error_log"; then
        echo "[SUCCESS] Virtual environment created successfully"

        # Verify the virtual environment was created properly
        local venv_python="$venv_dir/bin/python"
        echo "[INFO] Checking virtual environment Python at: $venv_python"

        if [ ! -f "$venv_python" ]; then
            echo "[ERROR] Virtual environment Python executable not found"
            echo "[INFO] Expected: $venv_python"
            echo "[INFO] Virtual environment directory contents:"
            if [ -d "$venv_dir" ]; then
                ls -la "$venv_dir/" 2>/dev/null | head -10 || echo "Directory not accessible"
                if [ -d "$venv_dir/bin" ]; then
                    echo "[INFO] bin/ directory contents:"
                    ls -la "$venv_dir/bin/" 2>/dev/null | head -5 || echo "bin directory not accessible"
                else
                    echo "[ERROR] bin/ directory missing"
                fi
            else
                echo "[ERROR] Virtual environment directory not created"
            fi
            rm -rf "$venv_dir" 2>/dev/null
            rm -f "$venv_error_log"
            echo "[INFO] Falling back to system Python"
            return 1
        fi

        echo "[SUCCESS] Virtual environment Python executable found"
        echo "[INFO] Testing virtual environment Python functionality..."

        # Test with proper quoting for paths with spaces
        if ! "$venv_python" --version >/dev/null 2>&1; then
            echo "❌ Virtual environment Python is not functional"
            echo "🔍 Debugging virtual environment Python:"
            echo "   Path: $venv_python"
            echo "   Absolute path: $(realpath "$venv_python" 2>/dev/null || echo "Cannot resolve path")"
            echo "   File exists: $([ -f "$venv_python" ] && echo "Yes" || echo "No")"
            echo "   File permissions: $(ls -l "$venv_python" 2>/dev/null || echo "Cannot check permissions")"
            echo "   File type: $(file "$venv_python" 2>/dev/null || echo "Cannot check file type")"

            # Try to get more detailed error
            echo "🧪 Attempting to run virtual environment Python with error output:"
            "$venv_python" --version 2>&1 | head -3 || echo "Failed to execute"

            # Check if it's a symlink issue
            if [ -L "$venv_python" ]; then
                echo "🔗 Python executable is a symlink"
                echo "   Target: $(readlink "$venv_python" 2>/dev/null || echo "Cannot read symlink")"
                echo "   Target exists: $([ -f "$(readlink "$venv_python" 2>/dev/null)" ] && echo "Yes" || echo "No")"
            fi

            rm -rf "$venv_dir" 2>/dev/null
            rm -f "$venv_error_log"
            echo "💡 Falling back to system Python"
            return 1
        fi

        echo "✅ Virtual environment Python is functional"

        # Set environment variables - use absolute paths for better reliability
        export VIRTUAL_ENV="$(cd "$venv_dir" && pwd)"
        export PATH="$VIRTUAL_ENV/bin:$PATH"
        PYTHON_CMD="$VIRTUAL_ENV/bin/python"

        echo "✅ Virtual environment verified and activated"
        echo "🐍 Using: $PYTHON_CMD"

        # Install requirements
        if install_venv_requirements "$venv_dir"; then
            echo "✅ Virtual environment setup complete"
            rm -f "$venv_error_log"
            return 0
        else
            echo "⚠️ Package installation failed in virtual environment"
            echo "💡 Virtual environment created but may have missing dependencies"
            echo "💡 Application will attempt to run with available packages"
            rm -f "$venv_error_log"
            return 0  # Continue with partial setup
        fi
    else
        echo "❌ Failed to create virtual environment"

        # Show detailed error information
        if [ -s "$venv_error_log" ]; then
            echo "💡 Virtual environment creation error details:"
            while IFS= read -r line; do
                echo "   $line"
            done < "$venv_error_log"
        fi
        rm -f "$venv_error_log"

        # Check common issues
        if ! "$PYTHON_CMD" -c "import venv" >/dev/null 2>&1; then
            echo "💡 Python venv module not available - try installing python3-venv"
        elif ! touch "venvs/test_write" 2>/dev/null; then
            echo "💡 No write permission to venvs directory"
            echo "💡 SharePoint paths may have special permission requirements"
        else
            rm -f "venvs/test_write" 2>/dev/null
            echo "💡 Virtual environment creation failed for unknown reason"
            echo "💡 This may be due to SharePoint path length or character restrictions"
            echo "💡 Working directory: $PWD"
            echo "💡 Target venv directory: $venv_dir"
            echo "💡 Full target path: $(realpath "$venv_dir" 2>/dev/null || echo "Cannot resolve")"
        fi

        echo "💡 Falling back to system Python"
        return 1
    fi
}

# Function to install requirements in virtual environment
install_venv_requirements() {
    local venv_dir="$1"
    local venv_python="$venv_dir/bin/python"
    local venv_pip="$venv_dir/bin/pip"

    echo "[INSTALL] Installing requirements in virtual environment..."
    echo "[INFO] Testing virtual environment Python: $venv_python"

    # Verify virtual environment is working
    if ! "$venv_python" --version >/dev/null 2>&1; then
        echo "[ERROR] Virtual environment Python is not working"
        echo "[INFO] Debug info:"
        echo "   Path: $venv_python"
        echo "   Absolute path: $(realpath "$venv_python" 2>/dev/null || echo "Cannot resolve")"
        echo "   File exists: $([ -f "$venv_python" ] && echo "Yes" || echo "No")"
        echo "   Executable: $([ -x "$venv_python" ] && echo "Yes" || echo "No")"
        return 1
    fi

    # Upgrade pip first with multiple fallback strategies
    echo "[INSTALL] Upgrading pip..."
    if "$venv_python" -m pip install --upgrade pip >/dev/null 2>&1; then
        echo "[SUCCESS] pip upgraded successfully"
    elif "$venv_python" -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org --upgrade pip >/dev/null 2>&1; then
        echo "[SUCCESS] pip upgraded with trusted hosts"
    elif "$venv_python" -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org --index-url https://pypi.org/simple/ --upgrade pip >/dev/null 2>&1; then
        echo "[SUCCESS] pip upgraded with explicit index"
    else
        echo "[WARNING] pip upgrade failed, continuing with existing version"
    fi

    # Check if requirements.txt exists
    if [ ! -f "requirements.txt" ]; then
        echo "⚠️ requirements.txt not found"
        return 1
    fi

    # Install requirements with multiple fallback strategies
    echo "📋 Installing packages from requirements.txt..."

    # Strategy 1: Standard installation
    if "$venv_python" -m pip install -r requirements.txt >/dev/null 2>&1; then
        echo "✅ Requirements installed successfully"
        return 0
    fi

    # Strategy 2: With trusted hosts
    echo "⚠️ Standard installation failed, trying with trusted hosts..."
    if "$venv_python" -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org -r requirements.txt >/dev/null 2>&1; then
        echo "✅ Requirements installed with trusted hosts"
        return 0
    fi

    # Strategy 3: With explicit index and trusted hosts
    echo "⚠️ Trusted hosts failed, trying with explicit index..."
    if "$venv_python" -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org --index-url https://pypi.org/simple/ -r requirements.txt >/dev/null 2>&1; then
        echo "✅ Requirements installed with explicit index"
        return 0
    fi

    # Strategy 4: Individual package installation with error capture
    echo "⚠️ Batch installation failed, trying individual packages..."
    local success_count=0
    local total_count=0
    local failed_packages=()

    while IFS= read -r line; do
        # Skip empty lines and comments
        if [[ -z "$line" ]] || [[ "$line" =~ ^[[:space:]]*# ]]; then
            continue
        fi

        # Clean the line (remove comments and whitespace)
        package=$(echo "$line" | sed 's/#.*$//' | xargs)
        if [[ -z "$package" ]]; then
            continue
        fi

        total_count=$((total_count + 1))
        echo "📦 Installing $package..."

        if "$venv_python" -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org --index-url https://pypi.org/simple/ "$package" >/dev/null 2>&1; then
            echo "   ✅ $package installed successfully"
            success_count=$((success_count + 1))
        else
            echo "   ❌ Failed to install $package"
            failed_packages+=("$package")
        fi
    done < requirements.txt

    echo "📊 Installation summary: $success_count/$total_count packages installed"

    if [ $success_count -eq $total_count ]; then
        echo "✅ All requirements installed successfully"
        return 0
    elif [ $success_count -gt 0 ]; then
        echo "⚠️ Partial installation successful, some packages failed:"
        for pkg in "${failed_packages[@]}"; do
            echo "   - $pkg"
        done
        echo "💡 The application may still work with partial dependencies"
        return 0  # Return success for partial installation
    else
        echo "❌ Failed to install any requirements"
        echo "💡 This may be due to network restrictions or proxy settings"
        echo "💡 Try running the application anyway - some features may work"
        return 1
    fi
}

# Function to cleanup old project-based virtual environments (legacy cleanup)
cleanup_old_venvs() {
    local username=$(whoami)
    local cleaned=0

    # Clean up old project-based virtual environments
    if [ -d "venvs" ]; then
        echo "[CLEANUP] Cleaning up old project-based virtual environments..."

        for venv_path in venvs/user_${username}_*; do
            if [ -d "$venv_path" ]; then
                local venv_name=$(basename "$venv_path")
                echo "[CLEANUP] Removing old project-based virtual environment: $venv_name"
                rm -rf "$venv_path" 2>/dev/null && cleaned=$((cleaned + 1))
            fi
        done

        # Remove the entire venvs directory if it's empty
        if [ -d "venvs" ] && [ -z "$(ls -A venvs 2>/dev/null)" ]; then
            echo "[CLEANUP] Removing empty venvs directory"
            rmdir "venvs" 2>/dev/null
        fi

        if [ $cleaned -gt 0 ]; then
            echo "[SUCCESS] Cleaned up $cleaned old project-based virtual environment(s)"
        else
            echo "[INFO] No old project-based virtual environments to clean"
        fi
    fi
}

# Function to create user-specific data directories
setup_user_data_directories() {
    local username=$(whoami)
    local user_data_dir="data/user_$username"

    echo "👤 Setting up user-specific data directories for: $username"

    # Create user data directory
    if mkdir -p "$user_data_dir" 2>/dev/null; then
        echo "✅ User data directory created: $user_data_dir"

        # Set environment variable for data manager to use
        export ADHOCLOG_USER_DATA_DIR="$user_data_dir"
        export ADHOCLOG_SHAREPOINT_MODE=1

        return 0
    else
        echo "⚠️ Could not create user data directory: $user_data_dir"
        echo "💡 Falling back to legacy data structure"
        return 1
    fi
}

# Check for SharePoint environment and configure accordingly
echo ""
echo "🔍 Checking deployment environment..."

SHAREPOINT_MODE=false
if detect_sharepoint_environment; then
    SHAREPOINT_MODE=true
    echo "🚀 Configuring for SharePoint deployment..."

    # Clean cache directories
    cleanup_python_cache

    # Setup cache isolation
    setup_cache_isolation

    # Setup user-specific directories
    if setup_user_data_directories; then
        echo "✅ SharePoint user data configuration complete"
    else
        echo "⚠️ SharePoint user data configuration partially completed"
    fi
else
    echo "💻 Local development environment detected"
    echo "✅ Using standard configuration"
fi

echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to compare version numbers
version_compare() {
    # Returns 0 if $1 >= $2, 1 if $1 < $2
    local v1=$1
    local v2=$2

    # Extract major and minor version numbers
    local v1_major=$(echo $v1 | cut -d. -f1)
    local v1_minor=$(echo $v1 | cut -d. -f2)
    local v2_major=$(echo $v2 | cut -d. -f1)
    local v2_minor=$(echo $v2 | cut -d. -f2)

    # Compare major version
    if [ "$v1_major" -gt "$v2_major" ]; then
        return 0
    elif [ "$v1_major" -lt "$v2_major" ]; then
        return 1
    else
        # Major versions are equal, compare minor versions
        if [ "$v1_minor" -ge "$v2_minor" ]; then
            return 0
        else
            return 1
        fi
    fi
}

# Function to detect Python installations
detect_python() {
    local python_candidates=()
    local python_cmd=""
    local python_version=""
    local best_python=""
    local best_version=""

    # Common Python command names to try
    local python_names=("python3" "python" "python3.11" "python3.10" "python3.9" "python3.8" "python3.7")

    # Add Homebrew paths for macOS
    if [[ "$OSTYPE" == darwin* ]]; then
        # Intel Macs
        if [ -d "/usr/local/bin" ]; then
            for name in "${python_names[@]}"; do
                if [ -x "/usr/local/bin/$name" ]; then
                    python_candidates+=("/usr/local/bin/$name")
                fi
            done
        fi

        # Apple Silicon Macs
        if [ -d "/opt/homebrew/bin" ]; then
            for name in "${python_names[@]}"; do
                if [ -x "/opt/homebrew/bin/$name" ]; then
                    python_candidates+=("/opt/homebrew/bin/$name")
                fi
            done
        fi

        # pyenv installations
        if [ -d "$HOME/.pyenv/versions" ]; then
            for version_dir in "$HOME/.pyenv/versions"/*; do
                if [ -x "$version_dir/bin/python3" ]; then
                    python_candidates+=("$version_dir/bin/python3")
                fi
            done
        fi
    fi

    # Add system PATH candidates
    for name in "${python_names[@]}"; do
        if command_exists "$name"; then
            python_candidates+=("$name")
        fi
    done

    # Evaluate each candidate
    for candidate in "${python_candidates[@]}"; do
        if [ -x "$candidate" ] || command_exists "$candidate"; then
            version=$($candidate --version 2>/dev/null | cut -d' ' -f2)
            if [[ $version == 3.* ]]; then
                # Check if this version is better than our current best
                if [ -z "$best_python" ] || version_compare "$version" "$best_version"; then
                    # Test if tkinter is available (important for GUI)
                    if $candidate -c "import tkinter" 2>/dev/null; then
                        best_python="$candidate"
                        best_version="$version"
                        echo "✅ Found Python with tkinter: $candidate ($version)"
                    elif [ -z "$best_python" ]; then
                        # Use as fallback if no tkinter-enabled Python found yet
                        best_python="$candidate"
                        best_version="$version"
                        echo "⚠️ Found Python without tkinter: $candidate ($version)"
                    fi
                fi
            fi
        fi
    done

    if [ -n "$best_python" ]; then
        PYTHON_CMD="$best_python"
        PYTHON_VERSION="$best_version"
        SYSTEM_PYTHON_CMD="$best_python"  # Save system Python for GUI testing
        return 0
    else
        return 1
    fi
}

# Check for Python
PYTHON_CMD=""
PYTHON_VERSION=""

echo "🔍 Detecting Python installations..."
if ! detect_python; then
    echo "❌ No suitable Python 3.7+ installation found!"
else
    echo "✅ Selected Python: $PYTHON_CMD ($PYTHON_VERSION)"

    # Check architecture on macOS
    if [[ "$OSTYPE" == darwin* ]]; then
        arch_info=$(uname -m)
        if [[ "$arch_info" == "arm64" ]]; then
            echo "🍎 Detected Apple Silicon Mac (ARM64)"
        else
            echo "🍎 Detected Intel Mac (x86_64)"
        fi

        # Check if we're using Homebrew Python and warn about potential issues
        if [[ "$PYTHON_CMD" == *"/opt/homebrew/"* ]]; then
            echo "🍺 Using Homebrew Python (Apple Silicon optimized)"
        elif [[ "$PYTHON_CMD" == *"/usr/local/"* ]]; then
            echo "🍺 Using Homebrew Python (Intel/Rosetta)"
        fi
    fi
fi

# Function to install tkinter on macOS
install_macos_tkinter() {
    echo "🔧 Installing tkinter for macOS..."

    if command_exists brew; then
        echo "📦 Installing python-tk via Homebrew..."
        brew install python-tk || {
            echo "⚠️ Homebrew python-tk install failed, trying alternative..."
            brew install python@3.11
        }
    else
        echo "❌ Homebrew not found. Please install it first:"
        echo "   /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        return 1
    fi

    # Test if tkinter works now
    if $PYTHON_CMD -c "import tkinter; print('tkinter available')" 2>/dev/null; then
        echo "✅ tkinter installation successful!"
        return 0
    else
        echo "⚠️ tkinter still not available. You may need to:"
        echo "   1. Use the official Python installer from python.org"
        echo "   2. Or try: brew reinstall python@3.11"
        return 1
    fi
}

# Function to configure pip for corporate/VPN environments
configure_pip() {
    echo "🔧 Configuring pip for corporate/VPN environments..."
    mkdir -p ~/.pip
    cat > ~/.pip/pip.conf << EOF
[global]
trusted-host = pypi.org
               pypi.python.org
               files.pythonhosted.org
timeout = 60
retries = 3
EOF

    # Also create pip.ini for Windows compatibility
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" || "$OSTYPE" == "mingw"* ]]; then
        mkdir -p ~/pip
        cp ~/.pip/pip.conf ~/pip/pip.ini
    fi
}

# Function to attempt Python installation
install_python() {
    echo "🔄 Attempting to install Python..."

    case "$OSTYPE" in
        darwin*)
            # macOS
            if command_exists brew; then
                echo "📦 Installing Python via Homebrew..."
                brew install python@3.11

                # Also install tkinter
                echo "📦 Installing tkinter support..."
                brew install python-tk

                configure_pip
            elif command_exists port; then
                echo "📦 Installing Python via MacPorts..."
                sudo port install python311 +tkinter
                configure_pip
            else
                echo "❌ Please install Homebrew first:"
                echo "   /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
                echo "   Then run this script again."
                return 1
            fi
            ;;
        linux*)
            # Linux
            if command_exists apt-get; then
                echo "📦 Installing Python via apt..."
                sudo apt-get update && sudo apt-get install -y python3 python3-pip python3-venv python3-tk
            elif command_exists yum; then
                echo "📦 Installing Python via yum..."
                sudo yum install -y python3 python3-pip python3-tkinter
            elif command_exists dnf; then
                echo "📦 Installing Python via dnf..."
                sudo dnf install -y python3 python3-pip python3-tkinter
            elif command_exists pacman; then
                echo "📦 Installing Python via pacman..."
                sudo pacman -S python python-pip tk
            else
                echo "❌ Unsupported Linux distribution. Please install Python 3.7+ manually."
                return 1
            fi

            configure_pip
            ;;
        msys*|cygwin*|mingw*)
            # Windows (Git Bash/MSYS/Cygwin)
            echo "❌ Automatic Python installation not supported on Windows."
            echo "📥 Please download and install Python from:"
            echo "   https://www.python.org/downloads/windows/"
            echo "   Make sure to check 'Add Python to PATH' during installation."
            configure_pip
            return 1
            ;;
        *)
            echo "❌ Unsupported operating system: $OSTYPE"
            return 1
            ;;
    esac

    # Re-check for Python after installation
    if command_exists python3; then
        PYTHON_CMD="python3"
        PYTHON_VERSION=$(python3 --version 2>/dev/null | cut -d' ' -f2)
        echo "✅ Python installation successful!"
        return 0
    elif command_exists python; then
        PYTHON_VERSION=$(python --version 2>/dev/null | cut -d' ' -f2)
        if [[ $PYTHON_VERSION == 3.* ]]; then
            PYTHON_CMD="python"
            echo "✅ Python installation successful!"
            return 0
        fi
    fi

    return 1
}

if [ -z "$PYTHON_CMD" ]; then
    echo "❌ Python 3.7+ is required but not found!"
    echo
    read -p "Would you like to attempt automatic installation? (y/n): " install_choice

    if [[ $install_choice =~ ^[Yy]$ ]]; then
        if install_python; then
            echo "✅ Python installed successfully!"
        else
            echo
            echo "📥 Manual installation required. Please install Python 3.7 or higher:"
            echo "  • macOS: brew install python3"
            echo "  • Ubuntu/Debian: sudo apt install python3 python3-pip python3-venv"
            echo "  • CentOS/RHEL: sudo yum install python3 python3-pip"
            echo "  • Fedora: sudo dnf install python3 python3-pip"
            echo "  • Windows: Download from https://python.org/downloads"
            echo "            (Make sure to check 'Add Python to PATH')"
            echo
            read -p "Press Enter to exit..."
            exit 1
        fi
    else
        echo
        echo "📥 Please install Python 3.7 or higher manually:"
        echo "  • macOS: brew install python3"
        echo "  • Ubuntu/Debian: sudo apt install python3 python3-pip python3-venv"
        echo "  • CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "  • Fedora: sudo dnf install python3 python3-pip"
        echo "  • Windows: Download from https://python.org/downloads"
        echo "            (Make sure to check 'Add Python to PATH')"
        echo
        read -p "Press Enter to exit..."
        exit 1
    fi
fi

# Verify Python version
if ! version_compare "$PYTHON_VERSION" "3.7"; then
    echo "❌ Python $PYTHON_VERSION found, but 3.7+ is required!"
    echo "📥 Please upgrade Python to version 3.7 or higher"
    read -p "Press Enter to exit..."
    exit 1
fi

echo "✅ Found Python: $($PYTHON_CMD --version)"

# Setup platform detection and virtual environment (for SharePoint deployments)
if [ "$SHAREPOINT_MODE" = true ]; then
    echo ""
    echo "🔧 Setting up SharePoint virtual environment..."

    # Detect platform information
    detect_platform_info

    # Setup user-specific virtual environment
    if setup_user_virtual_environment; then
        echo "✅ Virtual environment ready"
        # Cleanup old virtual environments from other platforms
        cleanup_old_venvs
    else
        echo "⚠️ Virtual environment setup failed, using system Python"
        echo "💡 This may cause conflicts in multi-user SharePoint environments"
        echo "💡 Attempting to install packages to user directory as fallback..."

        # Try to install critical packages to user directory
        echo "📦 Installing critical packages with --user flag..."

        # Test if we can install Flask to user directory
        if $PYTHON_CMD -m pip install --user flask >/dev/null 2>&1; then
            echo "✅ Flask installed to user directory"
        elif $PYTHON_CMD -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org --user flask >/dev/null 2>&1; then
            echo "✅ Flask installed to user directory (with trusted hosts)"
        elif $PYTHON_CMD -m pip install --user --break-system-packages flask >/dev/null 2>&1; then
            echo "✅ Flask installed to user directory (bypassing externally managed restriction)"
        else
            echo "⚠️ Could not install Flask - trying requirements.txt with fallback options..."

            # Try installing from requirements.txt with various fallback options
            if [ -f "requirements.txt" ]; then
                echo "📋 Attempting requirements.txt installation with fallback options..."

                # Try with --user and --break-system-packages for externally managed environments
                if $PYTHON_CMD -m pip install --user --break-system-packages -r requirements.txt >/dev/null 2>&1; then
                    echo "✅ Requirements installed with --break-system-packages"
                elif $PYTHON_CMD -m pip install --user --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org --break-system-packages -r requirements.txt >/dev/null 2>&1; then
                    echo "✅ Requirements installed with trusted hosts and --break-system-packages"
                else
                    echo "⚠️ Could not install requirements - application may not work properly"
                    echo "💡 You may need to manually install packages or contact IT support"
                fi
            fi
        fi

        # Try to verify existing installations
        if $PYTHON_CMD -c "import flask; print('Flask available:', flask.__version__)" >/dev/null 2>&1; then
            echo "✅ Flask is available in system Python"
        else
            echo "⚠️ Flask not available - application will likely fail to start"
            echo "💡 Manual package installation may be required"
        fi
    fi
fi

echo

# Function to test tkinter functionality
test_tkinter() {
    local python_cmd="$1"
    local test_result
    local temp_test_file

    # Create a temporary test file for better error capture
    temp_test_file=$(mktemp)

    # Try to import and create a test tkinter window
    $python_cmd << 'EOF' > "$temp_test_file" 2>&1
import sys
import os

try:
    # Set environment variables to avoid macOS tkinter warnings
    os.environ['TK_SILENCE_DEPRECATION'] = '1'

    import tkinter as tk

    # Try to create a test window
    root = tk.Tk()
    root.withdraw()  # Hide the window immediately

    # Test basic tkinter functionality
    label = tk.Label(root, text="Test")

    # Clean up
    root.destroy()

    print('tkinter_works')
    sys.exit(0)

except ImportError as e:
    print(f'tkinter_missing:{e}')
    sys.exit(1)

except Exception as e:
    print(f'tkinter_error:{e}')
    sys.exit(2)
EOF

    test_result=$(cat "$temp_test_file")
    local exit_code=$?
    rm -f "$temp_test_file"

    case "$exit_code" in
        0)
            if [[ "$test_result" == "tkinter_works" ]]; then
                return 0
            else
                return 3
            fi
            ;;
        1)
            echo "💡 tkinter import error: ${test_result#tkinter_missing:}"
            return 1
            ;;
        2)
            echo "💡 tkinter runtime error: ${test_result#tkinter_error:}"
            return 2
            ;;
        *)
            echo "💡 tkinter test failed with unknown error"
            return 3
            ;;
    esac
}

# Function to check if GUI is available
check_gui_available() {
    local gui_reason=""
    local test_python_cmd=""

    # For GUI testing, we need to use system Python as virtual environments
    # typically don't include tkinter
    if [ -n "$SYSTEM_PYTHON_CMD" ]; then
        test_python_cmd="$SYSTEM_PYTHON_CMD"
        echo "🔍 Testing tkinter with system Python: $test_python_cmd"
    else
        test_python_cmd="$PYTHON_CMD"
        echo "🔍 Testing tkinter with current Python: $test_python_cmd"
    fi

    # First, test if tkinter actually works
    echo "🔍 Testing tkinter availability..."
    if test_tkinter "$test_python_cmd"; then
        echo "✅ tkinter is working correctly"
        GUI_REASON="tkinter available and functional"
        return 0
    else
        local tkinter_status=$?
        case $tkinter_status in
            1)
                echo "❌ tkinter is not installed"
                GUI_REASON="tkinter not installed"
                ;;
            2)
                echo "⚠️ tkinter is installed but not working (display issue)"
                GUI_REASON="tkinter installed but display unavailable"
                ;;
            *)
                echo "❌ tkinter test failed"
                GUI_REASON="tkinter test failed"
                ;;
        esac
    fi

    # Check environment-specific conditions
    if [ -n "$DISPLAY" ]; then
        echo "🖥️ X11/Wayland display detected: $DISPLAY"
        if [[ "$GUI_REASON" == "tkinter installed but display unavailable" ]]; then
            echo "💡 Display available but tkinter can't use it"
            return 1
        fi
    fi

    # Check for macOS
    if [[ "$OSTYPE" == darwin* ]]; then
        if [ -n "$SSH_CLIENT" ] || [ -n "$SSH_TTY" ]; then
            echo "🔒 SSH session detected - GUI not available"
            GUI_REASON="SSH session (no GUI access)"
            return 1
        fi

        # Check if we're in a headless environment
        if [ -z "$TERM_PROGRAM" ] && [ -z "$DISPLAY" ] && [ "$TERM" = "dumb" ]; then
            echo "🤖 Headless environment detected"
            GUI_REASON="headless environment"
            return 1
        fi

        # On macOS, if tkinter failed, suggest solutions
        if [[ "$GUI_REASON" == "tkinter not installed" ]]; then
            echo "💡 On macOS, try: brew install python-tk"
            return 1
        fi
    fi

    # Check for Windows environments
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" || "$OSTYPE" == "mingw"* ]]; then
        if [[ "$GUI_REASON" == "tkinter not installed" ]]; then
            echo "💡 On Windows, reinstall Python with tkinter support"
            return 1
        fi
        # Assume GUI available on Windows if tkinter works
        return 0
    fi

    # For Linux, check more conditions
    if [[ "$OSTYPE" == linux* ]]; then
        if [ -z "$DISPLAY" ] && [ -z "$WAYLAND_DISPLAY" ]; then
            echo "🐧 Linux headless environment (no DISPLAY or WAYLAND_DISPLAY)"
            GUI_REASON="Linux headless environment"
            return 1
        fi

        if [[ "$GUI_REASON" == "tkinter not installed" ]]; then
            echo "💡 On Linux, try: sudo apt-get install python3-tk (Ubuntu/Debian)"
            echo "💡 Or: sudo yum install tkinter (CentOS/RHEL)"
            echo "💡 Or: sudo dnf install python3-tkinter (Fedora)"
            return 1
        fi
    fi

    # If we get here, GUI is not available
    return 1
}

# Check if GUI is available
echo ""
echo "🔍 Checking GUI availability..."
GUI_AVAILABLE=false
GUI_REASON=""

if check_gui_available; then
    GUI_AVAILABLE=true
    echo "✅ GUI is available!"
else
    echo "❌ GUI is not available"
    if [ -n "$GUI_REASON" ]; then
        echo "   Reason: $GUI_REASON"
    fi
fi

echo ""

# Show options
echo "Choose how to launch the application:"
echo
if [ "$GUI_AVAILABLE" = true ]; then
    echo "1. 🖥️  GUI Launcher (Recommended) - Easy graphical interface"
    echo "2. 🚀 Quick Start - Start app immediately"
    echo "3. 🔧 Advanced Options - Access utility scripts"
    echo "4. ❓ Help & Troubleshooting"
    echo
    read -p "Enter your choice (1-4): " choice
else
    echo "🖥️ GUI Launcher not available ($GUI_REASON)"
    echo "Available options:"
    echo "1. 🚀 Quick Start - Start app immediately"
    echo "2. 🔧 Advanced Options - Access utility scripts"
    echo "3. ❓ Help & Troubleshooting"
    echo
    read -p "Enter your choice (1-3): " choice
    # Adjust choice for non-GUI systems
    case $choice in
        1) choice=2 ;;
        2) choice=3 ;;
        3) choice=4 ;;
    esac
fi

case $choice in
    1)
        if [ "$GUI_AVAILABLE" = true ]; then
            echo "🖥️ Starting GUI Launcher..."

            # Set environment variables for macOS tkinter issues
            export TK_SILENCE_DEPRECATION=1

            # Strategy: Check for home directory virtual environment first, then system Python for GUI compatibility
            # The GUI launcher will handle importing packages from virtual environment
            if [ -f "$venv_dir/bin/python" ]; then
                echo "[SUCCESS] Using home directory virtual environment Python..."
                "$venv_dir/bin/python" gui_launcher.py
            elif [ -f "venv/bin/python" ]; then
                echo "[WARNING] Using legacy virtual environment Python..."
                venv/bin/python gui_launcher.py
            elif [ -n "$SYSTEM_PYTHON_CMD" ] && command -v "$SYSTEM_PYTHON_CMD" >/dev/null 2>&1; then
                echo "[INFO] Using system Python for GUI compatibility: $SYSTEM_PYTHON_CMD"
                "$SYSTEM_PYTHON_CMD" gui_launcher.py
            elif [[ "$OSTYPE" == darwin* ]] && command -v "$PYTHON_CMD" >/dev/null 2>&1; then
                echo "[INFO] Using current Python for macOS GUI compatibility..."
                "$PYTHON_CMD" gui_launcher.py
            else
                echo "[WARNING] Using fallback Python..."
                "$PYTHON_CMD" gui_launcher.py
            fi
        else
            echo "❌ GUI not available on this system"
            exit 1
        fi
        ;;
    2)
        echo "🚀 Quick Start - Starting application..."

        # For SharePoint environments, check if we have working Python setup
        if [ "$SHAREPOINT_MODE" = true ]; then
            echo "🌐 SharePoint mode detected - using configured Python environment"

            # Check if virtual environment is available and working
            if [ -n "$VIRTUAL_ENV" ] && [ -f "$VIRTUAL_ENV/bin/python" ]; then
                echo "📦 Using virtual environment Python..."
                if $PYTHON_CMD -c "import flask" >/dev/null 2>&1; then
                    echo "✅ Virtual environment is ready"
                    $PYTHON_CMD run.py
                else
                    echo "⚠️ Virtual environment missing dependencies, trying anyway..."
                    $PYTHON_CMD run.py
                fi
            else
                echo "⚠️ Virtual environment not available, using system Python..."
                # Verify system Python has required packages
                if $PYTHON_CMD -c "import flask" >/dev/null 2>&1; then
                    echo "✅ System Python has required packages"
                    $PYTHON_CMD run.py
                else
                    echo "❌ Flask not available in system Python"
                    echo ""
                    echo "💡 Quick fixes:"
                    echo "   1. Run setup first (option 3 → 1)"
                    echo "   2. Install manually: $PYTHON_CMD -m pip install --user --break-system-packages flask"
                    echo "   3. Or try with all requirements: $PYTHON_CMD -m pip install --user --break-system-packages -r requirements.txt"
                    echo ""
                    read -p "Would you like to try automatic package installation now? (y/n): " install_now
                    if [[ $install_now =~ ^[Yy]$ ]]; then
                        echo "📦 Installing packages automatically..."
                        if $PYTHON_CMD -m pip install --user --break-system-packages -r requirements.txt >/dev/null 2>&1; then
                            echo "✅ Packages installed successfully"
                            echo "🚀 Starting application..."
                            $PYTHON_CMD run.py
                        elif $PYTHON_CMD -m pip install --user --break-system-packages flask >/dev/null 2>&1; then
                            echo "✅ Flask installed (minimal setup)"
                            echo "🚀 Starting application..."
                            $PYTHON_CMD run.py
                        else
                            echo "❌ Automatic installation failed"
                            read -p "Attempt to start anyway? (y/n): " start_anyway
                            if [[ $start_anyway =~ ^[Yy]$ ]]; then
                                $PYTHON_CMD run.py
                            else
                                echo "❌ Application startup cancelled"
                                exit 1
                            fi
                        fi
                    else
                        read -p "Attempt to start anyway? (y/n): " start_anyway
                        if [[ $start_anyway =~ ^[Yy]$ ]]; then
                            $PYTHON_CMD run.py
                        else
                            echo "❌ Application startup cancelled"
                            exit 1
                        fi
                    fi
                fi
            fi
        else
            # Local development environment
            if [ -f "venv/bin/python" ]; then
                echo "📦 Using virtual environment Python..."
                venv/bin/python run.py
            else
                echo "⚠️ Virtual environment not found, using system Python..."
                $PYTHON_CMD run.py
            fi
        fi
        ;;
    3)
        echo "🔧 Advanced Options:"
        echo "1. Setup/Repair Environment"
        echo "2. Run Diagnostics"
        echo "3. Create Sample Data"
        echo "4. Clear All Data (⚠️ Destructive)"
        echo "5. Debug Mode"
        echo "6. Back to Main Menu"
        echo
        read -p "Choose option (1-6): " adv_choice

        case $adv_choice in
            1)
                echo "🔧 Running setup..."
                if [ -f "scripts/setup_missing_files.sh" ]; then
                    bash scripts/setup_missing_files.sh
                else
                    echo "Setting up environment..."

                    # Function to validate virtual environment
                    validate_venv() {
                        if [ ! -d "venv" ]; then
                            return 1  # No venv directory
                        fi

                        # Check for Python executable
                        if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" || "$OSTYPE" == "mingw"* ]]; then
                            VENV_PYTHON="venv/Scripts/python.exe"
                        else
                            VENV_PYTHON="venv/bin/python"
                        fi

                        # Test if Python executable exists and works
                        if [ -f "$VENV_PYTHON" ]; then
                            if $VENV_PYTHON --version >/dev/null 2>&1; then
                                echo "✅ Virtual environment Python working: $($VENV_PYTHON --version)"
                                return 0
                            else
                                echo "❌ Virtual environment Python broken"
                                return 2
                            fi
                        else
                            echo "❌ Virtual environment Python executable missing"
                            return 2
                        fi
                    }

                    # Function to create/repair virtual environment
                    create_venv() {
                        echo "📦 Creating virtual environment..."
                        $PYTHON_CMD -m venv venv || {
                            echo "❌ Failed to create virtual environment"
                            echo "💡 Try: $PYTHON_CMD -m pip install --user virtualenv"
                            return 1
                        }
                        echo "✅ Virtual environment created successfully"
                        return 0
                    }

                    # Function to repair broken virtual environment
                    repair_venv() {
                        echo "🔧 Repairing virtual environment..."
                        if [ -d "venv" ]; then
                            echo "🗑️ Removing broken virtual environment..."
                            rm -rf venv
                        fi
                        create_venv
                    }

                    # Validate or create virtual environment
                    echo "🔍 Checking virtual environment..."
                    venv_status=$(validate_venv; echo $?)

                    case $venv_status in
                        0)
                            echo "✅ Virtual environment is ready"
                            ;;
                        1)
                            echo "📦 Virtual environment not found, creating..."
                            create_venv || exit 1
                            ;;
                        2)
                            echo "🔧 Virtual environment broken, repairing..."
                            repair_venv || exit 1
                            ;;
                    esac

                    # Set Python executable path
                    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" || "$OSTYPE" == "mingw"* ]]; then
                        VENV_PYTHON="venv/Scripts/python.exe"
                    else
                        VENV_PYTHON="venv/bin/python"
                    fi

                    # Verify the Python executable one more time
                    if ! $VENV_PYTHON --version >/dev/null 2>&1; then
                        echo "❌ Virtual environment setup failed"
                        exit 1
                    fi

                    echo "📦 Upgrading pip..."
                    # Try multiple pip upgrade strategies
                    $VENV_PYTHON -m pip install --upgrade pip || {
                        echo "⚠️ Standard pip upgrade failed, trying with trusted hosts..."
                        $VENV_PYTHON -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org --upgrade pip || {
                            echo "⚠️ Trusted hosts upgrade failed, trying with --user flag..."
                            $VENV_PYTHON -m pip install --user --upgrade pip || {
                                echo "⚠️ All pip upgrade methods failed, continuing with existing pip..."
                            }
                        }
                    }

                    echo "📦 Installing requirements..."
                    # Try multiple installation strategies
                    $VENV_PYTHON -m pip install -r requirements.txt || {
                        echo "⚠️ Standard install failed, trying with trusted hosts..."
                        $VENV_PYTHON -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org -r requirements.txt || {
                            echo "⚠️ Trusted hosts install failed, trying individual packages..."
                            # Try installing packages individually
                            while IFS= read -r package; do
                                if [[ ! "$package" =~ ^[[:space:]]*# ]] && [[ -n "$package" ]]; then
                                    echo "📦 Installing $package..."
                                    $VENV_PYTHON -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org "$package" || {
                                        echo "⚠️ Failed to install $package, continuing..."
                                    }
                                fi
                            done < requirements.txt
                        }
                    }

                    # Check for tkinter on macOS
                    if [[ "$OSTYPE" == darwin* ]]; then
                        echo "🔍 Checking tkinter availability..."
                        if ! $VENV_PYTHON -c "import tkinter; print('tkinter available')" 2>/dev/null; then
                            echo "⚠️ tkinter not available, attempting to install..."
                            install_macos_tkinter
                        else
                            echo "✅ tkinter is available"
                        fi
                    fi

                    echo "✅ Setup complete"
                fi
                ;;
            2)
                echo "🩺 Running diagnostics..."
                if [ -f "scripts/diagnose.sh" ]; then
                    bash scripts/diagnose.sh
                else
                    echo ""
                    echo "=== AdhocLog Diagnostics ==="
                    echo ""
                    echo "🐍 Python Information:"
                    echo "  Command: $PYTHON_CMD"
                    echo "  Version: $($PYTHON_CMD --version 2>/dev/null || echo 'Not available')"
                    echo "  Location: $(which $PYTHON_CMD 2>/dev/null || echo 'Not in PATH')"
                    echo ""
                    echo "🌐 Environment:"
                    echo "  SharePoint Mode: $SHAREPOINT_MODE"
                    echo "  Current Directory: $PWD"
                    echo "  User: $(whoami)"
                    echo "  Platform: $(uname -s) $(uname -m)"
                    echo ""
                    echo "📁 Directory Structure:"
                    echo "  Virtual env: $([ -d "venv" ] && echo "✅ Found (legacy)" || echo "❌ Missing")"
                    echo "  User venvs: $([ -d "venvs" ] && echo "✅ Found ($(ls venvs/ 2>/dev/null | wc -l | xargs) environments)" || echo "❌ Missing")"
                    echo "  Data dir: $([ -d "data" ] && echo "✅ Found" || echo "❌ Missing")"
                    echo "  Requirements: $([ -f "requirements.txt" ] && echo "✅ Found" || echo "❌ Missing")"
                    echo ""
                    if [ "$SHAREPOINT_MODE" = true ]; then
                        echo "🌐 SharePoint Environment:"
                        echo "  Virtual Environment: ${VIRTUAL_ENV:-'Not set'}"
                        echo "  User Data Dir: ${ADHOCLOG_USER_DATA_DIR:-'Not set'}"
                        echo "  Cache Isolation: ${PYTHONDONTWRITEBYTECODE:-'Not set'}"
                        echo ""
                    fi
                    echo "📦 Package Availability:"
                    for pkg in flask werkzeug pandas openpyxl; do
                        if $PYTHON_CMD -c "import $pkg; print('$pkg:', $pkg.__version__)" 2>/dev/null; then
                            echo "  ✅ $pkg: Available"
                        else
                            echo "  ❌ $pkg: Not available"
                        fi
                    done
                    echo ""
                    echo "🖥️ GUI Status:"
                    echo "  GUI Available: $GUI_AVAILABLE"
                    echo "  GUI Reason: ${GUI_REASON:-'Not tested'}"
                    echo ""
                    echo "🔧 Permissions:"
                    echo "  Write to current dir: $(touch test_write 2>/dev/null && rm -f test_write && echo '✅ Yes' || echo '❌ No')"
                    echo "  Create venv dir: $(mkdir -p test_venv 2>/dev/null && rmdir test_venv && echo '✅ Yes' || echo '❌ No')"
                    echo ""
                    if [ "$SHAREPOINT_MODE" = true ]; then
                        echo "💡 SharePoint Recommendations:"
                        if [ ! -d "venvs" ]; then
                            echo "  - Run setup to create virtual environment (option 1)"
                        fi
                        if [ -z "$VIRTUAL_ENV" ]; then
                            echo "  - Virtual environment not activated properly"
                        fi
                        if ! $PYTHON_CMD -c "import flask" >/dev/null 2>&1; then
                            echo "  - Flask not available - run package installation"
                        fi
                    fi
                fi
                ;;
            3)
                echo "📊 Creating sample data..."
                # Use virtual environment Python if available
                if [ -f "venv/bin/python" ]; then
                    echo "📦 Using virtual environment Python..."
                    venv/bin/python create_sample_data.py
                else
                    echo "⚠️ Virtual environment not found, using system Python..."
                    $PYTHON_CMD create_sample_data.py
                fi
                echo "✅ Sample data created"
                ;;
            4)
                echo "🗑️ Clear All Data..."
                if [ -f "scripts/clear_data.sh" ]; then
                    bash scripts/clear_data.sh
                else
                    echo "⚠️ WARNING: This will delete all your task data!"
                    read -p "Are you sure? (yes/no): " confirm
                    if [[ $confirm == "yes" ]]; then
                        rm -f data/tasks_*.json 2>/dev/null
                        echo "✅ Data cleared"
                    else
                        echo "❌ Operation cancelled"
                    fi
                fi
                ;;
            5)
                echo "🐛 Starting in debug mode..."
                if [ -f "scripts/run_debug.sh" ]; then
                    bash scripts/run_debug.sh
                else
                    export FLASK_DEBUG=1
                    # Use virtual environment Python if available
                    if [ -f "venv/bin/python" ]; then
                        echo "📦 Using virtual environment Python..."
                        venv/bin/python run.py
                    else
                        echo "⚠️ Virtual environment not found, using system Python..."
                        $PYTHON_CMD run.py
                    fi
                fi
                ;;
            6)
                echo "↩️ Returning to main menu..."
                exec "$0"
                ;;
            *)
                echo "❌ Invalid option"
                ;;
        esac
        ;;
    4)
        echo "❓ Help & Troubleshooting:"
        echo
        echo "📋 Common Issues & Solutions:"
        echo
        echo "🐍 Python Issues:"
        echo "• Python not found:"
        echo "  - macOS: brew install python@3.11"
        echo "  - Linux: sudo apt-get install python3 python3-pip python3-venv"
        echo "  - Windows: Download from python.org (check 'Add to PATH')"
        echo
        echo "🖥️ GUI Issues:"
        echo "• tkinter not available:"
        echo "  - macOS: brew install python-tk"
        echo "  - Linux: sudo apt-get install python3-tk"
        echo "  - Windows: Reinstall Python with tkinter support"
        echo "• GUI launcher won't start:"
        echo "  - Check if you're in SSH session (use option 1 instead)"
        echo "  - Try: export TK_SILENCE_DEPRECATION=1"
        echo
        echo "📦 Dependency Issues:"
        echo "• pip install fails:"
        echo "  - Corporate network: Run setup (option 2 → 1)"
        echo "  - Permission denied: Use virtual environment"
        echo "  - SSL errors: Setup configures trusted hosts automatically"
        echo
        echo "🌐 Network Issues:"
        echo "• Port in use: App finds available port automatically"
        echo "• Can't access app: Try http://localhost:PORT instead of 127.0.0.1"
        echo "• Firewall blocking: Check system firewall settings"
        echo
        echo "📁 File Structure:"
        echo "• launch_app.sh - This launcher script"
        echo "• gui_launcher.py - Graphical interface"
        echo "• run.py - Command-line application starter"
        echo "• scripts/ - Utility scripts for maintenance"
        echo "• data/ - Your task data (JSON files)"
        echo "• venv/ - Virtual environment (auto-created)"
        echo
        echo "🔧 Quick Fixes:"
        echo "• Run diagnostics: Choose option 2 → 2"
        echo "• Reset environment: Delete 'venv' folder and run setup"
        echo "• Clear data: Choose option 2 → 4 (⚠️ destructive)"
        echo
        echo "🆘 Still Need Help?"
        echo "• Check the README.md file for detailed instructions"
        echo "• Run full diagnostics to identify specific issues"
        echo "• Contact your system administrator with diagnostic output"
        echo
        read -p "Press Enter to return to main menu..."
        exec "$0"
        ;;
    *)
        echo "❌ Invalid option. Please try again."
        exec "$0"
        ;;
esac
