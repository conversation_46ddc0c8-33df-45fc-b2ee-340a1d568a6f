#!/bin/bash

# AdhocLog - Robust Startup Script
# This version handles more edge cases and provides better debugging

# Change to the project root directory (parent of scripts)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_DIR"

set -e  # Exit on any error

echo "========================================"
echo "  AdhocLog - Starting..."
echo "========================================"
echo "📁 Working directory: $(pwd)"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check Python installation
echo "Checking Python installation..."
PYTHON_CMD=""

if command_exists python3; then
    PYTHON_CMD="python3"
    print_status "Found python3"
elif command_exists python; then
    # Check if it's Python 3
    if python --version 2>&1 | grep -q "Python 3"; then
        PYTHON_CMD="python"
        print_status "Found python (Python 3)"
    else
        print_error "Found python but it's Python 2. Please install Python 3.7+"
        exit 1
    fi
else
    print_error "Python 3 is not installed or not in PATH"
    echo "Please install Python 3.7+ from https://python.org"
    exit 1
fi

# Display Python version
echo "Python version: $($PYTHON_CMD --version)"
echo

# Check pip availability
if ! $PYTHON_CMD -m pip --version >/dev/null 2>&1; then
    print_error "pip is not available. Please install pip."
    exit 1
fi

print_status "pip is available"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    $PYTHON_CMD -m venv venv
    print_status "Virtual environment created"
else
    print_status "Virtual environment already exists"
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Verify activation
if [[ "$VIRTUAL_ENV" != "" ]]; then
    print_status "Virtual environment activated: $(basename $VIRTUAL_ENV)"
else
    print_warning "Virtual environment activation unclear, continuing..."
fi

# Use the Python from virtual environment
VENV_PYTHON="$VIRTUAL_ENV/bin/python"
if [ -f "$VENV_PYTHON" ]; then
    PYTHON_CMD="$VENV_PYTHON"
    print_status "Using virtual environment Python"
else
    print_warning "Using system Python (virtual environment may not be active)"
fi

# Upgrade pip in virtual environment
echo "Upgrading pip..."
$PYTHON_CMD -m pip install --upgrade pip --quiet
print_status "pip upgraded"

# Install dependencies
echo "Installing dependencies..."
if [ -f "requirements.txt" ]; then
    $PYTHON_CMD -m pip install -r requirements.txt
    print_status "Dependencies installed successfully"
else
    print_error "requirements.txt not found!"
    exit 1
fi

# Create sample data if needed
if [ ! -d "data" ] || [ ! -f "data/tasks_$(whoami).json" ]; then
    echo "Creating sample data..."
    $PYTHON_CMD create_sample_data.py
    print_status "Sample data created"
else
    print_status "Data already exists"
fi

# Start the application
echo
echo "========================================"
echo "  Starting Flask Application..."
echo "  Open your browser to: http://localhost:5000"
echo "  Press Ctrl+C to stop the server"
echo "========================================"
echo

print_status "Starting Flask application..."
$PYTHON_CMD app.py
