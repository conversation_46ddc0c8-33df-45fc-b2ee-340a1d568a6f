#!/bin/bash

# Test SharePoint Setup Script
# This script helps diagnose SharePoint environment issues

echo "🧪 SharePoint Environment Test"
echo "=============================="
echo

# Check current directory
echo "📍 Current Directory:"
echo "   $PWD"
echo

# Check if we're in SharePoint
if [[ "$PWD" == *"OneDrive"* ]] || [[ "$PWD" == *"SharePoint"* ]]; then
    echo "🌐 SharePoint Environment: DETECTED"
else
    echo "🌐 SharePoint Environment: NOT DETECTED"
fi
echo

# Check Python
echo "🐍 Python Information:"
if command -v python3 >/dev/null 2>&1; then
    PYTHON_CMD="python3"
elif command -v python >/dev/null 2>&1; then
    PYTHON_CMD="python"
else
    echo "❌ No Python found"
    exit 1
fi

echo "   Command: $PYTHON_CMD"
echo "   Version: $($PYTHON_CMD --version)"
echo "   Location: $(which $PYTHON_CMD)"
echo

# Test virtual environment creation
echo "🧪 Testing Virtual Environment Creation:"
TEST_VENV="test_sharepoint_venv"

if $PYTHON_CMD -m venv "$TEST_VENV" 2>/dev/null; then
    echo "✅ Virtual environment creation: SUCCESS"

    # Test package installation
    echo "📦 Testing Package Installation:"
    if $TEST_VENV/bin/python -m pip install flask >/dev/null 2>&1; then
        echo "✅ Package installation: SUCCESS"

        # Test Flask import
        if $TEST_VENV/bin/python -c "import flask; print('Flask version:', flask.__version__)" 2>/dev/null; then
            echo "✅ Flask import: SUCCESS"
        else
            echo "❌ Flask import: FAILED"
        fi
    else
        echo "❌ Package installation: FAILED"
    fi

    # Cleanup
    rm -rf "$TEST_VENV"
else
    echo "❌ Virtual environment creation: FAILED"

    # Try to capture error
    echo "💡 Error details:"
    $PYTHON_CMD -m venv "$TEST_VENV" 2>&1 | head -3
fi
echo

# Test system Python package installation
echo "🧪 Testing System Python Package Access:"
if $PYTHON_CMD -c "import flask" >/dev/null 2>&1; then
    echo "✅ Flask available in system Python"
else
    echo "❌ Flask not available in system Python"

    # Test if we can install with --user
    echo "📦 Testing --user installation:"
    if $PYTHON_CMD -m pip install --user flask --dry-run >/dev/null 2>&1; then
        echo "✅ --user installation: POSSIBLE"
    else
        echo "❌ --user installation: BLOCKED (externally managed)"

        # Test --break-system-packages
        echo "🔓 Testing --break-system-packages:"
        if $PYTHON_CMD -m pip install --user --break-system-packages flask --dry-run >/dev/null 2>&1; then
            echo "✅ --break-system-packages: POSSIBLE"
        else
            echo "❌ --break-system-packages: STILL BLOCKED"
        fi
    fi
fi
echo

# Check permissions
echo "🔒 Testing Permissions:"
if touch test_write 2>/dev/null; then
    echo "✅ Write permission: OK"
    rm -f test_write
else
    echo "❌ Write permission: DENIED"
fi

if mkdir -p test_dir 2>/dev/null; then
    echo "✅ Directory creation: OK"
    rmdir test_dir
else
    echo "❌ Directory creation: DENIED"
fi
echo

echo "🎯 Recommendations:"
if [[ "$PWD" == *"OneDrive"* ]] || [[ "$PWD" == *"SharePoint"* ]]; then
    echo "   - You're in a SharePoint environment"
    echo "   - Virtual environments should work if permissions allow"
    echo "   - If venv fails, use system Python with --break-system-packages"
fi

echo
echo "✅ Test complete!"
