#!/bin/bash

# Change to the project root directory (parent of scripts)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_DIR"

echo "========================================"
echo "  AdhocLog - Auto Port"
echo "========================================"

# Function to check if port is available
is_port_available() {
    ! lsof -i:$1 >/dev/null 2>&1
}

# Try different ports
PORTS=(8000 5001 3000 8080 9000 7000)

echo "🔍 Checking for available ports..."

for PORT in "${PORTS[@]}"; do
    if is_port_available $PORT; then
        echo "✅ Port $PORT is available!"

        # Activate virtual environment
        source venv/bin/activate

        # Set the port and run the app
        echo "🚀 Starting Flask app on port $PORT..."
        echo "📱 Open your browser to: http://127.0.0.1:$PORT"
        echo "🛑 Press Ctrl+C to stop the server"
        echo "-" * 50

        # Run Python with the port as an environment variable
        FLASK_RUN_PORT=$PORT python -c "
import os
from app import app
port = int(os.environ.get('FLASK_RUN_PORT', $PORT))
print(f'Starting on port {port}...')
app.run(debug=True, host='0.0.0.0', port=port)
"
        exit 0
    else
        echo "❌ Port $PORT is in use"
    fi
done

echo "😞 No available ports found in the range. Try manually:"
echo "python -c \"from app import app; app.run(debug=True, host='0.0.0.0', port=9999)\""
