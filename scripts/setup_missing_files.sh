#!/bin/bash

# Change to the project root directory (parent of scripts)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_DIR"

echo "========================================"
echo "  AdhocLog - Setup & Repair"
echo "========================================"
echo "📁 Project directory: $(pwd)"

# Create requirements.txt
echo "Creating requirements.txt..."
cat > requirements.txt << 'EOF'
Flask==2.3.3
python-dotenv==1.0.0
Werkzeug==2.3.7
EOF

echo "✅ requirements.txt created"

# Check if other essential files exist
echo
echo "Checking other essential files..."

if [ ! -f "app.py" ]; then
    echo "❌ app.py is missing - this is a critical file!"
    echo "You may need to re-download or recreate the project files."
else
    echo "✅ app.py exists"
fi

if [ ! -f "config.py" ]; then
    echo "❌ config.py is missing"
else
    echo "✅ config.py exists"
fi

if [ ! -f "data_manager.py" ]; then
    echo "❌ data_manager.py is missing"
else
    echo "✅ data_manager.py exists"
fi

if [ ! -d "templates" ]; then
    echo "❌ templates directory is missing"
else
    echo "✅ templates directory exists"
fi

echo
echo "🔧 Setting up virtual environment..."
if [ ! -d "venv" ]; then
    python3 -m venv venv 2>/dev/null || python -m venv venv
    echo "✅ Virtual environment created"
else
    echo "✅ Virtual environment already exists"
fi

echo
echo "📥 Installing dependencies..."
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
    # Windows (Git Bash/MSYS)
    source venv/Scripts/activate
else
    # macOS/Linux
    source venv/bin/activate
fi

pip install --upgrade pip >/dev/null 2>&1
pip install -r requirements.txt >/dev/null 2>&1
echo "✅ Dependencies installed"

echo
echo "📝 Checking sample data..."
if [ ! -d "data" ] || [ ! "$(ls -A data/tasks_*.json 2>/dev/null)" ]; then
    echo "Creating sample data..."
    python create_sample_data.py >/dev/null 2>&1
    echo "✅ Sample data created"
else
    echo "✅ Data files already exist"
fi

echo
echo "🎉 Setup complete! You can now:"
echo "  • Run: ./launch_app.sh (choose option 2 for quick start)"
echo "  • Or use GUI: ./launch_app.sh (choose option 1)"
