#!/bin/bash

# Debug version - won't exit on errors, shows detailed output
# This version will help you see exactly where the problem occurs

# Change to the project root directory (parent of scripts)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_DIR"

echo "========================================"
echo "  AdhocLog - DEBUG MODE"
echo "========================================"
echo "📁 Project directory: $(pwd)"
echo

# Function to pause and wait for user input
pause_on_error() {
    echo
    echo "❌ ERROR OCCURRED: $1"
    echo "Press Enter to continue or Ctrl+C to exit..."
    read -r
}

# Function to show success
show_success() {
    echo "✅ SUCCESS: $1"
}

echo "🔍 STEP 1: Checking Python installation..."
if command -v python3 >/dev/null 2>&1; then
    show_success "python3 found at: $(which python3)"
    echo "   Version: $(python3 --version)"
else
    pause_on_error "python3 not found in PATH"
    exit 1
fi

echo
echo "🔍 STEP 2: Checking pip availability..."
if python3 -m pip --version >/dev/null 2>&1; then
    show_success "pip is available"
    echo "   Version: $(python3 -m pip --version)"
else
    pause_on_error "pip is not available with python3"
    exit 1
fi

echo
echo "🔍 STEP 3: Creating virtual environment..."
if [ -d "venv" ]; then
    show_success "Virtual environment already exists"
else
    echo "Creating virtual environment..."
    python3 -m venv venv
    if [ $? -eq 0 ]; then
        show_success "Virtual environment created"
    else
        pause_on_error "Failed to create virtual environment"
        exit 1
    fi
fi

echo
echo "🔍 STEP 4: Activating virtual environment..."
source venv/bin/activate
if [ $? -eq 0 ]; then
    show_success "Virtual environment activation command executed"
    if [[ "$VIRTUAL_ENV" != "" ]]; then
        show_success "Virtual environment is active: $VIRTUAL_ENV"
    else
        echo "⚠️  WARNING: VIRTUAL_ENV variable not set, but continuing..."
    fi
else
    pause_on_error "Failed to activate virtual environment"
fi

echo
echo "🔍 STEP 5: Checking which Python we're using..."
echo "   which python: $(which python 2>/dev/null || echo 'not found')"
echo "   which python3: $(which python3 2>/dev/null || echo 'not found')"
echo "   which pip: $(which pip 2>/dev/null || echo 'not found')"

echo
echo "🔍 STEP 6: Upgrading pip..."
python -m pip install --upgrade pip
if [ $? -eq 0 ]; then
    show_success "pip upgraded successfully"
else
    echo "⚠️  WARNING: pip upgrade failed, but continuing..."
fi

echo
echo "🔍 STEP 7: Installing dependencies (THIS IS WHERE ERRORS USUALLY OCCUR)..."
echo "About to run: python -m pip install -r requirements.txt"
echo "Press Enter to continue..."
read -r

echo "Installing dependencies..."
python -m pip install -r requirements.txt
INSTALL_RESULT=$?

if [ $INSTALL_RESULT -eq 0 ]; then
    show_success "Dependencies installed successfully!"
else
    echo
    echo "❌ DEPENDENCY INSTALLATION FAILED!"
    echo "Exit code: $INSTALL_RESULT"
    echo
    echo "Let's try some troubleshooting steps:"
    echo
    echo "1. Checking if requirements.txt exists and is readable:"
    if [ -f requirements.txt ]; then
        echo "✅ requirements.txt exists"
        echo "Contents:"
        cat requirements.txt
    else
        echo "❌ requirements.txt not found!"
    fi

    echo
    echo "2. Trying to install each package individually:"
    echo "Installing Flask..."
    python -m pip install Flask==2.3.3
    echo "Installing python-dotenv..."
    python -m pip install python-dotenv==1.0.0
    echo "Installing Werkzeug..."
    python -m pip install Werkzeug==2.3.7

    echo
    echo "3. Checking pip list:"
    python -m pip list

    pause_on_error "Dependency installation failed - see details above"
fi

echo
echo "🔍 STEP 8: Creating sample data..."
if [ ! -d "data" ]; then
    python create_sample_data.py
    if [ $? -eq 0 ]; then
        show_success "Sample data created"
    else
        echo "⚠️  WARNING: Sample data creation failed, but continuing..."
    fi
else
    show_success "Data directory already exists"
fi

echo
echo "🔍 STEP 9: Starting Flask application..."
echo "About to run: python app.py"
echo "Press Enter to start the app (or Ctrl+C to exit)..."
read -r

python app.py
