#!/bin/bash

# Diagnostic script for AdhocLog

# Change to the project root directory (parent of scripts)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_DIR"

echo "========================================"
echo "  AdhocLog - Diagnostics"
echo "========================================"
echo

echo "System Information:"
echo "OS: $(uname -s)"
echo "Architecture: $(uname -m)"
echo "User: $(whoami)"
echo "Project directory: $(pwd)"
echo

echo "Python Information:"
if command -v python3 >/dev/null 2>&1; then
    echo "✓ python3 found: $(which python3)"
    echo "  Version: $(python3 --version)"
    echo "  pip3 available: $(python3 -m pip --version 2>/dev/null && echo "Yes" || echo "No")"
else
    echo "✗ python3 not found"
fi

if command -v python >/dev/null 2>&1; then
    echo "✓ python found: $(which python)"
    echo "  Version: $(python --version)"
    echo "  pip available: $(python -m pip --version 2>/dev/null && echo "Yes" || echo "No")"
else
    echo "✗ python not found"
fi
echo

echo "Project Files:"
echo "requirements.txt: $([ -f requirements.txt ] && echo "✓ Found" || echo "✗ Missing")"
echo "app.py: $([ -f app.py ] && echo "✓ Found" || echo "✗ Missing")"
echo "config.py: $([ -f config.py ] && echo "✓ Found" || echo "✗ Missing")"
echo "data_manager.py: $([ -f data_manager.py ] && echo "✓ Found" || echo "✗ Missing")"
echo "templates/: $([ -d templates ] && echo "✓ Found" || echo "✗ Missing")"
echo

echo "Virtual Environment:"
if [ -d "venv" ]; then
    echo "✓ venv directory exists"
    echo "  Python executable: $([ -f venv/bin/python ] && echo "✓ Found" || echo "✗ Missing")"
    echo "  Activate script: $([ -f venv/bin/activate ] && echo "✓ Found" || echo "✗ Missing")"
else
    echo "✗ venv directory not found"
fi
echo

echo "Data Directory:"
if [ -d "data" ]; then
    echo "✓ data directory exists"
    echo "  Contents: $(ls -la data/ 2>/dev/null || echo "Empty or inaccessible")"
else
    echo "✗ data directory not found"
fi
echo

echo "Permissions:"
echo "launch_app.sh executable: $([ -x launch_app.sh ] && echo "✓ Yes" || echo "✗ No")"
echo "run.py readable: $([ -r run.py ] && echo "✓ Yes" || echo "✗ No")"
echo "gui_launcher.py readable: $([ -r gui_launcher.py ] && echo "✓ Yes" || echo "✗ No")"
echo

echo "Network:"
echo "Port 5000 in use: $(lsof -ti:5000 >/dev/null 2>&1 && echo "✓ Yes (may need to kill process)" || echo "✗ No (available)")"
echo "Port 8000 in use: $(lsof -ti:8000 >/dev/null 2>&1 && echo "✓ Yes (may need to kill process)" || echo "✗ No (available)")"
echo

echo "Flask Dependencies:"
if [ -d "venv" ]; then
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
        source venv/Scripts/activate 2>/dev/null
    else
        source venv/bin/activate 2>/dev/null
    fi

    echo "Flask installed: $(python -c 'import flask; print("✓ Version:", flask.__version__)' 2>/dev/null || echo "✗ Not installed")"
    echo "Werkzeug installed: $(python -c 'import werkzeug; print("✓ Version:", werkzeug.__version__)' 2>/dev/null || echo "✗ Not installed")"
    echo "Jinja2 installed: $(python -c 'import jinja2; print("✓ Version:", jinja2.__version__)' 2>/dev/null || echo "✗ Not installed")"
else
    echo "✗ Cannot check Flask dependencies - no virtual environment"
fi
echo

echo "========================================"
echo "Diagnostic complete!"
echo "========================================"
