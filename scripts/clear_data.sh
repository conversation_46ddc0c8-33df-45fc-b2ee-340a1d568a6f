#!/bin/bash

# Clear data script for AdhocLog

# Change to the project root directory (parent of scripts)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_DIR"

echo "========================================"
echo "  AdhocLog - Clear Data"
echo "========================================"
echo "📁 Project directory: $(pwd)"
echo

# Check if data directory exists
if [ ! -d "data" ]; then
    echo "ℹ️  No data directory found. Nothing to clear."
    exit 0
fi

# Count data files
DATA_FILES=(data/tasks_*.json)
if [ ! -e "${DATA_FILES[0]}" ]; then
    echo "ℹ️  No data files found. Nothing to clear."
    exit 0
fi

echo "⚠️  WARNING: This will permanently delete ALL your task data!"
echo
echo "Found data files:"
for file in "${DATA_FILES[@]}"; do
    if [ -e "$file" ]; then
        echo "  • $(basename "$file")"
    fi
done
echo
echo "This action CANNOT be undone!"
echo

# First confirmation
read -p "Are you sure you want to delete all data? (yes/no): " -r
echo
if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
    echo "❌ Operation cancelled."
    exit 0
fi

# Second confirmation for safety
echo "⚠️  FINAL WARNING: All task data will be permanently deleted!"
read -p "Type 'DELETE' to confirm: " -r
echo
if [[ $REPLY != "DELETE" ]]; then
    echo "❌ Operation cancelled. You must type 'DELETE' exactly."
    exit 0
fi

# Delete data files
echo "🗑️  Deleting data files..."
DELETED_COUNT=0
for file in "${DATA_FILES[@]}"; do
    if [ -e "$file" ]; then
        rm "$file"
        echo "  ✅ Deleted: $(basename "$file")"
        ((DELETED_COUNT++))
    fi
done

# Remove data directory if empty
if [ -d "data" ] && [ -z "$(ls -A data)" ]; then
    rmdir data
    echo "  ✅ Removed empty data directory"
fi

echo
echo "✅ Successfully deleted $DELETED_COUNT data file(s)."
echo "   The application will create fresh sample data when you next run it."
echo
echo "========================================"
echo "Data clearing complete!"
echo "========================================"
