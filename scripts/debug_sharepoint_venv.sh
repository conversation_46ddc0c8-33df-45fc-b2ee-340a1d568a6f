#!/bin/bash

# SharePoint Virtual Environment Debug Script
echo "🧪 SharePoint Virtual Environment Debug"
echo "========================================"

# Set variables
PYTHON_CMD="/opt/homebrew/bin/python3"
USERNAME=$(whoami)
PLATFORM_OS=$(uname -s)
PLATFORM_ARCH=$(uname -m)
VENV_DIR="venvs/user_${USERNAME}_${PLATFORM_OS}_${PLATFORM_ARCH}"

echo "📍 Current Directory: $PWD"
echo "👤 User: $USERNAME"
echo "🔧 Platform: $PLATFORM_OS $PLATFORM_ARCH"
echo "📁 Target Virtual Environment: $VENV_DIR"
echo ""

# Test 1: Create venvs directory
echo "🧪 Test 1: Creating venvs directory..."
if mkdir -p "venvs" 2>/dev/null; then
    echo "✅ venvs directory created successfully"
else
    echo "❌ Failed to create venvs directory"
    exit 1
fi

# Test 2: Create virtual environment
echo ""
echo "🧪 Test 2: Creating virtual environment..."
echo "🔧 Command: $PYTHON_CMD -m venv \"$VENV_DIR\""

if $PYTHON_CMD -m venv "$VENV_DIR" 2>&1; then
    echo "✅ Virtual environment creation command completed"
else
    echo "❌ Virtual environment creation failed"
    exit 1
fi

# Test 3: Check if virtual environment directory exists
echo ""
echo "🧪 Test 3: Checking virtual environment directory..."
if [ -d "$VENV_DIR" ]; then
    echo "✅ Virtual environment directory exists"
    echo "📋 Directory contents:"
    ls -la "$VENV_DIR/"
else
    echo "❌ Virtual environment directory not found"
    exit 1
fi

# Test 4: Check for Python executable
echo ""
echo "🧪 Test 4: Checking Python executable..."
VENV_PYTHON="$PWD/$VENV_DIR/bin/python"
echo "📁 Expected Python path: $VENV_PYTHON"

if [ -f "$VENV_PYTHON" ]; then
    echo "✅ Python executable exists"
    echo "📋 File info:"
    ls -la "$VENV_PYTHON"
else
    echo "❌ Python executable not found"
    echo "📋 bin directory contents:"
    ls -la "$VENV_DIR/bin/" 2>/dev/null || echo "bin directory doesn't exist"
    exit 1
fi

# Test 5: Test Python functionality
echo ""
echo "🧪 Test 5: Testing Python functionality..."
if "$VENV_PYTHON" --version; then
    echo "✅ Virtual environment Python is functional"
else
    echo "❌ Virtual environment Python is not functional"
    echo "🔍 Debug info:"
    echo "   File permissions: $(ls -l "$VENV_PYTHON")"
    echo "   File type: $(file "$VENV_PYTHON" 2>/dev/null || echo "Cannot determine file type")"
    exit 1
fi

# Test 6: Install a package
echo ""
echo "🧪 Test 6: Testing package installation..."
if "$VENV_PYTHON" -m pip install flask >/dev/null 2>&1; then
    echo "✅ Package installation successful"

    # Test importing Flask
    if "$VENV_PYTHON" -c "import flask; print('Flask version:', flask.__version__)" 2>/dev/null; then
        echo "✅ Flask import successful"
    else
        echo "⚠️ Flask installation succeeded but import failed"
    fi
else
    echo "❌ Package installation failed"
    echo "🔍 Trying with verbose output:"
    "$VENV_PYTHON" -m pip install flask
fi

echo ""
echo "🎯 All tests completed!"
echo "✅ Virtual environment appears to be working correctly"

# Cleanup option
read -p "🗑️ Clean up test virtual environment? (y/n): " cleanup
if [[ $cleanup =~ ^[Yy]$ ]]; then
    rm -rf "$VENV_DIR"
    echo "✅ Test virtual environment cleaned up"
fi
