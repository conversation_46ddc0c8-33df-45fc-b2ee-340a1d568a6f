#!/bin/bash

# Change to the project root directory (parent of scripts)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_DIR"

echo "========================================"
echo "  AdhocLog - Starting..."
echo "========================================"
echo "📁 Working directory: $(pwd)"
echo

# Function to check command success
check_command() {
    if [ $? -ne 0 ]; then
        echo "ERROR: $1"
        exit 1
    fi
}

# Check if Python is installed
echo "Checking Python installation..."
if ! command -v python3 &> /dev/null; then
    echo "ERROR: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.7+ from https://python.org"
    exit 1
fi

# Display Python version
python3 --version
echo

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
    check_command "Failed to create virtual environment"
    echo "✓ Virtual environment created successfully"
else
    echo "✓ Virtual environment already exists"
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate
check_command "Failed to activate virtual environment"

# Verify virtual environment is active
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "✓ Virtual environment activated: $VIRTUAL_ENV"
else
    echo "WARNING: Virtual environment may not be properly activated"
fi

# Upgrade pip first
echo "Upgrading pip..."
python -m pip install --upgrade pip
check_command "Failed to upgrade pip"

# Install dependencies
echo "Installing dependencies..."
python -m pip install -r requirements.txt
check_command "Failed to install dependencies"
echo "✓ Dependencies installed successfully"

# Create sample data if no data exists
if [ ! -d "data" ]; then
    echo "Creating sample data..."
    python create_sample_data.py
    check_command "Failed to create sample data"
    echo "✓ Sample data created successfully"
else
    echo "✓ Data directory already exists"
fi

# Start the Flask application
echo
echo "========================================"
echo "  Starting Flask Application..."
echo "  Open your browser to: http://localhost:5000"
echo "  Press Ctrl+C to stop the server"
echo "========================================"
echo

echo "Starting Flask app..."
python app.py
