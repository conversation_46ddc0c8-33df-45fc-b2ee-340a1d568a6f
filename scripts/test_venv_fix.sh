#!/bin/bash

# Test Virtual Environment Fix
# This script tests the fixed virtual environment setup

echo "=========================================="
echo "🧪 Testing Virtual Environment Fix"
echo "=========================================="

# Change to the script's directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR/.."

echo "📍 Working directory: $PWD"

# Test 1: Check if venv exists
echo ""
echo "🔍 Test 1: Checking virtual environment directory..."
if [ -d "venvs/user_jackvincent.balcita_Darwin_arm64" ]; then
    echo "✅ Virtual environment directory exists"

    # Test the Python executable
    venv_python="venvs/user_jackvincent.balcita_Darwin_arm64/bin/python"
    echo "🔍 Testing virtual environment Python: $venv_python"

    if [ -f "$venv_python" ]; then
        echo "✅ Virtual environment Python executable exists"

        if "$venv_python" --version >/dev/null 2>&1; then
            version=$("$venv_python" --version 2>/dev/null)
            echo "✅ Virtual environment Python is functional: $version"

            # Test Flask import
            echo "🔍 Testing Flask import..."
            if "$venv_python" -c "import flask; print('Flask version:', flask.__version__)" 2>/dev/null; then
                echo "✅ Flask is available in virtual environment"
            else
                echo "❌ Flask is not available in virtual environment"
                echo "🔧 Attempting to install Flask..."
                if "$venv_python" -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org flask >/dev/null 2>&1; then
                    echo "✅ Flask installed successfully"
                else
                    echo "❌ Failed to install Flask"
                fi
            fi
        else
            echo "❌ Virtual environment Python is not functional"
        fi
    else
        echo "❌ Virtual environment Python executable not found"
    fi
else
    echo "❌ Virtual environment directory not found"
fi

# Test 2: Check system Python tkinter
echo ""
echo "🔍 Test 2: Checking system Python tkinter..."
system_python="/opt/homebrew/bin/python3"

if [ -f "$system_python" ]; then
    echo "✅ System Python found: $system_python"

    if "$system_python" -c "import tkinter; print('tkinter available')" 2>/dev/null; then
        echo "✅ tkinter is available in system Python"
    else
        echo "❌ tkinter is not available in system Python"
    fi
else
    echo "❌ System Python not found"
fi

# Test 3: Test requirements.txt installation in venv
echo ""
echo "🔍 Test 3: Testing requirements installation..."
if [ -f "requirements.txt" ]; then
    echo "✅ requirements.txt found"

    venv_python="venvs/user_jackvincent.balcita_Darwin_arm64/bin/python"
    if [ -f "$venv_python" ]; then
        echo "🔧 Testing pip installation in virtual environment..."

        # Test a simple package installation
        if "$venv_python" -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org requests >/dev/null 2>&1; then
            echo "✅ Package installation works in virtual environment"
        else
            echo "❌ Package installation failed in virtual environment"
        fi
    fi
else
    echo "❌ requirements.txt not found"
fi

echo ""
echo "=========================================="
echo "🏁 Test Complete"
echo "=========================================="
