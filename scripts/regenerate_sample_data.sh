#!/bin/bash

# Change to the project root directory (parent of scripts)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_DIR"

echo "========================================"
echo "  AdhocLog - Regenerating Sample Data..."
echo "========================================"
echo "📁 Project directory: $(pwd)"

# Backup existing data
if [ -d "data" ]; then
    echo "📁 Backing up existing data..."
    cp -r data data_backup_$(date +%Y%m%d_%H%M%S)
    echo "✅ Backup created"
fi

# Remove old data
echo "🗑️  Removing old sample data..."
rm -rf data

# Activate virtual environment
if [ -d "venv" ]; then
    echo "🔄 Activating virtual environment..."
    source venv/bin/activate
fi

# Generate new sample data
echo "📝 Generating new sample data with updated classifications..."
python create_sample_data.py

echo "✅ Sample data regeneration complete!"
echo "🚀 You can now restart the application to see the new data."
