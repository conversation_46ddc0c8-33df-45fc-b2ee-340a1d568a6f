#!/usr/bin/env python3
"""
AdhocLog - GUI Launcher
User-friendly graphical interface for launching the application
"""

import sys
import os
import platform
import subprocess
import threading
import socket
import webbrowser
import getpass
from pathlib import Path
import shutil
from datetime import datetime

# Handle tkinter import with better error handling for macOS
def check_tkinter_availability():
    """Check if tkinter is available and working"""
    try:
        import tkinter as tk
        # Try to create a test window to ensure tkinter actually works
        test_root = tk.Tk()
        test_root.withdraw()  # Hide the test window
        test_root.destroy()
        return True, None
    except ImportError as e:
        return False, f"tkinter not installed: {e}"
    except Exception as e:
        return False, f"tkinter not working: {e}"

# Add virtual environment packages to path if needed
def setup_package_path():
    """Setup package path to include virtual environment packages"""
    try:
        import sys
        import os
        from pathlib import Path
        import glob
        import getpass

        # Add virtual environment packages to Python path if they exist
        venv_site_packages = None
        home_dir = Path.home()

        # Check for new home directory virtual environment first
        home_venv_patterns = [
            str(home_dir / '.venvs/adhoc-log-app/lib/python*/site-packages'),
            str(home_dir / '.venvs/adhoc-log-app/lib/python3.*/site-packages')
        ]

        # Check for old project-based virtual environments (legacy support)
        script_dir = Path(__file__).parent
        username = getpass.getuser()
        platform_info = f"{platform.system()}_{platform.machine()}"

        legacy_sharepoint_venv_patterns = [
            str(script_dir / f'venvs/user_{username}_{platform_info}/lib/python*/site-packages'),
            str(script_dir / f'venvs/user_{username}_{platform_info}/lib/python3.*/site-packages')
        ]

        legacy_venv_patterns = [
            str(script_dir / 'venv/lib/python*/site-packages'),
            str(script_dir / 'venv/lib/python3.*/site-packages')
        ]

        # Try home directory virtual environment first (preferred)
        for possible_venv in home_venv_patterns:
            matches = glob.glob(possible_venv)
            if matches:
                venv_site_packages = matches[0]
                print(f"[SUCCESS] Found home directory virtual environment packages: {venv_site_packages}")
                break

        # Fall back to legacy SharePoint virtual environment
        if not venv_site_packages:
            for possible_venv in legacy_sharepoint_venv_patterns:
                matches = glob.glob(possible_venv)
                if matches:
                    venv_site_packages = matches[0]
                    print(f"[WARNING] Found legacy SharePoint virtual environment packages: {venv_site_packages}")
                    break

        # Fall back to legacy virtual environment
        if not venv_site_packages:
            for possible_venv in legacy_venv_patterns:
                matches = glob.glob(possible_venv)
                if matches:
                    venv_site_packages = matches[0]
                    print(f"[WARNING] Found legacy virtual environment packages: {venv_site_packages}")
                    break

        if venv_site_packages and os.path.exists(venv_site_packages):
            if venv_site_packages not in sys.path:
                sys.path.insert(0, venv_site_packages)
                print(f"✅ Added virtual environment packages to path: {venv_site_packages}")
                return True
        else:
            # Check if packages are available in user site-packages (for --user installs)
            import site
            user_site = site.getusersitepackages()
            if user_site and os.path.exists(user_site):
                if user_site not in sys.path:
                    sys.path.insert(0, user_site)
                    print(f"📦 Added user site-packages to path: {user_site}")

                # Verify Flask is available
                try:
                    import flask
                    print(f"✅ Flask available in user packages: {flask.__version__}")
                    return True
                except ImportError:
                    print("⚠️ Flask not found in user packages")

            print("⚠️ No virtual environment or user packages found")

    except Exception as e:
        print(f"⚠️ Could not setup package path: {e}")
    return False

# Setup package path first
setup_package_path()

# Check tkinter availability before proceeding
tkinter_available, tkinter_error = check_tkinter_availability()

if not tkinter_available:
    print(f"❌ GUI not available: {tkinter_error}")
    print("\n🔧 To fix this issue:")

    if platform.system() == "Darwin":  # macOS
        print("On macOS, try one of these solutions:")
        print("1. Install Python with tkinter via Homebrew:")
        print("   brew install python-tk")
        print("2. Or install a complete Python distribution:")
        print("   brew install python@3.11")
        print("3. Or use the official Python installer from python.org")
        print("4. Set TK_SILENCE_DEPRECATION=1 to suppress warnings")
    elif platform.system() == "Linux":
        print("On Linux, install tkinter:")
        print("   Ubuntu/Debian: sudo apt-get install python3-tk")
        print("   CentOS/RHEL: sudo yum install tkinter")
        print("   Fedora: sudo dnf install python3-tkinter")
    else:
        print("Please install tkinter for your Python distribution")

    print("\n🚀 You can still use the command-line launcher:")
    print("   python3 run.py")
    sys.exit(1)

# Import tkinter after verification
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext

# Suppress macOS tkinter deprecation warning if requested
if platform.system() == "Darwin" and os.environ.get('TK_SILENCE_DEPRECATION'):
    import warnings
    warnings.filterwarnings("ignore", category=DeprecationWarning)

class TaskTrackerLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("AdhocLog - Launcher")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # Variables
        self.process = None
        self.server_url = None

        # Setup UI
        self.setup_ui()

        # Center window
        self.center_window()

    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (800 // 2)
        y = (self.root.winfo_screenheight() // 2) - (600 // 2)
        self.root.geometry(f"800x600+{x}+{y}")

    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Title
        title_label = ttk.Label(main_frame, text="🚀 AdhocLog",
                               font=('Arial', 20, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))

        subtitle_label = ttk.Label(main_frame, text="Easy-to-use task tracking for IT Client Engineering Team",
                                  font=('Arial', 12))
        subtitle_label.grid(row=1, column=0, columnspan=2, pady=(0, 20))

        # System info frame
        info_frame = ttk.LabelFrame(main_frame, text="System Information", padding="10")
        info_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        info_frame.columnconfigure(1, weight=1)

        # System info
        ttk.Label(info_frame, text="Platform:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        platform_info = f"{platform.system()} {platform.release()}"
        if platform.system() == "Darwin":
            arch = platform.machine()
            if arch == "arm64":
                platform_info += " (Apple Silicon)"
            else:
                platform_info += " (Intel)"
        ttk.Label(info_frame, text=platform_info).grid(row=0, column=1, sticky=tk.W)

        ttk.Label(info_frame, text="Python:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        python_info = f"{sys.version.split()[0]} ({sys.executable})"
        ttk.Label(info_frame, text=python_info).grid(row=1, column=1, sticky=tk.W)

        ttk.Label(info_frame, text="Directory:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Label(info_frame, text=os.getcwd()).grid(row=2, column=1, sticky=tk.W)

        # Add tkinter info
        ttk.Label(info_frame, text="GUI Support:").grid(row=3, column=0, sticky=tk.W, padx=(0, 10))
        tkinter_info = "✅ tkinter available"
        if platform.system() == "Darwin" and "Tk" in str(tk.TkVersion):
            tkinter_info += f" (Tk {tk.TkVersion})"
        ttk.Label(info_frame, text=tkinter_info).grid(row=3, column=1, sticky=tk.W)

        # Action buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, columnspan=2, pady=(0, 20))

        # Main action buttons
        self.start_button = ttk.Button(buttons_frame, text="🚀 Start Application",
                                      command=self.start_application, style='Accent.TButton')
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_button = ttk.Button(buttons_frame, text="🛑 Stop Application",
                                     command=self.stop_application, state='disabled')
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        self.browser_button = ttk.Button(buttons_frame, text="🌐 Open in Browser",
                                        command=self.open_browser, state='disabled')
        self.browser_button.pack(side=tk.LEFT, padx=(0, 10))

        # Utility buttons frame
        utils_frame = ttk.LabelFrame(main_frame, text="Utilities", padding="10")
        utils_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))

        # First row of utility buttons
        utils_buttons_frame1 = ttk.Frame(utils_frame)
        utils_buttons_frame1.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(utils_buttons_frame1, text="🔧 Setup/Repair",
                  command=self.run_setup).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(utils_buttons_frame1, text="🩺 Diagnostics",
                  command=self.run_diagnostics).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(utils_buttons_frame1, text="📊 Sample Data",
                  command=self.create_sample_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(utils_buttons_frame1, text="📁 Open Data Folder",
                  command=self.open_data_folder).pack(side=tk.LEFT, padx=(0, 5))

        # Second row of utility buttons
        utils_buttons_frame2 = ttk.Frame(utils_frame)
        utils_buttons_frame2.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(utils_buttons_frame2, text="💾 Backup Data",
                  command=self.backup_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(utils_buttons_frame2, text="🔄 Restore Data",
                  command=self.restore_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(utils_buttons_frame2, text="🗑️ Clear Data",
                  command=self.clear_data).pack(side=tk.LEFT, padx=(0, 5))

        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="Status & Logs", padding="10")
        status_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        status_frame.columnconfigure(0, weight=1)
        status_frame.rowconfigure(1, weight=1)
        main_frame.rowconfigure(5, weight=1)

        # Status label
        self.status_label = ttk.Label(status_frame, text="Ready to start", foreground="green")
        self.status_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 10))

        # Log text area
        self.log_text = scrolledtext.ScrolledText(status_frame, height=10, width=70)
        self.log_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))

        # Initial log message
        self.log("Welcome to AdhocLog!")
        self.log("Click 'Start Application' to begin.")
        self.log("=" * 50)

    def log(self, message):
        """Add message to log"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def update_status(self, message, color="black"):
        """Update status label"""
        self.status_label.config(text=message, foreground=color)
        self.root.update_idletasks()

    def find_available_port(self, start_port=8000):
        """Find an available port"""
        for port in range(start_port, start_port + 100):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('127.0.0.1', port))
                    return port
            except OSError:
                continue
        return 5000  # fallback

    def validate_virtual_environment(self):
        """Validate and use home directory virtual environment if available, otherwise legacy venv"""
        # First, check for home directory virtual environment (preferred)
        home_dir = Path.home()
        home_venv_path = home_dir / ".venvs" / "adhoc-log-app"

        if home_venv_path.exists():
            self.log(f"[SUCCESS] Found home directory virtual environment: {home_venv_path}")

            # Get expected Python executable path for home venv
            if platform.system() == "Windows":
                python_exe = home_venv_path / "Scripts" / "python.exe"
            else:
                python_exe = home_venv_path / "bin" / "python"

            # Check if Python executable exists and works
            if python_exe.exists():
                try:
                    result = subprocess.run([str(python_exe), "--version"],
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        self.log(f"[SUCCESS] Home directory virtual environment Python working: {result.stdout.strip()}")
                        return python_exe
                    else:
                        self.log("[WARNING] Home directory virtual environment Python not responding")
                except (subprocess.CalledProcessError, subprocess.TimeoutExpired, FileNotFoundError):
                    self.log("[WARNING] Home directory virtual environment Python broken")
            else:
                self.log("[WARNING] Home directory virtual environment Python executable missing")

        # Second, check for legacy SharePoint virtual environment
        username = getpass.getuser()
        platform_info = f"{platform.system()}_{platform.machine()}"
        sharepoint_venv_path = Path(f"venvs/user_{username}_{platform_info}")

        if sharepoint_venv_path.exists():
            self.log(f"[WARNING] Found legacy SharePoint virtual environment: {sharepoint_venv_path}")

            # Get expected Python executable path for SharePoint venv
            if platform.system() == "Windows":
                python_exe = sharepoint_venv_path / "Scripts" / "python.exe"
            else:
                python_exe = sharepoint_venv_path / "bin" / "python"

            # Test if SharePoint virtual environment Python works
            if python_exe.exists():
                try:
                    result = subprocess.run([str(python_exe), "--version"],
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        self.log(f"[WARNING] Legacy SharePoint virtual environment Python working: {result.stdout.strip()}")
                        return python_exe
                    else:
                        self.log("[WARNING] Legacy SharePoint virtual environment Python not responding")
                except subprocess.TimeoutExpired:
                    self.log("[WARNING] Legacy SharePoint virtual environment Python timeout")
                except Exception as e:
                    self.log(f"[WARNING] Legacy SharePoint virtual environment Python error: {e}")
            else:
                self.log("[WARNING] Legacy SharePoint virtual environment Python executable missing")

        # Third, fallback to legacy venv check
        venv_path = Path("venv")

        # Check if legacy virtual environment directory exists
        if not venv_path.exists():
            self.log("[INFO] No virtual environment found")
            self.log("[INFO] Using system Python - will attempt to create home directory virtual environment")
            return None  # Return None to indicate we should use system Python

        # Get expected Python executable path for legacy venv
        if platform.system() == "Windows":
            python_exe = venv_path / "Scripts" / "python.exe"
        else:
            python_exe = venv_path / "bin" / "python"

        # Check if Python executable exists and works
        if not python_exe.exists():
            self.log("[WARNING] Legacy virtual environment Python executable missing")
            return None  # Use system Python instead

        # Test if Python executable actually works
        try:
            result = subprocess.run([str(python_exe), "--version"],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.log(f"[WARNING] Legacy virtual environment Python working: {result.stdout.strip()}")
                return python_exe
            else:
                self.log("[WARNING] Legacy virtual environment Python not responding")
                return None  # Use system Python instead
        except (subprocess.CalledProcessError, subprocess.TimeoutExpired, FileNotFoundError):
            self.log("[WARNING] Virtual environment Python broken or missing")
            return None  # Use system Python instead

    def create_virtual_environment(self):
        """Create a new virtual environment in home directory"""
        try:
            home_dir = Path.home()
            venv_base_dir = home_dir / ".venvs"
            venv_dir = venv_base_dir / "adhoc-log-app"

            self.log("[INSTALL] Creating virtual environment in home directory...")
            self.log(f"[INFO] Target directory: {venv_dir}")

            # Create .venvs directory if it doesn't exist
            venv_base_dir.mkdir(exist_ok=True)

            subprocess.run([sys.executable, "-m", "venv", str(venv_dir)], check=True, timeout=60)
            self.log("[SUCCESS] Virtual environment created successfully")

            # Return the Python executable path
            if platform.system() == "Windows":
                return venv_dir / "Scripts" / "python.exe"
            else:
                return venv_dir / "bin" / "python"
        except subprocess.CalledProcessError as e:
            self.log(f"[ERROR] Failed to create virtual environment: {e}")
            raise
        except subprocess.TimeoutExpired:
            self.log("[ERROR] Virtual environment creation timed out")
            raise

    def repair_virtual_environment(self):
        """Repair or recreate broken virtual environment"""
        self.log("🔧 Repairing virtual environment...")

        # Remove broken virtual environment
        venv_path = Path("venv")
        if venv_path.exists():
            self.log("🗑️ Removing broken virtual environment...")
            try:
                import shutil
                shutil.rmtree(venv_path)
                self.log("✅ Broken virtual environment removed")
            except Exception as e:
                self.log(f"⚠️ Could not remove broken venv: {e}")
                # Try to continue anyway

        # Create new virtual environment
        return self.create_virtual_environment()

    def start_application(self):
        """Start the Flask application"""
        def run_app():
            try:
                self.update_status("Starting application...", "orange")
                self.progress.start()
                self.start_button.config(state='disabled')

                self.log("[INFO] Checking Python environment...")

                # Validate and get Python executable
                python_exe = self.validate_virtual_environment()

                # If no virtual environment is available, try to create one
                if python_exe is None:
                    self.log("[INFO] No virtual environment found, attempting to create one...")
                    try:
                        python_exe = self.create_virtual_environment()
                        self.log(f"[SUCCESS] Using newly created virtual environment Python: {python_exe}")
                    except Exception as e:
                        self.log(f"[WARNING] Failed to create virtual environment: {e}")
                        python_exe = sys.executable
                        self.log(f"[INFO] Using system Python: {python_exe}")
                        self.log("[INFO] Will attempt to install packages with --user flag")
                else:
                    self.log(f"[SUCCESS] Using virtual environment Python: {python_exe}")

                # Install dependencies with robust error handling
                self.log("[INSTALL] Installing dependencies...")

                # Initialize variables
                pip_upgrade_success = False

                # Check if we're using a virtual environment or system Python
                home_venv_path = Path.home() / ".venvs" / "adhoc-log-app"
                using_home_venv = str(python_exe).startswith(str(home_venv_path))

                if using_home_venv:
                    self.log("[INFO] Using home directory virtual environment - proceeding with pip operations")
                    # Upgrade pip with multiple fallback strategies
                    pip_commands = [
                        [str(python_exe), "-m", "pip", "install", "--upgrade", "pip"],
                        [str(python_exe), "-m", "pip", "install", "--trusted-host", "pypi.org",
                         "--trusted-host", "pypi.python.org", "--trusted-host", "files.pythonhosted.org",
                         "--upgrade", "pip"]
                    ]
                else:
                    self.log("[INFO] Using system Python - will use --user flag for installations")
                    # For system Python, use --user flag to avoid permission issues
                    pip_commands = [
                        [str(python_exe), "-m", "pip", "install", "--user", "--upgrade", "pip"],
                        [str(python_exe), "-m", "pip", "install", "--user", "--trusted-host", "pypi.org",
                         "--trusted-host", "pypi.python.org", "--trusted-host", "files.pythonhosted.org",
                         "--upgrade", "pip"]
                    ]

                for cmd in pip_commands:
                    try:
                        result = subprocess.run(cmd, capture_output=True, check=True, timeout=120)
                        pip_upgrade_success = True
                        self.log("[SUCCESS] pip upgraded successfully")
                        break
                    except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
                        self.log(f"[WARNING] pip upgrade attempt failed: {' '.join(cmd[4:])}")
                        continue

                if not pip_upgrade_success:
                    self.log("[WARNING] All pip upgrade attempts failed, continuing with existing pip...")

                # Install requirements with fallback strategies
                requirements_success = False

                if using_home_venv:
                    self.log("[INFO] Installing requirements in virtual environment...")
                    req_commands = [
                        [str(python_exe), "-m", "pip", "install", "-r", "requirements.txt"],
                        [str(python_exe), "-m", "pip", "install", "--trusted-host", "pypi.org",
                         "--trusted-host", "pypi.python.org", "--trusted-host", "files.pythonhosted.org",
                         "-r", "requirements.txt"]
                    ]
                else:
                    self.log("[INFO] Installing requirements with --user flag...")
                    req_commands = [
                        [str(python_exe), "-m", "pip", "install", "--user", "-r", "requirements.txt"],
                        [str(python_exe), "-m", "pip", "install", "--user", "--trusted-host", "pypi.org",
                         "--trusted-host", "pypi.python.org", "--trusted-host", "files.pythonhosted.org",
                         "-r", "requirements.txt"]
                    ]

                for cmd in req_commands:
                    try:
                        result = subprocess.run(cmd, capture_output=True, check=True, timeout=300)
                        requirements_success = True
                        self.log("[SUCCESS] Dependencies installed successfully")
                        break
                    except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
                        self.log(f"[WARNING] Requirements install attempt failed")
                        if hasattr(e, 'stderr') and e.stderr:
                            self.log(f"Error: {e.stderr.decode()[:200]}...")
                        continue

                if not requirements_success:
                    self.log("[WARNING] Installing packages individually...")
                    # Try installing packages one by one
                    try:
                        with open("requirements.txt", "r") as f:
                            for line in f:
                                package = line.strip()
                                if package and not package.startswith("#"):
                                    try:
                                        if using_home_venv:
                                            cmd = [str(python_exe), "-m", "pip", "install",
                                                  "--trusted-host", "pypi.org", "--trusted-host", "pypi.python.org",
                                                  "--trusted-host", "files.pythonhosted.org", package]
                                        else:
                                            cmd = [str(python_exe), "-m", "pip", "install", "--user",
                                                  "--trusted-host", "pypi.org", "--trusted-host", "pypi.python.org",
                                                  "--trusted-host", "files.pythonhosted.org", package]

                                        subprocess.run(cmd, capture_output=True, check=True, timeout=120)
                                        self.log(f"[SUCCESS] Installed {package}")
                                    except Exception:
                                        self.log(f"[WARNING] Failed to install {package}")
                    except Exception as e:
                        self.log(f"[ERROR] Error reading requirements.txt: {e}")

                # Check for macOS tkinter issues
                if platform.system() == "Darwin":
                    try:
                        subprocess.run([str(python_exe), "-c", "import tkinter; print('tkinter test passed')"],
                                     capture_output=True, check=True)
                        self.log("[SUCCESS] tkinter is working correctly")
                    except subprocess.CalledProcessError:
                        self.log("[WARNING] tkinter may not be available in virtual environment")
                        self.log("[INFO] Consider using system Python or installing python-tk via Homebrew")

                # Create sample data if needed
                data_dir = Path("data")
                if not data_dir.exists() or not any(data_dir.glob("tasks_*.json")):
                    self.log("📝 Creating sample data...")
                    subprocess.run([str(python_exe), "utils/create_sample_data.py"],
                                  capture_output=True, check=True)
                    self.log("✅ Sample data created")

                # Find available port
                port = self.find_available_port()
                self.server_url = f"http://127.0.0.1:{port}"

                self.log(f"🚀 Starting server on port {port}...")

                # Start Flask app
                env = os.environ.copy()
                env['FLASK_RUN_PORT'] = str(port)

                # Create a startup script to ensure proper environment
                startup_script = f"""
import sys
import os
sys.path.insert(0, os.getcwd())
os.environ['FLASK_RUN_PORT'] = '{port}'

try:
    from app import app
    print(f'Starting Flask app on port {port}...')
    app.run(debug=False, host='0.0.0.0', port={port}, use_reloader=False)
except Exception as e:
    print(f'Error starting Flask app: {{e}}')
    import traceback
    traceback.print_exc()
"""

                self.process = subprocess.Popen([
                    str(python_exe), "-c", startup_script
                ], env=env, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                    universal_newlines=True, bufsize=1, cwd=os.getcwd())

                self.update_status(f"Running on {self.server_url}", "green")
                self.stop_button.config(state='normal')
                self.browser_button.config(state='normal')
                self.progress.stop()

                self.log(f"✅ Application started successfully!")
                self.log(f"🌐 Open your browser to: {self.server_url}")
                self.log("🛑 Click 'Stop Application' to stop the server")

                # Auto-open browser
                webbrowser.open(self.server_url)

            except Exception as e:
                self.progress.stop()
                self.start_button.config(state='normal')
                self.update_status(f"Error: {str(e)}", "red")
                self.log(f"❌ Error starting application: {str(e)}")

                # Provide helpful error message based on error type
                error_msg = str(e)
                help_text = ""

                if "No module named" in error_msg:
                    help_text = "\n\n💡 Solution: Try running Setup/Repair from the Utilities section."
                elif "Permission denied" in error_msg:
                    help_text = "\n\n💡 Solution: Check file permissions or run as administrator."
                elif "Address already in use" in error_msg:
                    help_text = "\n\n💡 Solution: Another instance may be running. Try stopping it first."
                elif "tkinter" in error_msg.lower():
                    help_text = "\n\n💡 Solution: Install tkinter support for your Python installation."
                    if platform.system() == "Darwin":
                        help_text += "\n   On macOS: brew install python-tk"
                    elif platform.system() == "Linux":
                        help_text += "\n   On Linux: sudo apt-get install python3-tk"
                else:
                    help_text = "\n\n💡 Try running Diagnostics to identify the issue."

                messagebox.showerror("Application Start Failed",
                                   f"Failed to start application:\n{error_msg}{help_text}")

        # Run in separate thread
        threading.Thread(target=run_app, daemon=True).start()

    def stop_application(self):
        """Stop the Flask application"""
        if self.process:
            self.log("🛑 Stopping application...")
            self.process.terminate()
            self.process = None
            self.update_status("Stopped", "red")
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')
            self.browser_button.config(state='disabled')
            self.log("✅ Application stopped")

    def open_browser(self):
        """Open the application in browser"""
        if self.server_url:
            webbrowser.open(self.server_url)
            self.log(f"🌐 Opened browser to {self.server_url}")

    def run_setup(self):
        """Run comprehensive setup and repair utilities"""
        def setup():
            try:
                self.log("🔧 Running comprehensive setup and repair...")
                self.progress.start()

                # Step 1: Validate/repair virtual environment
                self.log("🔍 Step 1: Checking virtual environment...")
                try:
                    python_exe = self.validate_virtual_environment()
                    if python_exe is None:
                        python_exe = sys.executable
                        self.log(f"🐍 Using system Python: {python_exe}")
                        self.log("💡 SharePoint packages should be available via setup_package_path()")
                    else:
                        self.log(f"📦 Using virtual environment: {python_exe}")
                except Exception as e:
                    self.log(f"❌ Virtual environment check failed: {e}")
                    python_exe = sys.executable
                    self.log(f"🐍 Falling back to system Python: {python_exe}")

                # Step 2: Upgrade pip (skip if using system Python in SharePoint mode)
                self.log("🔍 Step 2: Checking pip...")
                if python_exe == sys.executable and any(venv_dir.exists() for venv_dir in
                    [Path(f"venvs/user_{getpass.getuser()}_{platform.system()}_{platform.machine()}")]):
                    self.log("🌐 SharePoint mode - skipping pip operations")
                    pip_success = True
                else:
                    pip_success = False
                    pip_commands = [
                        [str(python_exe), "-m", "pip", "install", "--upgrade", "pip"],
                        [str(python_exe), "-m", "pip", "install", "--trusted-host", "pypi.org",
                         "--trusted-host", "pypi.python.org", "--trusted-host", "files.pythonhosted.org",
                         "--upgrade", "pip"]
                    ]

                    for cmd in pip_commands:
                        try:
                            subprocess.run(cmd, check=True, capture_output=True, timeout=120)
                            self.log("✅ pip upgraded successfully")
                            pip_success = True
                            break
                        except Exception:
                            continue

                if not pip_success:
                    self.log("⚠️ pip upgrade failed, continuing with existing version...")

                # Step 3: Install/update dependencies (skip if SharePoint mode)
                self.log("🔍 Step 3: Installing dependencies...")
                if python_exe == sys.executable and any(venv_dir.exists() for venv_dir in
                    [Path(f"venvs/user_{getpass.getuser()}_{platform.system()}_{platform.machine()}")]):
                    self.log("🌐 SharePoint mode - packages should already be available")
                    req_success = True
                else:
                    req_success = False
                    req_commands = [
                        [str(python_exe), "-m", "pip", "install", "-r", "requirements.txt"],
                        [str(python_exe), "-m", "pip", "install", "--trusted-host", "pypi.org",
                         "--trusted-host", "pypi.python.org", "--trusted-host", "files.pythonhosted.org",
                         "-r", "requirements.txt"]
                    ]

                    for cmd in req_commands:
                        try:
                            subprocess.run(cmd, check=True, capture_output=True, timeout=300)
                            self.log("✅ Dependencies installed successfully")
                            req_success = True
                            break
                        except Exception:
                            continue

                if not req_success:
                    self.log("⚠️ Installing packages individually...")
                    try:
                        with open("requirements.txt", "r") as f:
                            for line in f:
                                package = line.strip()
                                if package and not package.startswith("#"):
                                    try:
                                        subprocess.run([str(python_exe), "-m", "pip", "install", package],
                                                     check=True, capture_output=True, timeout=120)
                                        self.log(f"✅ Installed {package}")
                                    except Exception:
                                        self.log(f"⚠️ Failed to install {package}")
                    except Exception as e:
                        self.log(f"❌ Error reading requirements.txt: {e}")

                # Step 4: Create data directory if missing
                self.log("🔍 Step 4: Checking data directory...")
                data_dir = Path("data")
                if not data_dir.exists():
                    data_dir.mkdir(exist_ok=True)
                    self.log("✅ Data directory created")
                else:
                    self.log("✅ Data directory exists")

                # Step 5: Test Flask installation
                self.log("🔍 Step 5: Testing Flask installation...")
                try:
                    result = subprocess.run([str(python_exe), "-c", "import flask; print(f'Flask {flask.__version__} ready')"],
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        self.log(f"✅ {result.stdout.strip()}")
                    else:
                        self.log("⚠️ Flask test failed")
                except Exception:
                    self.log("⚠️ Flask test failed")

                # Step 6: Run external setup script if available
                if Path("scripts/setup_missing_files.sh").exists():
                    self.log("🔍 Step 6: Running additional setup script...")
                    try:
                        result = subprocess.run(["bash", "scripts/setup_missing_files.sh"],
                                              capture_output=True, text=True, timeout=60)
                        if result.returncode == 0:
                            self.log("✅ Additional setup completed")
                            if result.stdout:
                                self.log(result.stdout)
                        else:
                            self.log("⚠️ Additional setup had issues")
                    except Exception as e:
                        self.log(f"⚠️ Additional setup failed: {e}")

                self.progress.stop()
                self.log("🎉 Comprehensive setup and repair completed!")
                messagebox.showinfo("Setup Complete",
                                  "Comprehensive setup and repair completed successfully!\n\n" +
                                  "✅ Virtual environment validated/repaired\n" +
                                  "✅ Dependencies installed/updated\n" +
                                  "✅ Data directory ready\n" +
                                  "✅ Flask installation tested\n\n" +
                                  "You can now start the application.")

            except Exception as e:
                self.progress.stop()
                self.log(f"❌ Setup error: {str(e)}")

                # Provide specific guidance based on error type
                error_msg = str(e)
                if "No module named 'venv'" in error_msg:
                    help_msg = "Virtual environment module not available.\n\nSolution: Install Python with venv support or use system Python."
                elif "Permission denied" in error_msg:
                    help_msg = "Permission denied during setup.\n\nSolution: Run with administrator privileges or check file permissions."
                elif "SSL" in error_msg or "certificate" in error_msg.lower():
                    help_msg = "SSL/Certificate error (common in corporate networks).\n\nSolution: Setup configures trusted hosts automatically."
                elif "timeout" in error_msg.lower():
                    help_msg = "Network timeout during package installation.\n\nSolution: Check internet connection and try again."
                elif "No such file or directory" in error_msg and "python" in error_msg.lower():
                    help_msg = "Python executable issue detected.\n\nSolution: Virtual environment will be recreated automatically."
                else:
                    help_msg = f"Setup failed: {error_msg}\n\nTry running diagnostics to identify the specific issue."

                messagebox.showerror("Setup Error", help_msg)

        threading.Thread(target=setup, daemon=True).start()

    def run_diagnostics(self):
        """Run system diagnostics"""
        def diagnose():
            try:
                self.log("🩺 Running diagnostics...")
                self.progress.start()

                # Check Python
                self.log(f"Python version: {sys.version}")

                # Check virtual environment
                venv_exists = Path("venv").exists()
                self.log(f"Virtual environment: {'✅ Found' if venv_exists else '❌ Missing'}")

                # Check requirements
                try:
                    import flask
                    self.log(f"Flask: ✅ {flask.__version__}")
                except ImportError:
                    self.log("Flask: ❌ Not installed")

                # Check data directory
                data_dir = Path("data")
                if data_dir.exists():
                    task_files = list(data_dir.glob("tasks_*.json"))
                    self.log(f"Data files: ✅ {len(task_files)} found")
                else:
                    self.log("Data directory: ❌ Missing")

                # Check ports
                port = self.find_available_port()
                self.log(f"Available port: ✅ {port}")

                self.progress.stop()
                self.log("🩺 Diagnostics completed")
                messagebox.showinfo("Diagnostics", "Diagnostics completed. Check logs for details.")

            except Exception as e:
                self.progress.stop()
                self.log(f"❌ Diagnostics error: {str(e)}")

        threading.Thread(target=diagnose, daemon=True).start()

    def create_sample_data(self):
        """Create sample data"""
        def create_data():
            try:
                self.log("📊 Creating sample data...")
                self.progress.start()

                # Get Python executable (prefer SharePoint venv, fallback to system)
                python_exe = self.validate_virtual_environment()
                if python_exe is None:
                    python_exe = sys.executable
                    self.log(f"🐍 Using system Python for sample data creation: {python_exe}")
                else:
                    self.log(f"📦 Using virtual environment Python for sample data creation: {python_exe}")

                result = subprocess.run([str(python_exe), "utils/create_sample_data.py"],
                                      capture_output=True, text=True)

                if result.returncode == 0:
                    self.log("✅ Sample data created successfully")
                    messagebox.showinfo("Success", "Sample data created successfully!")
                else:
                    self.log(f"❌ Error creating sample data: {result.stderr}")
                    messagebox.showerror("Error", f"Failed to create sample data:\n{result.stderr}")

                self.progress.stop()

            except Exception as e:
                self.progress.stop()
                self.log(f"❌ Error: {str(e)}")
                messagebox.showerror("Error", f"Failed to create sample data:\n{str(e)}")

        threading.Thread(target=create_data, daemon=True).start()

    def open_data_folder(self):
        """Open the data folder in file explorer"""
        data_dir = Path("data")
        if data_dir.exists():
            if platform.system() == "Windows":
                subprocess.run(["explorer", str(data_dir)])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", str(data_dir)])
            else:  # Linux
                subprocess.run(["xdg-open", str(data_dir)])
            self.log(f"📁 Opened data folder: {data_dir.absolute()}")
        else:
            messagebox.showwarning("Not Found", "Data folder does not exist yet.")

    def clear_data(self):
        """Clear all data files with confirmation"""
        data_path = Path("data")

        if not data_path.exists():
            messagebox.showinfo("Info", "No data folder exists yet.\nNothing to clear.")
            self.log("ℹ️ No data to clear")
            return

        # Count existing data files
        data_files = list(data_path.glob("tasks_*.json"))
        if not data_files:
            messagebox.showinfo("Info", "No data files found.\nNothing to clear.")
            self.log("ℹ️ No data files to clear")
            return

        # Show warning dialog
        warning_message = f"""⚠️ WARNING: This will permanently delete ALL your task data!

Found {len(data_files)} data file(s):
{chr(10).join([f"• {f.name}" for f in data_files[:5]])}
{'• ... and more' if len(data_files) > 5 else ''}

This action CANNOT be undone!

Are you sure you want to continue?"""

        result = messagebox.askyesno(
            "⚠️ Confirm Data Deletion",
            warning_message,
            icon='warning'
        )

        if not result:
            self.log("❌ Data clearing cancelled by user")
            return

        # Second confirmation for safety
        final_confirmation = messagebox.askyesno(
            "⚠️ Final Confirmation",
            "This is your FINAL WARNING!\n\nAll task data will be permanently deleted.\n\nProceed with deletion?",
            icon='warning'
        )

        if not final_confirmation:
            self.log("❌ Data clearing cancelled at final confirmation")
            return

        try:
            # Delete all data files
            deleted_count = 0
            for data_file in data_files:
                data_file.unlink()
                deleted_count += 1
                self.log(f"🗑️ Deleted: {data_file.name}")

            # Remove data directory if empty
            if not any(data_path.iterdir()):
                data_path.rmdir()
                self.log("🗑️ Removed empty data directory")

            messagebox.showinfo(
                "✅ Data Cleared",
                f"Successfully deleted {deleted_count} data file(s).\n\nThe application will create fresh sample data when you next run it."
            )
            self.log(f"✅ Successfully cleared {deleted_count} data files")

        except Exception as e:
            error_msg = f"Failed to clear data: {str(e)}"
            messagebox.showerror("Error", error_msg)
            self.log(f"❌ {error_msg}")

    def backup_data(self):
        """Create a backup of all data files"""
        data_path = Path("data")

        if not data_path.exists():
            messagebox.showinfo("Info", "No data folder exists yet.\nNothing to backup.")
            self.log("ℹ️ No data to backup")
            return

        # Count existing data files
        data_files = list(data_path.glob("*.json"))
        if not data_files:
            messagebox.showinfo("Info", "No data files found.\nNothing to backup.")
            self.log("ℹ️ No data files to backup")
            return

        def create_backup():
            try:
                self.log("💾 Creating data backup...")
                self.progress.start()

                # Create backup directory with timestamp
                timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
                backup_root = Path("backups")
                backup_dir = backup_root / f"backup_{timestamp}"

                # Create backup directories
                backup_root.mkdir(exist_ok=True)
                backup_dir.mkdir(exist_ok=True)

                self.log(f"📁 Created backup directory: {backup_dir}")

                # Copy all data files
                copied_count = 0
                for data_file in data_files:
                    try:
                        destination = backup_dir / data_file.name
                        shutil.copy2(data_file, destination)
                        copied_count += 1
                        self.log(f"💾 Backed up: {data_file.name}")
                    except Exception as e:
                        self.log(f"⚠️ Failed to backup {data_file.name}: {e}")

                # Create backup info file
                info_file = backup_dir / "backup_info.txt"
                with open(info_file, 'w', encoding='utf-8') as f:
                    f.write(f"AdhocLog Data Backup\n")
                    f.write(f"Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"Platform: {platform.system()} {platform.release()}\n")
                    f.write(f"Files backed up: {copied_count}\n")
                    f.write(f"Source directory: {data_path.absolute()}\n\n")
                    f.write("Files included:\n")
                    for data_file in data_files:
                        if (backup_dir / data_file.name).exists():
                            f.write(f"  ✅ {data_file.name}\n")
                        else:
                            f.write(f"  ❌ {data_file.name} (failed to copy)\n")

                self.progress.stop()

                message = f"""✅ Backup completed successfully!

📁 Backup location: {backup_dir}
📊 Files backed up: {copied_count} out of {len(data_files)}
🕐 Created: {timestamp.replace('_', ' at ').replace('-', '/')}

Your data is now safely backed up and can be restored if needed."""

                messagebox.showinfo("✅ Backup Complete", message)
                self.log(f"✅ Successfully created backup with {copied_count} files")

                # Ask if user wants to open backup folder
                if messagebox.askyesno("Open Backup Folder", "Would you like to open the backup folder to verify the files?"):
                    self.open_backup_folder(backup_dir)

            except Exception as e:
                self.progress.stop()
                error_msg = f"Failed to create backup: {str(e)}"
                messagebox.showerror("❌ Backup Error", error_msg)
                self.log(f"❌ {error_msg}")

        # Show backup confirmation
        backup_message = f"""💾 Create Data Backup

This will create a timestamped backup of all your data files.

📊 Found {len(data_files)} data file(s):
{chr(10).join([f"• {f.name}" for f in data_files[:10]])}
{'• ... and more' if len(data_files) > 10 else ''}

📁 Backup will be saved to: backups/backup_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}/

This is a safe operation that will not modify your existing data.

Continue with backup?"""

        result = messagebox.askyesno("💾 Create Backup", backup_message)

        if result:
            threading.Thread(target=create_backup, daemon=True).start()
        else:
            self.log("❌ Backup cancelled by user")

    def restore_data(self):
        """Restore data from a backup"""
        backup_root = Path("backups")

        if not backup_root.exists() or not any(backup_root.iterdir()):
            messagebox.showinfo("Info", "No backups found.\nCreate a backup first before you can restore data.")
            self.log("ℹ️ No backups available for restore")
            return

        # Get available backups
        backup_dirs = [d for d in backup_root.iterdir() if d.is_dir() and d.name.startswith("backup_")]

        if not backup_dirs:
            messagebox.showinfo("Info", "No valid backup directories found.")
            self.log("ℹ️ No valid backup directories found")
            return

        # Sort backups by timestamp (newest first)
        backup_dirs.sort(key=lambda x: x.name, reverse=True)

        # Variables to store user selections
        selected_backup = None
        restore_mode = None

        def perform_restore():
            try:
                if not selected_backup or not restore_mode:
                    messagebox.showerror("Error", "Invalid backup or restore mode selected.")
                    return

                self.log(f"🔄 Starting restore from {selected_backup.name}")
                self.progress.start()

                # Check what files are in the backup
                backup_files = list(selected_backup.glob("*.json"))

                if not backup_files:
                    self.progress.stop()
                    messagebox.showerror("Error", f"No data files found in backup:\n{selected_backup}")
                    return

                # Create data directory if it doesn't exist
                data_path = Path("data")
                data_path.mkdir(exist_ok=True)

                # Handle existing data based on user choice
                existing_files = list(data_path.glob("*.json"))

                if existing_files and restore_mode == "replace":
                    # Delete existing data files
                    for existing_file in existing_files:
                        try:
                            existing_file.unlink()
                            self.log(f"🗑️ Removed existing: {existing_file.name}")
                        except Exception as e:
                            self.log(f"⚠️ Could not remove {existing_file.name}: {e}")

                # Copy backup files to data directory
                restored_count = 0
                for backup_file in backup_files:
                    try:
                        destination = data_path / backup_file.name

                        if destination.exists() and restore_mode == "merge":
                            # For merge mode, skip if file already exists
                            self.log(f"⏭️ Skipped existing: {backup_file.name}")
                            continue

                        shutil.copy2(backup_file, destination)
                        restored_count += 1
                        self.log(f"🔄 Restored: {backup_file.name}")
                    except Exception as e:
                        self.log(f"⚠️ Failed to restore {backup_file.name}: {e}")

                self.progress.stop()

                message = f"""✅ Restore completed successfully!

📁 Restored from: {selected_backup.name}
📊 Files restored: {restored_count} out of {len(backup_files)}
🔄 Mode: {restore_mode.title()}

Your data has been restored. You may need to restart the application to see the changes."""

                messagebox.showinfo("✅ Restore Complete", message)
                self.log(f"✅ Successfully restored {restored_count} files from {selected_backup.name}")

            except Exception as e:
                self.progress.stop()
                error_msg = f"Failed to restore data: {str(e)}"
                messagebox.showerror("❌ Restore Error", error_msg)
                self.log(f"❌ {error_msg}")

        # Create backup selection dialog
        backup_dialog = tk.Toplevel(self.root)
        backup_dialog.title("🔄 Restore Data")
        backup_dialog.geometry("600x500")
        backup_dialog.resizable(True, True)
        backup_dialog.transient(self.root)
        backup_dialog.grab_set()

        # Center the dialog
        backup_dialog.update_idletasks()
        x = (backup_dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (backup_dialog.winfo_screenheight() // 2) - (500 // 2)
        backup_dialog.geometry(f"600x500+{x}+{y}")

        main_frame = ttk.Frame(backup_dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(main_frame, text="🔄 Restore Data from Backup",
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 10))

        # Instructions
        instructions = ttk.Label(main_frame,
            text="Select a backup to restore and choose how to handle existing data:",
            font=('Arial', 10))
        instructions.pack(pady=(0, 10))

        # Backup selection frame
        selection_frame = ttk.LabelFrame(main_frame, text="Available Backups", padding="10")
        selection_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Listbox with scrollbar for backup selection
        listbox_frame = ttk.Frame(selection_frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True)

        backup_listbox = tk.Listbox(listbox_frame, height=8)
        scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=backup_listbox.yview)
        backup_listbox.configure(yscrollcommand=scrollbar.set)

        backup_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Populate backup list with detailed info
        backup_info = []
        for backup_dir in backup_dirs:
            timestamp_str = backup_dir.name.replace("backup_", "").replace("_", " at ").replace("-", "/")

            # Read backup info if available
            info_file = backup_dir / "backup_info.txt"
            file_count = len(list(backup_dir.glob("*.json")))

            if info_file.exists():
                try:
                    with open(info_file, 'r', encoding='utf-8') as f:
                        info_content = f.read()
                        # Extract file count from info
                        for line in info_content.split('\n'):
                            if line.startswith('Files backed up:'):
                                file_count = line.split(':')[1].strip()
                                break
                except:
                    pass

            display_text = f"{timestamp_str} ({file_count} files)"
            backup_listbox.insert(tk.END, display_text)
            backup_info.append(backup_dir)

        # Select the most recent backup by default
        if backup_info:
            backup_listbox.selection_set(0)

        # Restore mode selection
        mode_frame = ttk.LabelFrame(main_frame, text="Restore Mode", padding="10")
        mode_frame.pack(fill=tk.X, pady=(0, 10))

        restore_var = tk.StringVar(value="replace")

        replace_radio = ttk.Radiobutton(mode_frame, text="🔄 Replace existing data",
                                       variable=restore_var, value="replace")
        replace_radio.pack(anchor=tk.W, pady=2)

        merge_radio = ttk.Radiobutton(mode_frame, text="🔗 Merge with existing data",
                                     variable=restore_var, value="merge")
        merge_radio.pack(anchor=tk.W, pady=2)

        # Mode descriptions
        desc_frame = ttk.Frame(mode_frame)
        desc_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(desc_frame, text="• Replace: Delete all existing data and restore from backup",
                 font=('Arial', 9), foreground='gray').pack(anchor=tk.W)
        ttk.Label(desc_frame, text="• Merge: Keep existing data and add backup files (skip duplicates)",
                 font=('Arial', 9), foreground='gray').pack(anchor=tk.W)

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        def on_restore():
            nonlocal selected_backup, restore_mode
            selection = backup_listbox.curselection()
            if not selection:
                messagebox.showwarning("No Selection", "Please select a backup to restore.")
                return

            selected_backup = backup_info[selection[0]]
            restore_mode = restore_var.get()

            # Show confirmation
            mode_text = "delete all existing data and replace it" if restore_mode == "replace" else "merge with existing data"

            confirm_msg = f"""⚠️ Confirm Restore Operation

📁 Backup: {selected_backup.name}
🔄 Mode: {restore_mode.title()} - {mode_text}

Are you sure you want to proceed?"""

            if messagebox.askyesno("⚠️ Confirm Restore", confirm_msg):
                backup_dialog.destroy()
                threading.Thread(target=perform_restore, daemon=True).start()

        def on_cancel():
            backup_dialog.destroy()
            self.log("❌ Restore cancelled by user")

        ttk.Button(button_frame, text="🔄 Restore", command=on_restore).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="❌ Cancel", command=on_cancel).pack(side=tk.RIGHT)

    def open_backup_folder(self, backup_dir):
        """Open a specific backup folder in file explorer"""
        try:
            if platform.system() == "Windows":
                subprocess.run(["explorer", str(backup_dir)])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", str(backup_dir)])
            else:  # Linux
                subprocess.run(["xdg-open", str(backup_dir)])
            self.log(f"📁 Opened backup folder: {backup_dir}")
        except Exception as e:
            self.log(f"⚠️ Could not open backup folder: {e}")

    def on_closing(self):
        """Handle window closing"""
        if self.process:
            if messagebox.askokcancel("Quit", "Application is running. Stop it and quit?"):
                self.stop_application()
                self.root.destroy()
        else:
            self.root.destroy()

    def run(self):
        """Run the GUI"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

def main():
    """Main function"""
    try:
        launcher = TaskTrackerLauncher()
        launcher.run()
    except Exception as e:
        messagebox.showerror("Error", f"Failed to start launcher:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
