#!/bin/bash

# AdhocLog - GUI Launcher Script
# Handles macOS tkinter issues and virtual environment setup

# Change to the script's directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "🚀 AdhocLog - GUI Launcher"
echo "=========================="

# Set environment variables for macOS tkinter compatibility
export TK_SILENCE_DEPRECATION=1

# Function to test if tkinter works with a given Python
test_tkinter() {
    local python_cmd="$1"
    "$python_cmd" -c "
import tkinter as tk
try:
    root = tk.Tk()
    root.withdraw()
    root.destroy()
    print('tkinter_works')
except Exception:
    print('tkinter_error')
" 2>/dev/null | grep -q "tkinter_works"
}

# Function to find best Python for GUI
find_gui_python() {
    echo "🔍 Finding best Python for GUI..." >&2

    # Check if virtual environment exists and has required packages
    if [ -f "venv/bin/python" ]; then
        echo "📦 Checking virtual environment..." >&2
        if venv/bin/python -c "import pandas, openpyxl" 2>/dev/null; then
            echo "✅ Virtual environment has required packages" >&2
            if test_tkinter "venv/bin/python"; then
                echo "✅ Virtual environment Python works with tkinter" >&2
                echo "venv/bin/python"
                return 0
            else
                echo "⚠️ Virtual environment Python has tkinter issues" >&2
            fi
        else
            echo "⚠️ Virtual environment missing required packages" >&2
        fi
    fi

    # Check system Python candidates
    for python_cmd in python3 python /opt/homebrew/bin/python3 /usr/local/bin/python3; do
        if command -v "$python_cmd" >/dev/null 2>&1; then
            echo "🔍 Testing: $python_cmd" >&2
            if test_tkinter "$python_cmd"; then
                echo "✅ Found working GUI Python: $python_cmd" >&2
                echo "$python_cmd"
                return 0
            fi
        fi
    done

    echo "❌ No suitable Python found for GUI" >&2
    return 1
}

# Find the best Python for GUI
GUI_PYTHON=$(find_gui_python)
if [ $? -ne 0 ]; then
    echo "❌ Cannot start GUI - no compatible Python found"
    echo ""
    echo "💡 Troubleshooting:"
    echo "• On macOS, try: brew install python-tk"
    echo "• Or use the command line version: python run.py"
    exit 1
fi

echo "🎯 Using Python: $GUI_PYTHON"

# If using system Python, ensure it can access virtual environment packages
if [[ "$GUI_PYTHON" != "venv/bin/python" ]]; then
    echo "⚙️ Using system Python - will configure package access"

    # Check if we need to install packages on system Python
    if ! $GUI_PYTHON -c "import pandas, openpyxl" 2>/dev/null; then
        echo "📦 Installing required packages for system Python..."
        $GUI_PYTHON -m pip install pandas openpyxl || {
            echo "❌ Failed to install packages on system Python"
            echo "💡 Try: pip3 install pandas openpyxl"
            echo "💡 Or run: ./launch_app.sh and choose Setup/Repair"
            exit 1
        }
    fi
fi

echo "🖥️ Starting GUI Launcher..."
echo "💡 Tip: If you see a black screen, try resizing the window"

# Start the GUI launcher
$GUI_PYTHON gui_launcher.py

echo "👋 GUI Launcher closed"
