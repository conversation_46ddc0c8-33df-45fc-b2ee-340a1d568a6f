# AdhocLog - Import Functionality Guide

## 📥 Excel/CSV Import Feature

The AdhocLog application now supports importing tasks from Excel (.xlsx, .xls) and CSV (.csv) files through the GUI launcher.

## 🚀 How to Use

### 1. Access Import Feature
- Launch the AdhocLog GUI launcher: `python gui_launcher.py`
- In the "Utilities" section, click **"📥 Import Excel/CSV"**

### 2. Select File
- Choose your Excel or CSV file using the file dialog
- Supported formats: `.xlsx`, `.xls`, `.csv`

### 3. Review Preview
- The system will automatically detect column headers
- Preview shows the first 5 tasks that will be imported
- Column mapping is displayed for verification

### 4. Confirm Import
- Click **"📥 Import Tasks"** to proceed
- Tasks are added to your existing data (not replaced)

## 📊 Supported Column Headers

The import system automatically detects these column headers (case-insensitive):

| Field | Supported Headers |
|-------|------------------|
| **Date** | `Date`, `Task Date`, `Date Created`, `Created Date` |
| **Team Member** | `Team Member`, `Member`, `Employee`, `User`, `Person` |
| **Title** | `Task Title`, `Title`, `Task`, `Task Name`, `Name` |
| **Classification** | `Classification`, `Type`, `Task Type`, `Task Classification` |
| **Description** | `Actions Taken / Description`, `Description`, `Actions Taken`, `Actions`, `Details`, `Notes`, `Comments` |
| **Time** | `Estimated Time (minute)`, `Estimated Time`, `Est Time`, `Time`, `Minutes`, `Duration` |
| **Category** | `Category`, `Task Category`, `Main Category` |

### ⚠️ Important Notes

- **Team Member Override**: The team member field from your file is ignored - all imported tasks will be assigned to the current user
- **Date Flexibility**: Multiple date formats are supported (YYYY-MM-DD, MM/DD/YYYY, DD/MM/YYYY, etc.)
- **Classification Mapping**: Classifications are automatically mapped to categories using the system's predefined rules

## 📝 File Format Examples

### CSV Example
```csv
Date,Team Member,Task Title,Classification,Actions Taken / Description,Estimated Time (minute),Category
2025-07-28,John Doe,Daily Stand-up,Business Support Activities,Attended daily standup meeting,15,Business Support Activities
2025-07-29,Jane Smith,Code Review,Execution,Reviewed pull requests,60,Adhoc
2025-07-30,Bob Johnson,Planning Session,Planning,Created project timeline,90,Adhoc
```

### Excel Example
Same structure as CSV but in Excel format with headers in the first row.

## 🔧 Technical Requirements

### Dependencies
The import feature requires these Python packages:
- `pandas >= 1.5.0` - Data processing
- `openpyxl >= 3.0.0` - Excel file support

### Installation
These packages are automatically installed when you:
1. Use the **"🔧 Setup/Repair"** utility in the GUI launcher
2. Or manually run: `pip install pandas openpyxl`

## 🎯 Data Processing Rules

### Date Processing
- Supports multiple date formats automatically
- Invalid dates default to current date
- Output format: YYYY-MM-DD

### Time Processing
- Accepts numbers (assumed minutes) or text with time units
- "2 hours" becomes 120 minutes
- "45 min" becomes 45 minutes
- Invalid values default to 30 minutes

### Classification Validation
- Automatically maps to valid system classifications:
  - `Planning` → Adhoc
  - `Offline Processing` → Adhoc
  - `Execution` → Adhoc
  - `Business Support Activities` → Business Support Activities
  - `Operational Project Involvement` → Adhoc
- Invalid classifications default to "Planning"

### Text Cleaning
- Removes extra whitespace
- Limits title length to 200 characters
- Limits description length to 1000 characters

## 🚨 Troubleshooting

### Import Fails with "Missing Dependencies"
**Solution:** Run the Setup/Repair utility to install required packages.

### "Could not identify any recognizable columns"
**Possible causes:**
- Column headers don't match expected patterns
- File has no headers
- File is empty or corrupted

**Solutions:**
- Ensure your file has headers in the first row
- Check that column names match the supported patterns (case doesn't matter)
- Verify file is not corrupted

### Import succeeds but no tasks appear
**Possible causes:**
- All rows had empty titles
- Data processing failed for all rows

**Solutions:**
- Ensure your file has task titles/names
- Check that data is in the expected format

### Wrong team member assignments
**This is expected behavior** - all imported tasks are automatically assigned to the current user for security/isolation.

## 🔍 Testing Import Feature

Use the test script to verify import functionality:
```bash
python test_import.py
```

This creates sample files and tests the import process.

## 💡 Best Practices

### File Preparation
1. **Include headers**: First row should contain column headers
2. **Use clear naming**: Column headers should be descriptive
3. **Consistent dates**: Use consistent date formats throughout
4. **Valid classifications**: Use classifications that match the system's predefined list

### Data Quality
1. **Required fields**: Ensure each row has at least a task title
2. **Reasonable time estimates**: Use realistic time estimates in minutes
3. **Meaningful descriptions**: Include useful task descriptions
4. **Clean data**: Remove empty rows and clean up text

### Import Strategy
1. **Small batches**: For large datasets, consider importing in smaller batches
2. **Backup first**: Create a backup before importing large datasets
3. **Preview carefully**: Review the preview to ensure data mapping is correct
4. **Test import**: Use test files to familiarize yourself with the process

## 🔗 Related Features

- **📊 Sample Data**: Create sample data to understand the expected format
- **💾 Backup Data**: Backup your data before large imports
- **📁 Open Data Folder**: View your imported data files directly
- **🔧 Setup/Repair**: Install required dependencies for import functionality

## 📞 Support

If you encounter issues with the import feature:
1. Check the troubleshooting section above
2. Run the diagnostics utility
3. Review the logs in the GUI launcher
4. Ensure all dependencies are properly installed
