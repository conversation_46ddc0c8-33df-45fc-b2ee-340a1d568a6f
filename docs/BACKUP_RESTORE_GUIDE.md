# AdhocLog Backup & Restore Documentation

## Overview

AdhocLog now includes comprehensive backup and restore functionality directly in the GUI launcher. This feature ensures your task data is safely backed up and can be restored when needed.

## Features

### 🛡️ Data Protection
- **Automatic timestamped backups** - Each backup is saved with a unique timestamp
- **Complete data preservation** - All JSON files in the data folder are included
- **Backup verification** - Detailed backup info file created with each backup
- **Cross-platform compatibility** - Works on Windows, macOS, and Linux

### 💾 Backup Functionality

#### What Gets Backed Up
- User task files (`tasks_[username].json`)
- Archived task files (`archived_tasks_[username].json`)
- Template files (`templates_[username].json`)
- Analytics data (`analytics.json`)
- User events (`user_events.json`)
- Enhanced keywords (`enhanced_keywords.json`)
- Any other JSON files in the data directory

#### Backup Process
1. Click the **"💾 Backup Data"** button in the GUI launcher
2. Review the files to be backed up
3. Confirm the backup operation
4. Backup is created in `backups/backup_[timestamp]/` directory
5. Option to open backup folder for verification

#### Backup Structure
```
backups/
├── backup_2025-01-28_14-30-15/
│   ├── backup_info.txt          # Backup metadata
│   ├── tasks_username.json      # User tasks
│   ├── archived_tasks_username.json  # Archived tasks
│   ├── analytics.json           # Analytics data
│   └── [other data files]       # All other JSON files
└── backup_2025-01-28_15-45-22/
    └── [another backup set]
```

### 🔄 Restore Functionality

#### Restore Modes
1. **Replace Mode (🔄 Replace existing data)**
   - Deletes all current data files
   - Restores files from the selected backup
   - Complete replacement of existing data

2. **Merge Mode (🔗 Merge with existing data)**
   - Keeps existing data files
   - Adds backup files that don't already exist
   - Skips files that already exist (no overwrite)

#### Restore Process
1. Click the **"🔄 Restore Data"** button in the GUI launcher
2. Select a backup from the list (sorted by newest first)
3. Choose restore mode (Replace or Merge)
4. Confirm the restore operation
5. Data is restored to the data directory

#### Backup Selection Interface
- **Visual backup list** with timestamps and file counts
- **Detailed information** showing creation date and number of files
- **Preview mode** to see what will be restored
- **Clear confirmation dialogs** to prevent accidental data loss

## Usage Instructions

### Creating Your First Backup

1. **Ensure you have data to backup**
   - Run the application and create some tasks
   - Or use the "📊 Sample Data" button to generate test data

2. **Open the GUI launcher**
   ```bash
   python gui_launcher.py
   ```

3. **Create backup**
   - Click **"💾 Backup Data"** in the Utilities section
   - Review the files to be backed up
   - Click "Continue with backup"
   - Optionally open the backup folder to verify files

### Restoring from a Backup

1. **Open the GUI launcher**
   ```bash
   python gui_launcher.py
   ```

2. **Start restore process**
   - Click **"🔄 Restore Data"** in the Utilities section
   - Browse available backups (sorted newest to oldest)

3. **Select backup and mode**
   - Choose the backup you want to restore
   - Select restore mode:
     - **Replace**: Completely replace current data
     - **Merge**: Add backup files to existing data

4. **Confirm and restore**
   - Review the confirmation dialog
   - Click "🔄 Restore" to proceed
   - Wait for completion message

## Safety Features

### 🔒 Data Protection
- **Double confirmation** for destructive operations
- **Backup before restore** option in replace mode
- **Detailed logs** of all backup/restore operations
- **Error handling** with user-friendly messages

### ⚠️ Important Notes
- **Restart recommendation**: After restoring data, restart the application to see changes
- **Username isolation**: Backups preserve per-user data isolation
- **File permissions**: Ensure write access to the application directory
- **Disk space**: Monitor available disk space for backup storage

## Troubleshooting

### Common Issues

#### "No data to backup"
- **Cause**: No JSON files found in data directory
- **Solution**: Create some tasks or use "📊 Sample Data" button

#### "Permission denied during backup"
- **Cause**: Insufficient write permissions
- **Solution**: Run with appropriate permissions or change directory location

#### "Backup folder not found"
- **Cause**: Backup directory was moved or deleted
- **Solution**: Create new backup using "💾 Backup Data" button

#### "Restore failed"
- **Cause**: Corrupted backup files or permission issues
- **Solution**: Try a different backup or check file permissions

### Recovery Scenarios

#### Complete Data Loss
1. Use **"🔄 Restore Data"** with Replace mode
2. Select your most recent backup
3. Restore all data files

#### Accidental Task Deletion
1. Use **"🔄 Restore Data"** with Merge mode
2. Select a backup from before the deletion
3. Recover specific files without losing recent work

#### Data Corruption
1. Use **"🔄 Restore Data"** with Replace mode
2. Select a known good backup
3. Start fresh with clean data

## Advanced Usage

### Command Line Backup Testing
Use the included test script to verify functionality:
```bash
python test_backup_functionality.py
```

### Manual Backup Management
- Backup location: `backups/` directory
- Each backup is in `backup_[YYYY-MM-DD_HH-MM-SS]/` format
- Safe to manually delete old backups if needed
- Backup info files contain metadata for reference

### Integration with External Tools
- Backup directories can be copied to external storage
- JSON files are human-readable and can be manually inspected
- Compatible with version control systems for additional protection

## Best Practices

### 📅 Regular Backups
- Create backups before major data changes
- Weekly backups for active users
- Before software updates or system changes

### 🗂️ Backup Management
- Keep multiple recent backups
- Archive important milestone backups
- Monitor backup directory size
- Document important backup dates

### 🔄 Restore Testing
- Periodically test restore functionality
- Practice with test data before real emergencies
- Verify restored data integrity
- Document recovery procedures

## Support

For issues with backup and restore functionality:

1. **Check logs** in the GUI launcher status area
2. **Verify permissions** on data and backup directories
3. **Test with sample data** using test script
4. **Review error messages** for specific guidance
5. **Contact support** with detailed error information

The backup and restore system is designed to be robust and user-friendly, providing peace of mind for your valuable task tracking data.
