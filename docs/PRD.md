# 📄 Product Requirements Document (PRD)
**Project Name:** AdhocLog
**Platform:** Python Flask Web App (Local First)
**Deployment:** Run locally from shared folder (e.g. OneDrive)
**Frontend:** HTML, CSS, JavaScript, Jinja2, Bootstrap 5
**Backend:** Python 3, Flask
**Storage:** Local JSON or SQLite per user

---

## 🧭 Objective

Build a simple, responsive, modern web app that allows team members to record, edit, delete, and export their daily ad hoc tasks. The app must:
- Run locally via Flask
- Be accessible by double-click or local run script
- Save data to the user’s local machine
- Be designed for non-technical users with an easy UI

---

## 🧩 Features

### ✅ Task Management
- Add new task
- Edit task
- Delete task
- View task list
- Filter/search tasks
- Export filtered tasks as CSV

### ✅ Field Details

| Field             | Type      | Description |
|------------------|-----------|-------------|
| `id`             | Integer   | Auto-generated unique ID |
| `date`           | Date      | Date of the task (default: today) |
| `team_member`    | String    | Auto-detected from machine (fallback to manual input) |
| `title`          | String    | Task title |
| `classification` | Dropdown  | E.g., Bug Fix, Support, Enhancement |
| `category`       | Auto-text | Derived from classification |
| `description`    | TextArea  | Description / Actions Taken |
| `est_time`       | Integer   | Estimated time in minutes |

---

## 🧠 Classification → Category Mapping

| Classification     | Category     |
|--------------------|--------------|
| Bug Fix            | Issue        |
| Admin Work         | Admin        |
| Enhancement        | Feature      |
| Support            | Service      |
| Documentation      | Knowledge    |

---

## 💻 Pages and Routes

| Page         | Route             | Description |
|--------------|------------------|-------------|
| Dashboard    | `/`              | Summary of today's or recent tasks |
| Task List    | `/tasks`         | Full list view with filters |
| Add Task     | `/tasks/add`     | Form to submit new task |
| Edit Task    | `/tasks/edit/<id>` | Edit existing task |
| Export CSV   | `/export`        | Exports filtered tasks |

---

## 📁 Suggested Folder Structure
adhoc-task-tracker/
├── app.py
├── config.py
├── data/
│   └── tasks_.json
├── static/
│   ├── css/
│   └── js/
├── templates/
│   ├── base.html
│   ├── index.html
│   ├── tasks.html
│   ├── add_task.html
│   └── edit_task.html
├── requirements.txt
├── run.bat        # Windows
├── run.sh         # macOS/Linux
└── README.md

---

## 🎨 UI/UX Design

- Modern UI using **Bootstrap 5**
- Responsive design: mobile-first layout
- Accessible and keyboard-friendly
- Clean form layout with validation
- Reusable Bootstrap components for navigation and modals

---

## ⚙️ Technologies Used

| Layer        | Tech                     |
|--------------|--------------------------|
| Backend      | Python 3 + Flask         |
| Templating   | Jinja2                   |
| Frontend     | HTML + Bootstrap 5 + JS  |
| Storage      | JSON or SQLite (per user) |
| Export       | CSV                      |

---

## 📦 Installation / Usage

1. Clone or copy the folder to your machine from OneDrive
2. Open terminal and run:

```bash
pip install -r requirements.txt
python app.py

3.Open browser at http://localhost:5000
4.Data is stored locally per user in data/tasks_<username>.json

## 🧪 Functional Requirements
- Add/Edit/Delete/View tasks
- Auto-detect current user
- Responsive Bootstrap layout
- Auto-map category based on classification
- Export CSV with filters
- Works without internet connection
- Can run from double-click batch/script/exe

---

🛠️ Non-Functional Requirements
- Fast performance
- Portable (no install needed)
- Easy for non-devs to use
- Local-only data storage
- Optional: error logs per user

---

## 🔮 Future Enhancements
- User profiles (optional login)
- Team summary report
- Weekly productivity stats
- Reminder or recurring tasks
- Integration with Microsoft Teams or Outlook
- Deployable cloud version (Flask + SQLite/PostgreSQL)

📄 Dependencies (requirements.txt)
- Flask
- python-dotenv
- (If using SQLite with SQLAlchemy, also include: flask_sqlalchemy)
