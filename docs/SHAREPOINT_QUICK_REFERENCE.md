# AdhocLog SharePoint Quick Reference

## 🚀 Getting Started (2 Steps)

1. **Navigate to SharePoint folder** containing Adho<PERSON><PERSON>og
2. **Run launcher:**
   - **macOS/Linux:** `./launch_app.sh`
   - **Windows:** `launch_app.bat`

That's it! The app automatically detects SharePoint and configures itself.

---

## 💻 Common Commands

### Launch Application
```bash
# macOS/Linux
./launch_app.sh

# Windows
launch_app.bat
```

### Troubleshooting
```bash
# Clear cache and restart
find . -name "__pycache__" -exec rm -rf {} +
./launch_app.sh

# Check your data directory
ls data/user_$(whoami)/
```

---

## 🔧 Quick Fixes

### Problem: Permission denied
**Solution:** Contact IT for SharePoint edit permissions

### Problem: Python cache errors
**Solution:** Run Advanced Options → Setup/Repair Environment

### Problem: Can't see my tasks
**Solution:** Check you're in the right SharePoint folder with your username

### Problem: Multiple users conflicts
**Solution:** Each user needs their own terminal/command prompt session

---

## 📁 Where Your Data Lives

**Your personal data folder:**
- Path: `data/user_[yourusername]/`
- Contains: Your tasks, archived tasks, and templates
- Synced: Automatically through SharePoint

**Shared location:**
- AdhocLog app files (shared by everyone)
- Your data is isolated from other users

---

## 🆘 Need Help?

1. **Quick fix:** Try Advanced Options → Setup/Repair
2. **IT Support:** [Your IT Contact] - for SharePoint/permissions issues
3. **App Support:** [Development Team Contact] - for application problems
4. **Documentation:** See `SHAREPOINT_DEPLOYMENT_GUIDE.md` for detailed help

---

## ✅ Status Indicators

When you launch, look for these messages:

- **🌐 SharePoint environment detected** = Working correctly
- **💻 Local development environment** = Not in SharePoint folder
- **✅ User data directory created** = Your personal space ready
- **⚠️ Using legacy data structure** = Fallback mode (still works)

---

*Quick Reference v1.0 | July 29, 2025*
