# AdhocLog - Troubleshooting Guide

This guide provides detailed manual procedures for when the automated setup fails.

## 🚨 Emergency Quick Fixes

### If Nothing Works
1. **Delete everything and start fresh:**
   ```bash
   # Remove virtual environment
   rm -rf venv/          # macOS/Linux
   rmdir /s venv         # Windows
   
   # Run automated repair
   python3 setup_repair.py
   ```

2. **Use system Python directly:**
   ```bash
   # Install dependencies globally (not recommended but works)
   pip3 install flask python-dotenv werkzeug
   python3 run.py
   ```

## 🐍 Python Installation Issues

### macOS
**Problem:** Python not found or wrong version

**Solutions:**
1. **Install via Homebrew (Recommended):**
   ```bash
   # Install Homebrew if not present
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   
   # Install Python with tkinter
   brew install python@3.11
   brew install python-tk
   ```

2. **Install from python.org:**
   - Download from https://www.python.org/downloads/macos/
   - Choose "macOS 64-bit universal2 installer"
   - Check "Add Python to PATH" during installation

3. **Fix PATH issues:**
   ```bash
   # Add to ~/.zshrc or ~/.bash_profile
   export PATH="/opt/homebrew/bin:$PATH"          # Apple Silicon
   export PATH="/usr/local/bin:$PATH"             # Intel Mac
   ```

### Windows
**Problem:** Python not found or wrong version

**Solutions:**
1. **Install from python.org (Recommended):**
   - Download from https://www.python.org/downloads/windows/
   - **IMPORTANT:** Check "Add Python to PATH" during installation
   - Choose "Install for all users" if you have admin rights

2. **Install from Microsoft Store:**
   - Search for "Python 3.11" in Microsoft Store
   - Install directly from store

3. **Fix PATH issues:**
   ```cmd
   # Check current PATH
   echo %PATH%
   
   # Add Python manually (replace with your Python path)
   setx PATH "%PATH%;C:\Python311;C:\Python311\Scripts"
   ```

4. **Use Python Launcher:**
   ```cmd
   # Try these commands
   py -3 --version
   py -3.11 --version
   ```

### Linux
**Problem:** Python not found or wrong version

**Solutions:**
1. **Ubuntu/Debian:**
   ```bash
   sudo apt update
   sudo apt install python3 python3-pip python3-venv python3-tk
   ```

2. **CentOS/RHEL:**
   ```bash
   sudo yum install python3 python3-pip python3-tkinter
   ```

3. **Fedora:**
   ```bash
   sudo dnf install python3 python3-pip python3-tkinter
   ```

## 🖥️ GUI/tkinter Issues

### macOS
**Problem:** GUI launcher won't start or tkinter errors

**Solutions:**
1. **Install tkinter support:**
   ```bash
   brew install python-tk
   ```

2. **Suppress deprecation warnings:**
   ```bash
   export TK_SILENCE_DEPRECATION=1
   python3 gui_launcher.py
   ```

3. **Use different Python installation:**
   ```bash
   # Try official Python from python.org instead of Homebrew
   /usr/local/bin/python3 gui_launcher.py
   ```

### Windows
**Problem:** tkinter not available

**Solutions:**
1. **Reinstall Python with tkinter:**
   - Download Python from python.org
   - During installation, choose "Custom installation"
   - Make sure "tcl/tk and IDLE" is checked

2. **Check if tkinter is available:**
   ```cmd
   python -c "import tkinter; print('tkinter works')"
   ```

### Linux
**Problem:** tkinter not installed

**Solutions:**
1. **Install tkinter package:**
   ```bash
   # Ubuntu/Debian
   sudo apt install python3-tk
   
   # CentOS/RHEL
   sudo yum install tkinter
   
   # Fedora
   sudo dnf install python3-tkinter
   ```

## 📦 Virtual Environment Issues

### Manual Virtual Environment Creation

**All Platforms:**
```bash
# Remove broken environment
rm -rf venv/                    # macOS/Linux
rmdir /s /q venv               # Windows

# Create new environment
python3 -m venv venv           # macOS/Linux
python -m venv venv            # Windows

# Activate environment
source venv/bin/activate       # macOS/Linux
venv\Scripts\activate          # Windows

# Upgrade pip
python -m pip install --upgrade pip

# Install dependencies
python -m pip install -r requirements.txt
```

### Virtual Environment Activation Issues

**macOS/Linux:**
```bash
# If activation fails, try:
source ./venv/bin/activate
source $(pwd)/venv/bin/activate

# Check if Python executable exists
ls -la venv/bin/python*
```

**Windows:**
```cmd
# If activation fails, try:
venv\Scripts\activate.bat
call venv\Scripts\activate.bat

# Check if Python executable exists
dir venv\Scripts\python.exe
```

## 🌐 Network/Corporate Environment Issues

### Corporate Firewall/VPN Issues

**Configure pip for corporate networks:**

**macOS/Linux:**
```bash
mkdir -p ~/.pip
cat > ~/.pip/pip.conf << EOF
[global]
trusted-host = pypi.org
               pypi.python.org
               files.pythonhosted.org
timeout = 60
retries = 3
EOF
```

**Windows:**
```cmd
mkdir %APPDATA%\pip
echo [global] > %APPDATA%\pip\pip.ini
echo trusted-host = pypi.org >> %APPDATA%\pip\pip.ini
echo                pypi.python.org >> %APPDATA%\pip\pip.ini
echo                files.pythonhosted.org >> %APPDATA%\pip\pip.ini
```

**Manual package installation with trusted hosts:**
```bash
pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org flask
```

### Proxy Configuration

**If behind corporate proxy:**
```bash
# Set proxy environment variables
export HTTP_PROXY=http://proxy.company.com:8080
export HTTPS_PROXY=http://proxy.company.com:8080

# Or configure pip directly
pip install --proxy http://proxy.company.com:8080 flask
```

## 🔧 Dependency Installation Issues

### Manual Dependency Installation

**If requirements.txt fails, install individually:**
```bash
pip install flask==2.3.3
pip install python-dotenv==1.0.0
pip install werkzeug==2.3.7
```

### Permission Issues

**macOS/Linux:**
```bash
# Use --user flag to install in user directory
pip install --user flask

# Or fix permissions
sudo chown -R $(whoami) ~/.local/
```

**Windows:**
```cmd
# Run Command Prompt as Administrator
# Or use --user flag
pip install --user flask
```

## 🗂️ File and Directory Issues

### Missing Files

**Create missing directories:**
```bash
mkdir -p data templates static/css static/js static/images
```

**Check file permissions:**
```bash
# macOS/Linux
chmod +x launch_app.sh
chmod 644 *.py

# Windows - run as Administrator if needed
```

### Data Directory Issues

**Manual data directory setup:**
```bash
mkdir data
# The application will create sample data automatically
```

## 🚀 Application Startup Issues

### Port Issues

**If port is in use:**
```bash
# Find what's using the port
lsof -i :5000          # macOS/Linux
netstat -ano | findstr :5000    # Windows

# Kill the process or use different port
export FLASK_RUN_PORT=8080
python run.py
```

### Manual Application Start

**Bypass all launchers:**
```bash
# Activate virtual environment first
source venv/bin/activate        # macOS/Linux
venv\Scripts\activate          # Windows

# Start application directly
python app.py
# Or
flask run --port 8080
```

## 🆘 Last Resort Solutions

### Complete Reset

**Nuclear option - start completely fresh:**
```bash
# Remove everything
rm -rf venv/ data/ __pycache__/

# Reinstall Python if needed
# Run setup again
python3 setup_repair.py
```

### Use System Python

**If virtual environments keep failing:**
```bash
# Install globally (not recommended but works)
pip3 install flask python-dotenv werkzeug

# Run with system Python
python3 run.py
```

### Alternative Launchers

**If GUI launcher fails:**
```bash
# Use command line launcher
python3 run.py

# Or start Flask directly
python3 -c "from app import app; app.run(debug=True, port=8080)"
```

## 📞 Getting Help

### Diagnostic Information to Collect

**Before asking for help, collect this information:**
```bash
# System information
python3 --version
pip3 --version
which python3
echo $PATH

# Virtual environment status
ls -la venv/
venv/bin/python --version  # macOS/Linux
venv\Scripts\python.exe --version  # Windows

# Network connectivity
ping pypi.org

# Run diagnostics
python3 setup_repair.py
```

### Common Error Messages and Solutions

**"No module named 'venv'":**
- Install Python with venv support
- Use `python3 -m virtualenv venv` instead

**"Permission denied":**
- Run as administrator (Windows) or use sudo (macOS/Linux)
- Use `--user` flag with pip

**"SSL Certificate error":**
- Configure trusted hosts (see Network Issues section)
- Update certificates or contact IT

**"Command not found: python":**
- Install Python or fix PATH
- Try `python3` instead of `python`

This troubleshooting guide covers the most common issues. If you encounter something not covered here, run the diagnostic tools and check the error messages carefully.
