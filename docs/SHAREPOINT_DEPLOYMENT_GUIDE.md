# SharePoint Deployment Guide for AdhocLog

## 📋 Overview

This guide provides step-by-step instructions for deploying AdhocLog to SharePoint/OneDrive environments, enabling multiple users to access the application simultaneously without conflicts.

**Key Benefits:**
- ✅ Multi-user support with automatic data isolation
- ✅ Maintains existing `launch_app.sh`/`launch_app.bat` simplicity
- ✅ Automatic SharePoint environment detection
- ✅ Python cache conflict prevention
- ✅ Cross-platform compatibility (Windows, macOS, Linux)

---

## 🚀 Quick Start for End Users

### For Users Accessing Existing SharePoint Deployment

1. **Navigate to the SharePoint/OneDrive folder** containing AdhocLog
2. **Open Terminal/Command Prompt** in that folder
3. **Run the launcher:**
   - **macOS/Linux:** `./launch_app.sh`
   - **Windows:** `launch_app.bat`
4. **Choose your preferred option** from the menu

The application will automatically:
- Detect SharePoint environment
- Clean Python cache files
- Create your personal data directory
- Launch the application

---

## 🏢 IT Administrator Deployment Guide

### Prerequisites

**Required Software:**
- Python 3.7 or higher
- Git (for cloning repository)
- SharePoint site with document library access

**Permissions Needed:**
- SharePoint document library: Edit permissions
- Users need: Read/Write access to the SharePoint folder
- Python execution: Users must be able to run Python scripts

### Step 1: Download and Prepare AdhocLog

```bash
# Clone or download AdhocLog
git clone https://github.com/your-org/adhoc-log-app.git

# Or download ZIP and extract to SharePoint folder
```

### Step 2: SharePoint Site Setup

1. **Create SharePoint Document Library:**
   - Name: `AdhocLog-App` (or your preferred name)
   - Permissions: Edit access for all intended users

2. **Upload AdhocLog files:**
   - Upload entire application folder to SharePoint
   - Ensure all files are synced and accessible

3. **Set Permissions:**
   - Grant users "Edit" permissions to the document library
   - Verify users can create folders (needed for user directories)

### Step 3: User Onboarding

**For Each User:**

1. **SharePoint Sync Setup:**
   ```
   Navigate to SharePoint site → Document Library → "Sync"
   ```

2. **Local Folder Access:**
   - Windows: `%USERPROFILE%\[Organization]\[SharePoint Site] - Documents\AdhocLog-App`
   - macOS: `~/[Organization]/[SharePoint Site] - Documents/AdhocLog-App`

3. **First Launch:**
   ```bash
   # Navigate to synced folder
   cd "/path/to/sharepoint/AdhocLog-App"

   # Run launcher (will auto-configure for SharePoint)
   ./launch_app.sh        # macOS/Linux
   launch_app.bat         # Windows
   ```

### Step 4: Verification

**Verify Multi-User Setup:**

1. **User Isolation Check:**
   ```bash
   # After first launch, verify user directories exist:
   ls data/user_*
   # Should show: data/user_[username1]/, data/user_[username2]/, etc.
   ```

2. **Cache Isolation Check:**
   ```bash
   # Verify no shared cache directories
   find . -name "__pycache__" -type d
   # Should return no results or only user-specific locations
   ```

3. **Simultaneous Access Test:**
   - Have 2-3 users launch the application simultaneously
   - Verify each user sees only their own tasks
   - Check for any file conflicts in SharePoint sync

---

## 🔧 Technical Implementation Details

### SharePoint Environment Detection

The launcher automatically detects SharePoint environments by checking for:

- **OneDrive paths:** Contains "OneDrive" in path
- **SharePoint sites:** Contains "SharePoint" in path
- **Teams libraries:** Contains "Microsoft Teams" in path
- **Site patterns:** Contains "/Sites/" or "/teams/" in path

### Cache Management System

**Automatic Cache Isolation:**
```bash
# Environment variables set automatically:
PYTHONDONTWRITEBYTECODE=1                    # Prevents .pyc creation
PYTHONPYCACHEPREFIX=/tmp/adhoclog_cache_$USER # User-specific cache location
```

**Cache Cleanup Process:**
1. Removes existing `__pycache__` directories
2. Deletes `.pyc` files
3. Sets cache isolation environment variables
4. Creates user-specific temporary cache directory

### Data Directory Structure

**Legacy Structure (Local):**
```
data/
├── tasks_username.json
├── archived_tasks_username.json
├── templates_username.json
├── analytics.json (shared)
└── user_events.json (shared)
```

**New Structure (SharePoint):**
```
data/
├── user_username1/
│   ├── tasks.json
│   ├── archived_tasks.json
│   ├── templates.json
│   ├── analytics_username1.json
│   └── user_events_username1.json
├── user_username2/
│   ├── tasks.json
│   ├── archived_tasks.json
│   ├── templates.json
│   ├── analytics_username2.json
│   └── user_events_username2.json
└── ...
```

**Analytics Files:**
- `analytics_username.json` - User behavior tracking, suggestion metrics, performance data
- `user_events_username.json` - Detailed event logs for optimization and debugging

### Automatic Data Migration

The system automatically migrates existing user data:

1. **Detection:** Checks for legacy files (`tasks_username.json`)
2. **Backup:** Creates timestamped backup of original files
3. **Migration:** Copies data to new user directory structure
4. **Verification:** Confirms successful migration

---

## 🛠️ Troubleshooting

### Common Issues and Solutions

#### Issue: "Permission denied" when creating user directories

**Symptoms:**
```
⚠️ Could not create user data directory: data/user_username
💡 Falling back to legacy data structure
```

**Solutions:**
1. **Check SharePoint permissions:**
   - Verify user has "Edit" access to document library
   - Confirm folder creation permissions enabled

2. **Manual directory creation:**
   ```bash
   mkdir -p data/user_$(whoami)
   ```

3. **Alternative: Use temporary directory fallback**
   - Application automatically falls back to temp directory
   - Data stored in: `/tmp/adhoclog_username/` (Unix) or `%TEMP%\adhoclog_username\` (Windows)

#### Issue: Python cache conflicts

**Symptoms:**
```
ImportError: cannot import name 'xxx' from partially initialized module
```

**Solutions:**
1. **Run cache cleanup manually:**
   ```bash
   # Remove all cache directories
   find . -name "__pycache__" -type d -exec rm -rf {} +
   find . -name "*.pyc" -type f -delete
   ```

2. **Set environment variables manually:**
   ```bash
   export PYTHONDONTWRITEBYTECODE=1
   export PYTHONPYCACHEPREFIX="/tmp/adhoclog_cache_$(whoami)"
   ```

3. **Restart application**

#### Issue: SharePoint sync conflicts

**Symptoms:**
- Duplicate files with version numbers
- "Sync pending" status in SharePoint

**Solutions:**
1. **Wait for sync completion** before launching
2. **Resolve conflicts manually** in SharePoint web interface
3. **Use "Always keep on this device"** setting for AdhocLog folder

#### Issue: Multiple users accessing simultaneously

**Symptoms:**
- Data corruption
- Tasks appearing/disappearing
- JSON parsing errors

**Solutions:**
1. **Verify user isolation:**
   ```bash
   # Each user should have separate directory
   ls -la data/user_*
   ```

2. **Check environment variables:**
   ```bash
   echo $ADHOCLOG_USER_DATA_DIR
   echo $ADHOCLOG_SHAREPOINT_MODE
   ```

3. **Restart with cache cleanup**

### Performance Optimization

#### SharePoint Sync Optimization

1. **Configure selective sync:**
   - Only sync AdhocLog folder, not entire document library
   - Use "Always keep on this device" for faster access

2. **Network considerations:**
   - Ensure stable internet connection
   - Consider VPN impact on sync performance

3. **File size management:**
   - Archive old tasks periodically
   - Keep active task lists reasonably sized

#### Python Performance

1. **Use virtual environments:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # Unix
   venv\Scripts\activate     # Windows
   ```

2. **Optimize dependencies:**
   - Only install required packages
   - Use `pip install --no-cache-dir` in corporate environments

---

## 📊 Monitoring and Maintenance

### Health Check Commands

**Environment Status:**
```bash
# Check SharePoint detection
grep -i "sharepoint\|onedrive" <<< "$PWD"

# Verify user directories
ls -la data/user_*

# Check cache isolation
echo "Cache prevention: $PYTHONDONTWRITEBYTECODE"
echo "Cache prefix: $PYTHONPYCACHEPREFIX"
```

**User Data Status:**
```bash
# Count user directories
ls data/user_* | wc -l

# Check data file sizes
du -sh data/user_*/
```

### Regular Maintenance Tasks

1. **Weekly:**
   - Monitor SharePoint sync status
   - Check for cache accumulation
   - Verify user directory permissions

2. **Monthly:**
   - Review user access patterns
   - Archive old task data
   - Update application if new versions available

3. **Quarterly:**
   - Performance review and optimization
   - User feedback collection
   - Documentation updates

---

## 🆘 Support and Escalation

### User Support Process

1. **Level 1 - Self Service:**
   - Check this guide's troubleshooting section
   - Try cache cleanup and restart
   - Verify SharePoint sync status

2. **Level 2 - IT Support:**
   - SharePoint permissions and access issues
   - Python environment and dependency problems
   - Network and VPN connectivity issues

3. **Level 3 - Application Support:**
   - Data corruption or migration issues
   - Multi-user conflict resolution
   - Application bugs or enhancement requests

### Information to Collect for Support

**Environment Information:**
```bash
# System info
uname -a                    # Unix
systeminfo                  # Windows

# Python info
python --version
which python

# SharePoint info
echo "Current path: $PWD"
ls -la data/
echo "User: $(whoami)"

# Environment variables
env | grep ADHOCLOG
env | grep PYTHON
```

**Error Information:**
- Exact error messages
- Steps to reproduce issue
- Screenshots of error dialogs
- Time when issue occurred

### Contact Information

- **IT Helpdesk:** [Your IT Support Contact]
- **Application Support:** [Development Team Contact]
- **SharePoint Admin:** [SharePoint Administrator Contact]

---

## 📚 Additional Resources

### Documentation
- [AdhocLog User Guide](README.md)
- [Quick Reference](SHAREPOINT_QUICK_REFERENCE.md)
- [Technical Implementation](SHAREPOINT_DEPLOYMENT_TASKS.md)

### Training Materials
- Video: SharePoint Setup Walkthrough
- Tutorial: First-Time User Onboarding
- FAQ: Common Questions and Solutions

### Updates and Releases
- [Release Notes](CHANGELOG.md)
- [Known Issues](KNOWN_ISSUES.md)
- [Planned Features](ROADMAP.md)

---

*Last Updated: July 29, 2025*
*Version: 1.0*
*For AdhocLog SharePoint Deployment*
