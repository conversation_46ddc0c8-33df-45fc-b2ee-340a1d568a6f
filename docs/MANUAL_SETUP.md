# AdhocLog - Manual Setup Guide

This guide provides step-by-step manual setup instructions for all platforms when automated setup fails.

## 📋 Prerequisites

### System Requirements
- **Python:** 3.7 or higher
- **Operating System:** Windows 10+, macOS 10.14+, or Linux
- **Memory:** 512MB RAM minimum
- **Storage:** 100MB free space
- **Network:** Internet connection for dependency installation

### Required Python Packages
- Flask 2.3.3
- python-dotenv 1.0.0
- Werkzeug 2.3.7
- tkinter (for GUI, usually included with Python)

## 🍎 macOS Manual Setup

### Step 1: Install Python

**Option A: Homebrew (Recommended)**
```bash
# Install Homebrew if not present
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install Python with tkinter support
brew install python@3.11
brew install python-tk

# Verify installation
python3 --version
python3 -c "import tkinter; print('tkinter available')"
```

**Option B: Official Python Installer**
1. Download from https://www.python.org/downloads/macos/
2. Choose "macOS 64-bit universal2 installer"
3. Run installer and follow prompts
4. Verify: `python3 --version`

### Step 2: Create Project Directory
```bash
# Navigate to desired location
cd ~/Documents

# Clone or download project files
# Ensure you have: app.py, config.py, data_manager.py, requirements.txt, etc.
```

### Step 3: Create Virtual Environment
```bash
# Navigate to project directory
cd adhoc-log-app

# Create virtual environment
python3 -m venv venv

# Activate virtual environment
source venv/bin/activate

# Verify activation (should show venv path)
which python
```

### Step 4: Install Dependencies
```bash
# Upgrade pip
python -m pip install --upgrade pip

# Install requirements
pip install -r requirements.txt

# Verify installation
python -c "import flask; print(f'Flask {flask.__version__} installed')"
```

### Step 5: Create Data Directory
```bash
mkdir -p data
```

### Step 6: Test Installation
```bash
# Test basic functionality
python -c "from app import app; print('App imports successfully')"

# Start application
python run.py
```

## 🪟 Windows Manual Setup

### Step 1: Install Python

**Option A: Official Python Installer (Recommended)**
1. Download from https://www.python.org/downloads/windows/
2. **IMPORTANT:** Check "Add Python to PATH" during installation
3. Choose "Install for all users" if you have admin rights
4. Verify: Open Command Prompt and run `python --version`

**Option B: Microsoft Store**
1. Open Microsoft Store
2. Search for "Python 3.11"
3. Install directly from store
4. Verify: `python --version`

### Step 2: Open Command Prompt
```cmd
# Open Command Prompt as Administrator (recommended)
# Or open regular Command Prompt
# Navigate to project directory
cd C:\path\to\adhoc-log-app
```

### Step 3: Create Virtual Environment
```cmd
# Create virtual environment
python -m venv venv

# Activate virtual environment
venv\Scripts\activate

# Verify activation (should show (venv) prefix)
python --version
```

### Step 4: Configure pip for Corporate Networks (if needed)
```cmd
# Create pip config directory
mkdir %APPDATA%\pip

# Create config file
echo [global] > %APPDATA%\pip\pip.ini
echo trusted-host = pypi.org >> %APPDATA%\pip\pip.ini
echo                pypi.python.org >> %APPDATA%\pip\pip.ini
echo                files.pythonhosted.org >> %APPDATA%\pip\pip.ini
```

### Step 5: Install Dependencies
```cmd
# Upgrade pip
python -m pip install --upgrade pip

# Install requirements
pip install -r requirements.txt

# If corporate network issues, use trusted hosts
pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org -r requirements.txt

# Verify installation
python -c "import flask; print('Flask installed')"
```

### Step 6: Create Data Directory
```cmd
mkdir data
```

### Step 7: Test Installation
```cmd
# Test basic functionality
python -c "from app import app; print('App imports successfully')"

# Start application
python run.py
```

## 🐧 Linux Manual Setup

### Step 1: Install Python

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install python3 python3-pip python3-venv python3-tk
```

**CentOS/RHEL:**
```bash
sudo yum install python3 python3-pip python3-tkinter
```

**Fedora:**
```bash
sudo dnf install python3 python3-pip python3-tkinter
```

**Arch Linux:**
```bash
sudo pacman -S python python-pip tk
```

### Step 2: Verify Python Installation
```bash
python3 --version
python3 -c "import tkinter; print('tkinter available')"
```

### Step 3: Create Virtual Environment
```bash
# Navigate to project directory
cd /path/to/adhoc-log-app

# Create virtual environment
python3 -m venv venv

# Activate virtual environment
source venv/bin/activate

# Verify activation
which python
```

### Step 4: Install Dependencies
```bash
# Upgrade pip
python -m pip install --upgrade pip

# Install requirements
pip install -r requirements.txt

# Verify installation
python -c "import flask; print(f'Flask {flask.__version__} installed')"
```

### Step 5: Create Data Directory
```bash
mkdir -p data
```

### Step 6: Test Installation
```bash
# Test basic functionality
python -c "from app import app; print('App imports successfully')"

# Start application
python run.py
```

## 🔧 Manual Dependency Installation

### If requirements.txt Installation Fails

**Install packages individually:**
```bash
pip install flask==2.3.3
pip install python-dotenv==1.0.0
pip install werkzeug==2.3.7
```

**For corporate networks:**
```bash
pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org flask==2.3.3
pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org python-dotenv==1.0.0
pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org werkzeug==2.3.7
```

## 📁 Manual File Structure Setup

### Required Files and Directories
```
adhoc-log-app/
├── app.py                 # Main Flask application
├── config.py              # Configuration settings
├── data_manager.py        # Data management functions
├── gui_launcher.py        # GUI launcher
├── run.py                 # Application runner
├── setup_repair.py        # Automated setup tool
├── launch_app.sh          # Unix/Linux launcher
├── launch_app.bat         # Windows launcher
├── requirements.txt       # Python dependencies
├── data/                  # Data directory (auto-created)
├── templates/             # HTML templates
│   ├── base.html
│   ├── index.html
│   ├── tasks.html
│   └── ...
├── static/                # Static files
│   ├── css/
│   ├── js/
│   └── images/
└── venv/                  # Virtual environment (created)
```

### Create Missing Directories
```bash
# Create all required directories
mkdir -p data templates static/css static/js static/images

# Set proper permissions (Unix/Linux)
chmod 755 data templates static
chmod 644 *.py *.txt *.md
chmod +x launch_app.sh
```

## 🚀 Manual Application Startup

### Method 1: Using Launchers
```bash
# macOS/Linux
bash launch_app.sh

# Windows
launch_app.bat
```

### Method 2: Direct Python Execution
```bash
# Activate virtual environment first
source venv/bin/activate    # macOS/Linux
venv\Scripts\activate       # Windows

# Start application
python run.py
```

### Method 3: Flask Direct Start
```bash
# Set environment variables
export FLASK_APP=app.py     # macOS/Linux
set FLASK_APP=app.py        # Windows

# Start Flask
flask run --port 8080
```

### Method 4: GUI Launcher
```bash
# Start GUI launcher directly
python gui_launcher.py
```

## 🔍 Verification Steps

### Test Each Component

**1. Python Installation:**
```bash
python3 --version
python3 -c "print('Python works')"
```

**2. Virtual Environment:**
```bash
source venv/bin/activate    # macOS/Linux
venv\Scripts\activate       # Windows
which python                # Should show venv path
```

**3. Dependencies:**
```bash
python -c "import flask; print('Flask:', flask.__version__)"
python -c "import werkzeug; print('Werkzeug:', werkzeug.__version__)"
python -c "from dotenv import load_dotenv; print('python-dotenv works')"
```

**4. tkinter (for GUI):**
```bash
python -c "import tkinter; root = tkinter.Tk(); root.withdraw(); print('tkinter works')"
```

**5. Application Import:**
```bash
python -c "from app import app; print('App imports successfully')"
```

**6. Full Application Start:**
```bash
python run.py
# Should show: "Starting Flask app on port XXXX..."
```

## 🆘 Common Issues and Solutions

### Virtual Environment Issues
- **Problem:** `python -m venv venv` fails
- **Solution:** Install `python3-venv` package or use `virtualenv`

### Permission Issues
- **Problem:** Permission denied errors
- **Solution:** Use `sudo` (Linux/macOS) or run as Administrator (Windows)

### Network Issues
- **Problem:** pip install fails with SSL errors
- **Solution:** Configure trusted hosts (see corporate network section)

### Path Issues
- **Problem:** `python` command not found
- **Solution:** Use `python3` or add Python to PATH

### tkinter Issues
- **Problem:** GUI launcher won't start
- **Solution:** Install tkinter package or use command-line launcher

## 📞 Getting Help

If manual setup still fails:

1. **Run diagnostics:**
   ```bash
   python3 setup_repair.py
   ```

2. **Check system requirements**
3. **Review error messages carefully**
4. **Consult TROUBLESHOOTING.md for specific error solutions**
5. **Contact system administrator with diagnostic output**

## ✅ Success Indicators

You know setup is successful when:
- ✅ Python 3.7+ is installed and accessible
- ✅ Virtual environment is created and activated
- ✅ All dependencies are installed without errors
- ✅ Application starts without errors
- ✅ You can access the web interface at http://localhost:8080
- ✅ GUI launcher works (if tkinter is available)

This manual setup guide should work even when all automated tools fail. Follow the steps carefully for your specific platform.
