"""
AdhocLog Application Package
Main Flask application package for AI-powered task tracking
"""

__version__ = "1.0.0"

# Import the Flask app directly
from .app import app

# Make other components available via lazy loading
def get_config():
    """Get Config class - lazy loaded"""
    from .config import Config
    return Config

def get_data_manager():
    """Get DataManager class - lazy loaded"""
    from .data_manager import DataManager
    return DataManager

def get_ai_engine():
    """Get AITaskEngine class - lazy loaded"""
    from .ai_engine import AITaskEngine
    return AITaskEngine

def get_chatbot_engine():
    """Get ChatbotEngine class - lazy loaded"""
    from .chatbot_engine import ChatbotEngine
    return ChatbotEngine

def get_analytics():
    """Get analytics classes - lazy loaded"""
    from .analytics import AnalyticsEngine, OptimizationEngine, ReportGenerator
    return AnalyticsEngine, OptimizationEngine, ReportGenerator

__all__ = ['app', 'get_config', 'get_data_manager', 'get_ai_engine', 'get_chatbot_engine', 'get_analytics']
