from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, make_response
import csv
import io
from datetime import datetime, date
from .config import Config
from .data_manager import DataManager
from .ai_engine import AITaskEngine
from .analytics import AnalyticsEngine, OptimizationEngine, ReportGenerator
from .chatbot_engine import ChatbotEngine

app = Flask(__name__,
           static_folder='static',
           template_folder='templates')
app.config.from_object(Config)

# Initialize data manager and AI systems
data_manager = DataManager()
ai_engine = AITaskEngine()
analytics_engine = AnalyticsEngine()
optimization_engine = OptimizationEngine(analytics_engine)
report_generator = ReportGenerator(analytics_engine, optimization_engine)
chatbot_engine = ChatbotEngine(data_manager, ai_engine, analytics_engine)

# Template helper function for pagination URLs
@app.template_global()
def build_pagination_url(page):
    """Build pagination URL preserving current filters"""
    args = request.args.copy()
    args['page'] = page
    return url_for(request.endpoint, **args)

@app.route('/')
def dashboard():
    """Dashboard showing today's tasks and recent activity"""
    today = date.today().strftime('%Y-%m-%d')

    # Get current month start and end
    current_month_start = date.today().replace(day=1).strftime('%Y-%m-%d')
    from datetime import timedelta
    next_month = date.today().replace(day=28) + timedelta(days=4)  # this will never fail
    current_month_end = (next_month - timedelta(days=next_month.day)).strftime('%Y-%m-%d')

    # Get today's tasks
    today_result = data_manager.filter_tasks({'start_date': today, 'end_date': today}, per_page=100)
    today_tasks = today_result['tasks']

    # Get current month's tasks
    month_result = data_manager.filter_tasks({'start_date': current_month_start, 'end_date': current_month_end}, per_page=1000)
    month_tasks = month_result['tasks']

    # Get recent tasks (last 7 days)
    week_ago = (date.today() - timedelta(days=7)).strftime('%Y-%m-%d')
    recent_result = data_manager.filter_tasks({'start_date': week_ago}, per_page=100)
    recent_tasks = recent_result['tasks']

    # Calculate stats
    total_time_today = sum(task.get('est_time', 0) for task in today_tasks)
    total_tasks_today = len(today_tasks)

    # Calculate monthly stats
    total_time_month = sum(task.get('est_time', 0) for task in month_tasks)
    total_tasks_month = len(month_tasks)

    return render_template('index.html',
                         today_tasks=today_tasks,
                         recent_tasks=recent_tasks[:10],  # Limit to 10 recent
                         total_time_today=total_time_today,
                         total_tasks_today=total_tasks_today,
                         total_time_month=total_time_month,
                         total_tasks_month=total_tasks_month,
                         current_user=data_manager.username)

@app.route('/tasks')
def task_list():
    """Full task list with filtering and pagination"""
    # Get filter parameters
    filters = {
        'start_date': request.args.get('start_date', ''),
        'end_date': request.args.get('end_date', ''),
        'classification': request.args.get('classification', ''),
        'search': request.args.get('search', '')
    }

    # Get pagination parameters
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 10))

    # Remove empty filters
    clean_filters = {k: v for k, v in filters.items() if v}

    # Get filtered tasks with pagination
    result = data_manager.filter_tasks(clean_filters, page=page, per_page=per_page)

    return render_template('tasks.html',
                         tasks=result['tasks'],
                         pagination=result['pagination'],
                         filters=filters,
                         classifications=app.config['CLASSIFICATIONS'],
                         current_user=data_manager.username)

@app.route('/tasks/add', methods=['GET', 'POST'])
def add_task():
    """Add a new task"""
    if request.method == 'POST':
        task_data = {
            'title': request.form.get('title', '').strip(),
            'classification': request.form.get('classification', ''),
            'description': request.form.get('description', '').strip(),
            'est_time': int(request.form.get('est_time', 0)),
            'date': request.form.get('date', date.today().strftime('%Y-%m-%d'))
        }

        # Validate required fields
        if not task_data['title']:
            flash('Title is required', 'error')
            return render_template('add_task.html',
                                 classifications=app.config['CLASSIFICATIONS'],
                                 task_data=task_data,
                                 current_user=data_manager.username)

        try:
            new_task = data_manager.add_task(task_data)
            flash(f'Task "{new_task["title"]}" added successfully!', 'success')
            return redirect(url_for('task_list'))
        except Exception as e:
            flash(f'Error adding task: {str(e)}', 'error')

    return render_template('add_task.html',
                         classifications=app.config['CLASSIFICATIONS'],
                         task_data={},
                         current_user=data_manager.username)

@app.route('/tasks/edit/<int:task_id>', methods=['GET', 'POST'])
def edit_task(task_id):
    """Edit an existing task"""
    task = data_manager.get_task_by_id(task_id)
    if not task:
        flash('Task not found', 'error')
        return redirect(url_for('task_list'))

    if request.method == 'POST':
        task_data = {
            'title': request.form.get('title', '').strip(),
            'classification': request.form.get('classification', ''),
            'description': request.form.get('description', '').strip(),
            'est_time': int(request.form.get('est_time', 0)),
            'date': request.form.get('date', task.get('date', ''))
        }

        # Validate required fields
        if not task_data['title']:
            flash('Title is required', 'error')
            return render_template('edit_task.html',
                                 task=task,
                                 classifications=app.config['CLASSIFICATIONS'],
                                 current_user=data_manager.username)

        try:
            updated_task = data_manager.update_task(task_id, task_data)
            if updated_task:
                flash(f'Task "{updated_task["title"]}" updated successfully!', 'success')
                return redirect(url_for('task_list'))
            else:
                flash('Error updating task', 'error')
        except Exception as e:
            flash(f'Error updating task: {str(e)}', 'error')

    return render_template('edit_task.html',
                         task=task,
                         classifications=app.config['CLASSIFICATIONS'],
                         current_user=data_manager.username)

@app.route('/tasks/delete/<int:task_id>', methods=['POST'])
def delete_task(task_id):
    """Delete a task"""
    task = data_manager.get_task_by_id(task_id)
    if not task:
        flash('Task not found', 'error')
        return redirect(url_for('task_list'))

    try:
        if data_manager.delete_task(task_id):
            flash(f'Task "{task["title"]}" deleted successfully!', 'success')
        else:
            flash('Error deleting task', 'error')
    except Exception as e:
        flash(f'Error deleting task: {str(e)}', 'error')

    return redirect(url_for('task_list'))

@app.route('/archive')
def archive_list():
    """View archived tasks with pagination"""
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 10))

    result = data_manager.get_archived_tasks_paginated(page=page, per_page=per_page)

    return render_template('archive.html',
                         archived_tasks=result['tasks'],
                         pagination=result['pagination'],
                         current_user=data_manager.username)

@app.route('/tasks/restore/<int:task_id>', methods=['POST'])
def restore_task(task_id):
    """Restore a task from archive"""
    archived_tasks = data_manager.get_archived_tasks()
    task = None
    for t in archived_tasks:
        if t.get('id') == task_id:
            task = t
            break

    if not task:
        flash('Archived task not found', 'error')
        return redirect(url_for('archive_list'))

    try:
        if data_manager.restore_task(task_id):
            flash(f'Task "{task["title"]}" restored successfully!', 'success')
        else:
            flash('Error restoring task', 'error')
    except Exception as e:
        flash(f'Error restoring task: {str(e)}', 'error')

    return redirect(url_for('archive_list'))

@app.route('/tasks/permanent_delete/<int:task_id>', methods=['POST'])
def permanent_delete_task(task_id):
    """Permanently delete a task from archive"""
    archived_tasks = data_manager.get_archived_tasks()
    task = None
    for t in archived_tasks:
        if t.get('id') == task_id:
            task = t
            break

    if not task:
        flash('Archived task not found', 'error')
        return redirect(url_for('archive_list'))

    try:
        if data_manager.permanently_delete_task(task_id):
            flash(f'Task "{task["title"]}" permanently deleted!', 'success')
        else:
            flash('Error permanently deleting task', 'error')
    except Exception as e:
        flash(f'Error permanently deleting task: {str(e)}', 'error')

    return redirect(url_for('archive_list'))

@app.route('/tasks/bulk_archive', methods=['POST'])
def bulk_archive_tasks():
    """Archive multiple tasks"""
    try:
        task_ids = request.json.get('task_ids', [])
        if not task_ids:
            return jsonify({'success': False, 'message': 'No tasks selected'}), 400

        # Convert to integers
        task_ids = [int(id) for id in task_ids]

        result = data_manager.bulk_archive_tasks(task_ids)

        if result['success']:
            return jsonify(result), 200
        else:
            return jsonify(result), 400

    except Exception as e:
        return jsonify({'success': False, 'message': f'Error archiving tasks: {str(e)}'}), 500

@app.route('/tasks/bulk_restore', methods=['POST'])
def bulk_restore_tasks():
    """Restore multiple tasks from archive"""
    try:
        task_ids = request.json.get('task_ids', [])
        if not task_ids:
            return jsonify({'success': False, 'message': 'No tasks selected'}), 400

        # Convert to integers
        task_ids = [int(id) for id in task_ids]

        result = data_manager.bulk_restore_tasks(task_ids)

        if result['success']:
            return jsonify(result), 200
        else:
            return jsonify(result), 400

    except Exception as e:
        return jsonify({'success': False, 'message': f'Error restoring tasks: {str(e)}'}), 500

@app.route('/tasks/bulk_delete', methods=['POST'])
def bulk_permanently_delete_tasks():
    """Permanently delete multiple tasks from archive"""
    try:
        task_ids = request.json.get('task_ids', [])
        if not task_ids:
            return jsonify({'success': False, 'message': 'No tasks selected'}), 400

        # Convert to integers
        task_ids = [int(id) for id in task_ids]

        result = data_manager.bulk_permanently_delete_tasks(task_ids)

        if result['success']:
            return jsonify(result), 200
        else:
            return jsonify(result), 400

    except Exception as e:
        return jsonify({'success': False, 'message': f'Error deleting tasks: {str(e)}'}), 500

@app.route('/statistics')
def statistics():
    """Statistics page with monthly breakdown"""
    from datetime import timedelta
    import calendar

    # Get month filter parameter, default to current month
    selected_month = request.args.get('month', date.today().strftime('%Y-%m'))

    try:
        # Parse the selected month
        year, month = map(int, selected_month.split('-'))

        # Get month start and end dates
        month_start = date(year, month, 1)
        # Get last day of month
        last_day = calendar.monthrange(year, month)[1]
        month_end = date(year, month, last_day)

        month_start_str = month_start.strftime('%Y-%m-%d')
        month_end_str = month_end.strftime('%Y-%m-%d')

    except (ValueError, IndexError):
        # If invalid month format, default to current month
        month_start = date.today().replace(day=1)
        next_month = month_start.replace(day=28) + timedelta(days=4)
        month_end = (next_month - timedelta(days=next_month.day))
        month_start_str = month_start.strftime('%Y-%m-%d')
        month_end_str = month_end.strftime('%Y-%m-%d')
        selected_month = month_start.strftime('%Y-%m')

    # Get all tasks for the selected month
    month_result = data_manager.filter_tasks({
        'start_date': month_start_str,
        'end_date': month_end_str
    }, per_page=1000)

    month_tasks = month_result['tasks']

    # Calculate statistics
    total_tasks = len(month_tasks)
    total_time = sum(task.get('est_time', 0) for task in month_tasks)

    # Group by classification
    classification_stats = {}
    for task in month_tasks:
        classification = task.get('classification', 'Unknown')
        if classification not in classification_stats:
            classification_stats[classification] = {'count': 0, 'time': 0}
        classification_stats[classification]['count'] += 1
        classification_stats[classification]['time'] += task.get('est_time', 0)

    # Group by category
    category_stats = {}
    for task in month_tasks:
        category = task.get('category', 'Unknown')
        if category not in category_stats:
            category_stats[category] = {'count': 0, 'time': 0}
        category_stats[category]['count'] += 1
        category_stats[category]['time'] += task.get('est_time', 0)

    # Daily breakdown
    daily_stats = {}
    for task in month_tasks:
        task_date = task.get('date', '')
        if task_date not in daily_stats:
            daily_stats[task_date] = {'count': 0, 'time': 0}
        daily_stats[task_date]['count'] += 1
        daily_stats[task_date]['time'] += task.get('est_time', 0)

    # Sort daily stats by date
    sorted_daily_stats = dict(sorted(daily_stats.items()))

    # Calculate averages
    working_days = len([d for d in daily_stats.values() if d['count'] > 0])
    avg_tasks_per_day = total_tasks / working_days if working_days > 0 else 0
    avg_time_per_day = total_time / working_days if working_days > 0 else 0
    avg_time_per_task = total_time / total_tasks if total_tasks > 0 else 0

    # Generate month options for dropdown (last 12 months)
    month_options = []
    current_date = date.today()
    for i in range(12):
        option_date = current_date.replace(day=1) - timedelta(days=i*30)
        option_date = option_date.replace(day=1)  # Ensure we're at start of month
        month_options.append({
            'value': option_date.strftime('%Y-%m'),
            'label': option_date.strftime('%B %Y')
        })

    return render_template('statistics.html',
                         month_tasks=month_tasks[:20],  # Show latest 20 tasks
                         total_tasks=total_tasks,
                         total_time=total_time,
                         classification_stats=classification_stats,
                         category_stats=category_stats,
                         daily_stats=sorted_daily_stats,
                         avg_tasks_per_day=round(avg_tasks_per_day, 1),
                         avg_time_per_day=round(avg_time_per_day, 1),
                         avg_time_per_task=round(avg_time_per_task, 1),
                         selected_month=selected_month,
                         month_options=month_options,
                         month_name=month_start.strftime('%B %Y'),
                         current_user=data_manager.username)

@app.route('/export')
def export_csv():
    """Export filtered tasks as CSV"""
    # Get same filters as task list
    filters = {
        'start_date': request.args.get('start_date', ''),
        'end_date': request.args.get('end_date', ''),
        'classification': request.args.get('classification', ''),
        'search': request.args.get('search', '')
    }

    # Remove empty filters
    filters = {k: v for k, v in filters.items() if v}

    # Get filtered tasks
    if filters:
        tasks = data_manager.filter_tasks(filters)
    else:
        tasks = data_manager.get_all_tasks()

    # Sort by date
    tasks.sort(key=lambda x: x.get('date', ''), reverse=True)

    # Create CSV
    output = io.StringIO()
    writer = csv.writer(output)

    # Write header
    writer.writerow(['Date', 'Team Member', 'Task Title', 'Classification', 'Actions Taken / Description', 'Estimated Time (minute)', 'Category'])

    # Write data
    for task in tasks:
        writer.writerow([
            task.get('date', ''),
            task.get('team_member', ''),
            task.get('title', ''),
            task.get('classification', ''),
            task.get('description', ''),
            task.get('est_time', 0),
            task.get('category', '')
        ])

    # Create response
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = f'attachment; filename=tasks_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'

    return response

@app.route('/api/get_category/<classification>')
def get_category(classification):
    """API endpoint to get category for a classification"""
    category = app.config['CLASSIFICATION_MAPPING'].get(classification, 'Other')
    return jsonify({'category': category})

@app.route('/api/get_recent_tasks')
def get_recent_tasks():
    """API endpoint to get recent unique tasks for quick add"""
    limit = int(request.args.get('limit', 10))
    all_tasks = data_manager.get_all_tasks()

    # Get unique tasks based on title and classification (most recent first)
    unique_tasks = {}
    for task in sorted(all_tasks, key=lambda x: x.get('date', ''), reverse=True):
        key = (task.get('title', ''), task.get('classification', ''))
        if key not in unique_tasks:
            unique_tasks[key] = {
                'title': task.get('title', ''),
                'classification': task.get('classification', ''),
                'category': task.get('category', ''),
                'description': task.get('description', ''),
                'est_time': task.get('est_time', 30)
            }

    # Return limited number of most recent unique tasks
    recent_tasks = list(unique_tasks.values())[:limit]
    return jsonify({'tasks': recent_tasks})

@app.route('/api/recent-tasks')
def api_recent_tasks():
    """API endpoint for enhanced recent tasks with usage frequency"""
    try:
        limit = int(request.args.get('limit', 10))
        with_frequency = request.args.get('with_frequency', 'false').lower() == 'true'

        # Get recent tasks (last 30 days)
        from datetime import timedelta
        thirty_days_ago = (date.today() - timedelta(days=30)).strftime('%Y-%m-%d')
        recent_result = data_manager.filter_tasks({'start_date': thirty_days_ago}, per_page=1000)
        all_recent_tasks = recent_result['tasks']

        if with_frequency:
            # Count frequency of similar tasks
            task_frequency = {}
            for task in all_recent_tasks:
                # Create a key based on title similarity (simple approach)
                key = task.get('title', '').lower().strip()
                if key not in task_frequency:
                    task_frequency[key] = {'count': 0, 'latest_task': task, 'ids': []}
                task_frequency[key]['count'] += 1
                task_frequency[key]['ids'].append(task.get('id'))

                # Keep the most recent task
                if task.get('date', '') > task_frequency[key]['latest_task'].get('date', ''):
                    task_frequency[key]['latest_task'] = task

            # Get unique tasks sorted by frequency and recency
            unique_tasks = []
            for freq_data in task_frequency.values():
                task = freq_data['latest_task'].copy()
                task['usage_count'] = freq_data['count']
                task['similar_task_ids'] = freq_data['ids']
                unique_tasks.append(task)

            # Sort by frequency (desc) then by date (desc)
            unique_tasks.sort(key=lambda x: (x.get('usage_count', 0), x.get('date', '')), reverse=True)
            tasks_to_return = unique_tasks[:limit]
        else:
            # Just return recent tasks sorted by date
            tasks_to_return = sorted(all_recent_tasks, key=lambda x: x.get('date', ''), reverse=True)[:limit]

        return jsonify({
            'success': True,
            'tasks': tasks_to_return,
            'total': len(tasks_to_return)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'tasks': []
        }), 500

# Custom Template Management API Endpoints (Phase 2)
@app.route('/api/templates', methods=['GET'])
def get_templates():
    """Get all custom templates for the current user"""
    try:
        templates = data_manager.get_custom_templates()
        return jsonify({'success': True, 'templates': templates})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/templates', methods=['POST'])
def create_template():
    """Create a new custom template"""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['name', 'title', 'classification', 'description', 'est_time']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        template_data = {
            'name': data['name'],
            'title': data['title'],
            'classification': data['classification'],
            'category': data.get('category', ''),
            'description': data['description'],
            'est_time': int(data['est_time']),
            'created_at': datetime.now().isoformat(),
            'usage_count': 0
        }

        template_id = data_manager.create_custom_template(template_data)
        return jsonify({'success': True, 'template_id': template_id, 'template': template_data})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/templates/<template_id>', methods=['PUT'])
def update_template(template_id):
    """Update an existing custom template"""
    try:
        data = request.get_json()
        success = data_manager.update_custom_template(template_id, data)

        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': 'Template not found'}), 404

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/templates/<template_id>', methods=['DELETE'])
def delete_template(template_id):
    """Delete a custom template"""
    try:
        success = data_manager.delete_custom_template(template_id)

        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': 'Template not found'}), 404

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/templates/<template_id>/use', methods=['POST'])
def use_template(template_id):
    """Increment usage count for a template"""
    try:
        success = data_manager.increment_template_usage(template_id)

        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': 'Template not found'}), 404

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# Bulk Entry API Endpoints (Phase 2)
@app.route('/api/tasks/bulk', methods=['POST'])
def create_bulk_tasks():
    """Create multiple tasks at once"""
    try:
        data = request.get_json()
        tasks = data.get('tasks', [])

        if not tasks:
            return jsonify({'success': False, 'error': 'No tasks provided'}), 400

        created_tasks = []
        errors = []

        for i, task_data in enumerate(tasks):
            try:
                # Validate required fields
                required_fields = ['title', 'classification', 'description', 'est_time']
                for field in required_fields:
                    if field not in task_data or not task_data[field]:
                        errors.append(f'Task {i+1}: Missing required field: {field}')
                        continue

                # Create task
                task = {
                    'date': task_data.get('date', date.today().strftime('%Y-%m-%d')),
                    'team_member': data_manager.get_current_user(),
                    'title': task_data['title'],
                    'classification': task_data['classification'],
                    'category': task_data.get('category', data_manager.get_category_for_classification(task_data['classification'])),
                    'description': task_data['description'],
                    'est_time': int(task_data['est_time'])
                }

                created_task = data_manager.add_task(task)
                created_tasks.append({'id': created_task['id'], 'task': created_task})

            except Exception as e:
                errors.append(f'Task {i+1}: {str(e)}')

        return jsonify({
            'success': True,
            'created_count': len(created_tasks),
            'error_count': len(errors),
            'created_tasks': created_tasks,
            'errors': errors
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# Pattern Recognition & Learning API Endpoints (Phase 2)
@app.route('/api/suggestions/classification', methods=['POST'])
def suggest_classification():
    """Predict classification based on title and description"""
    try:
        data = request.get_json()
        title = data.get('title', '')
        description = data.get('description', '')

        if not title:
            return jsonify({'success': False, 'error': 'Title is required'}), 400

        prediction = data_manager.predict_classification(title, description)
        return jsonify({'success': True, 'prediction': prediction})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/suggestions/duration', methods=['POST'])
def suggest_duration():
    """Estimate task duration based on title, classification, and description"""
    try:
        data = request.get_json()
        title = data.get('title', '')
        classification = data.get('classification', '')
        description = data.get('description', '')

        if not title:
            return jsonify({'success': False, 'error': 'Title is required'}), 400

        estimation = data_manager.estimate_duration(title, classification, description)
        return jsonify({'success': True, 'estimation': estimation})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/suggestions/contextual', methods=['GET'])
def get_contextual_suggestions():
    """Get contextual task suggestions based on current time and user patterns"""
    try:
        suggestions = data_manager.get_contextual_suggestions()
        return jsonify({'success': True, 'suggestions': suggestions})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/suggestions/title', methods=['POST'])
def get_title_suggestions():
    """Get suggestions based on partial title input"""
    try:
        data = request.get_json()
        partial_title = data.get('title', '').strip()
        limit = data.get('limit', 5)

        if len(partial_title) < 2:
            return jsonify({'success': True, 'suggestions': []})

        suggestions = data_manager.get_title_suggestions(partial_title, limit)
        return jsonify({'success': True, **suggestions})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/patterns', methods=['GET'])
def get_user_patterns():
    """Get user pattern analysis for debugging and insights"""
    try:
        patterns = data_manager.analyze_user_patterns()
        return jsonify({'success': True, 'patterns': patterns})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# Phase 3: AI-Powered Suggestions and Advanced Analytics Endpoints

@app.route('/api/ai/analyze-task', methods=['POST'])
def ai_analyze_task():
    """Comprehensive AI analysis of a task"""
    try:
        data = request.get_json()
        title = data.get('title', '')
        description = data.get('description', '')

        if not title:
            return jsonify({'success': False, 'error': 'Title is required'}), 400

        # Get user tasks for context
        user_tasks = data_manager.get_all_tasks()

        # Perform AI analysis
        analysis = ai_engine.analyze_task(title, description, user_tasks)

        # Track analytics
        analytics_engine.track_event('ai_analysis_requested', {
            'title_length': len(title),
            'description_length': len(description),
            'has_description': bool(description)
        })

        return jsonify({'success': True, 'analysis': analysis})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/ai/predict-next-tasks', methods=['GET'])
def ai_predict_next_tasks():
    """Get AI predictions for next likely tasks"""
    try:
        limit = request.args.get('limit', 5, type=int)

        # Get user tasks
        user_tasks = data_manager.get_all_tasks()

        # Get predictions
        predictions = ai_engine.get_next_task_predictions(user_tasks, limit)

        # Track analytics
        analytics_engine.track_event('next_task_predictions_requested', {
            'limit': limit,
            'predictions_returned': len(predictions)
        })

        return jsonify({'success': True, 'predictions': predictions})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/ai/workload-optimization', methods=['GET'])
def ai_workload_optimization():
    """Get AI-powered workload optimization recommendations"""
    try:
        target_date = request.args.get('date', date.today().strftime('%Y-%m-%d'))

        # Get tasks for analysis
        user_tasks = data_manager.get_all_tasks()

        # Get optimization recommendations
        optimization = ai_engine.get_workload_optimization(user_tasks, target_date)

        # Track analytics
        analytics_engine.track_event('workload_optimization_requested', {
            'target_date': target_date,
            'total_tasks': len(user_tasks)
        })

        return jsonify({'success': True, 'optimization': optimization})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/track-event', methods=['POST'])
def track_analytics_event():
    """Track user interaction events for analytics"""
    try:
        data = request.get_json()
        event_type = data.get('event_type')
        event_data = data.get('data', {})
        user_id = data.get('user_id', 'default_user')

        if not event_type:
            return jsonify({'success': False, 'error': 'Event type is required'}), 400

        analytics_engine.track_event(event_type, event_data, user_id)
        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/suggestion-metrics', methods=['GET'])
def get_suggestion_metrics():
    """Get suggestion effectiveness metrics"""
    try:
        suggestion_type = request.args.get('type')
        days = request.args.get('days', 30, type=int)

        metrics = analytics_engine.get_suggestion_metrics(suggestion_type, days)

        # Convert dataclass objects to dictionaries
        metrics_dict = {}
        for key, value in metrics.items():
            metrics_dict[key] = {
                'suggestion_type': value.suggestion_type,
                'total_shown': value.total_shown,
                'total_accepted': value.total_accepted,
                'total_rejected': value.total_rejected,
                'total_ignored': value.total_ignored,
                'avg_confidence': value.avg_confidence,
                'avg_response_time': value.avg_response_time,
                'acceptance_rate': value.acceptance_rate
            }

        return jsonify({'success': True, 'metrics': metrics_dict})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/behavior-patterns', methods=['GET'])
def get_behavior_patterns():
    """Get user behavior patterns"""
    try:
        days = request.args.get('days', 30, type=int)
        patterns = analytics_engine.get_user_behavior_patterns(days)
        return jsonify({'success': True, 'patterns': patterns})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/performance-metrics', methods=['GET'])
def get_performance_metrics():
    """Get system performance metrics"""
    try:
        days = request.args.get('days', 30, type=int)
        metrics = analytics_engine.get_performance_metrics(days)
        return jsonify({'success': True, 'metrics': metrics})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/optimization-recommendations', methods=['GET'])
def get_optimization_recommendations():
    """Get optimization recommendations based on analytics"""
    try:
        recommendations = optimization_engine.get_optimization_recommendations()
        return jsonify({'success': True, 'recommendations': recommendations})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/ab-test-suggestions', methods=['GET'])
def get_ab_test_suggestions():
    """Get A/B test suggestions"""
    try:
        suggestions = optimization_engine.get_ab_test_suggestions()
        return jsonify({'success': True, 'suggestions': suggestions})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/summary-report', methods=['GET'])
def get_summary_report():
    """Get comprehensive analytics summary report"""
    try:
        days = request.args.get('days', 30, type=int)
        report = report_generator.generate_summary_report(days)
        return jsonify({'success': True, 'report': report})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# Enhanced suggestion endpoints with AI improvements
@app.route('/api/suggestions/enhanced-classification', methods=['POST'])
def enhanced_suggest_classification():
    """Enhanced classification prediction using AI"""
    try:
        data = request.get_json()
        title = data.get('title', '')
        description = data.get('description', '')

        if not title:
            return jsonify({'success': False, 'error': 'Title is required'}), 400

        # Get user tasks for context
        user_tasks = data_manager.get_all_tasks()

        # Use AI engine for enhanced prediction
        analysis = ai_engine.analyze_task(title, description, user_tasks)
        prediction = analysis['classification']

        # Track suggestion shown
        analytics_engine.track_suggestion_shown('enhanced_classification', prediction, prediction.get('confidence', 0))

        return jsonify({'success': True, 'prediction': prediction})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/suggestions/enhanced-duration', methods=['POST'])
def enhanced_suggest_duration():
    """Enhanced duration estimation using AI"""
    try:
        data = request.get_json()
        title = data.get('title', '')
        description = data.get('description', '')
        classification = data.get('classification', '')

        if not title:
            return jsonify({'success': False, 'error': 'Title is required'}), 400

        # Get user tasks for context
        user_tasks = data_manager.get_all_tasks()

        # Use AI engine for enhanced estimation
        analysis = ai_engine.analyze_task(title, description, user_tasks)
        estimation = analysis['duration']

        # Track suggestion shown
        analytics_engine.track_suggestion_shown('enhanced_duration', estimation, estimation.get('confidence', 0))

        return jsonify({'success': True, 'estimation': estimation})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/suggestions/track-interaction', methods=['POST'])
def track_suggestion_interaction():
    """Track user interaction with suggestions"""
    try:
        data = request.get_json()
        interaction_type = data.get('type')  # 'accepted', 'rejected', 'ignored'
        suggestion_type = data.get('suggestion_type')
        suggestion_data = data.get('suggestion_data', {})
        response_time = data.get('response_time', 0)

        if not interaction_type or not suggestion_type:
            return jsonify({'success': False, 'error': 'Interaction type and suggestion type are required'}), 400

        # Track the interaction
        if interaction_type == 'accepted':
            analytics_engine.track_suggestion_accepted(suggestion_type, suggestion_data, response_time)
        elif interaction_type == 'rejected':
            analytics_engine.track_suggestion_rejected(suggestion_type, suggestion_data, response_time)

        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# Chatbot API Endpoints

@app.route('/api/chatbot/message', methods=['POST'])
def chatbot_message():
    """Process chatbot message and return AI response"""
    try:
        data = request.get_json()
        user_message = data.get('message', '').strip()

        if not user_message:
            return jsonify({'success': False, 'error': 'Message is required'}), 400

        # Process message through chatbot engine
        response = chatbot_engine.process_message(user_message)

        # Convert response to JSON-serializable format
        response_data = {
            'id': response.id,
            'content': response.content,
            'sender': response.sender,
            'timestamp': response.timestamp.isoformat(),
            'message_type': response.message_type,
            'metadata': response.metadata or {}
        }

        return jsonify({'success': True, 'response': response_data})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/chatbot/suggestions', methods=['GET'])
def chatbot_conversation_suggestions():
    """Get conversation starter suggestions"""
    try:
        suggestions = chatbot_engine.get_conversation_suggestions()
        return jsonify({'success': True, 'suggestions': suggestions})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/chatbot/context', methods=['GET'])
def chatbot_get_context():
    """Get current conversation context"""
    try:
        context = {
            'last_topic': chatbot_engine.context.last_topic,
            'session_history': chatbot_engine.context.session_history or []
        }
        return jsonify({'success': True, 'context': context})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/chatbot/reset', methods=['POST'])
def chatbot_reset_context():
    """Reset chatbot conversation context"""
    try:
        from chatbot_engine import ConversationContext
        chatbot_engine.context = ConversationContext()
        return jsonify({'success': True, 'message': 'Context reset successfully'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/import', methods=['POST'])
def import_data():
    """Import tasks from Excel or CSV file"""
    try:
        # Check if file was uploaded
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No file uploaded'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'}), 400

        # Check file type
        allowed_extensions = ['.xlsx', '.xls', '.csv']
        filename = file.filename or ''
        file_extension = '.' + filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
        if file_extension not in allowed_extensions:
            return jsonify({'success': False, 'error': f'Unsupported file type. Please use: {", ".join(allowed_extensions)}'}), 400

        # Try to import required packages
        try:
            import pandas as pd
            if file_extension in ['.xlsx', '.xls']:
                import openpyxl
        except ImportError as e:
            missing_pkg = str(e).split("'")[1] if "'" in str(e) else "required packages"
            return jsonify({
                'success': False,
                'error': f'Missing required package: {missing_pkg}. Please install pandas and openpyxl.'
            }), 500

        # Import the import utility
        try:
            from import_utility import ImportUtility
            importer = ImportUtility()
        except ImportError:
            return jsonify({
                'success': False,
                'error': 'Import utility not available. Please ensure import_utility.py exists.'
            }), 500

        # Save uploaded file temporarily
        import tempfile
        import os

        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as tmp_file:
            file.save(tmp_file.name)
            temp_file_path = tmp_file.name

        try:
            # Perform import
            result = importer.import_file(temp_file_path)

            if not result['success']:
                return jsonify({
                    'success': False,
                    'error': result.get('error', 'Unknown import error')
                }), 400

            # Get import preview data
            tasks = result['tasks']
            column_mapping = result['column_mapping']
            rows_processed = result.get('rows_processed', len(tasks))

            # Return preview data
            return jsonify({
                'success': True,
                'preview': {
                    'tasks': tasks,  # All tasks for frontend pagination
                    'total_tasks': len(tasks),
                    'column_mapping': column_mapping,
                    'rows_processed': rows_processed,
                    'file_name': file.filename or 'uploaded_file'
                }
            })

        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/import/confirm', methods=['POST'])
def confirm_import():
    """Confirm and save imported tasks"""
    try:
        data = request.get_json()
        tasks = data.get('tasks', [])

        if not tasks:
            return jsonify({'success': False, 'error': 'No tasks to import'}), 400

        # Import the import utility
        try:
            from import_utility import ImportUtility
            importer = ImportUtility()
        except ImportError:
            return jsonify({
                'success': False,
                'error': 'Import utility not available'
            }), 500

        # Save imported tasks
        result = importer.save_imported_tasks(tasks)

        if result['success']:
            return jsonify({
                'success': True,
                'message': f"Successfully imported {result['tasks_added']} tasks",
                'tasks_added': result['tasks_added'],
                'total_tasks': result['total_tasks']
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', 'Unknown save error')
            }), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.errorhandler(404)
def not_found(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('500.html'), 500

if __name__ == '__main__':
    print("Starting Flask application...")
    print("Access the app at: http://127.0.0.1:8000")
    print("Or try: http://localhost:8000")
    print("Press Ctrl+C to stop the server")
    print("-" * 50)

    try:
        app.run(debug=True, host='0.0.0.0', port=8000)
    except Exception as e:
        print(f"Error starting Flask app on port 8000: {e}")
        print("Trying port 5001...")
        try:
            print("Access the app at: http://127.0.0.1:5001")
            app.run(debug=True, host='0.0.0.0', port=5001)
        except Exception as e2:
            print(f"Port 5001 also failed: {e2}")
            print("Please check which ports are available or disable AirPlay Receiver in System Preferences -> Sharing")
