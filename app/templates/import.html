{% extends "base.html" %}

{% block title %}Import Data - AdhocLog{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 col-lg-9">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">📥 Import Excel/CSV Data</h4>
                    <a href="{{ url_for('tasks') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Tasks
                    </a>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-4">
                        Import tasks from Excel (.xlsx, .xls) or CSV files. The system will automatically detect columns and map them to the appropriate fields.
                    </p>

                    <!-- File Upload Section -->
                    <div id="upload-section">
                        <div class="mb-3">
                            <label for="fileInput" class="form-label">Select File</label>
                            <input type="file" class="form-control" id="fileInput" accept=".xlsx,.xls,.csv" required>
                            <div class="form-text">
                                Supported formats: Excel (.xlsx, .xls) and CSV (.csv)
                            </div>
                        </div>

                        <div class="mb-3">
                            <button type="button" class="btn btn-primary" id="uploadBtn" disabled>
                                <i class="bi bi-upload"></i> Upload and Preview
                            </button>
                        </div>
                    </div>

                    <!-- Progress Section -->
                    <div id="progress-section" style="display: none;">
                        <div class="mb-3">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar" style="width: 100%">
                                    Processing file...
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preview Section -->
                    <div id="preview-section" style="display: none;">
                        <hr>
                        <h5>📋 Import Preview</h5>

                        <!-- File Info -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body py-2">
                                        <h6 class="card-title mb-1">File Information</h6>
                                        <p class="card-text mb-0">
                                            <strong>File:</strong> <span id="preview-filename"></span><br>
                                            <strong>Rows processed:</strong> <span id="preview-rows"></span><br>
                                            <strong>Tasks found:</strong> <span id="preview-task-count"></span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body py-2">
                                        <h6 class="card-title mb-1">Column Mapping</h6>
                                        <div id="column-mapping" class="small">
                                            <!-- Column mapping will be populated here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Task Preview Table -->
                        <div class="table-responsive mb-3">
                            <table class="table table-sm table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Title</th>
                                        <th>Classification</th>
                                        <th>Date</th>
                                        <th>Time (min)</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody id="preview-table-body">
                                    <!-- Preview tasks will be populated here -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Import Notes -->
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle"></i> Import Notes:</h6>
                            <ul class="mb-0">
                                <li>Team member will be set to current user</li>
                                <li>Tasks will be added to existing data (not replaced)</li>
                                <li>Column mapping was detected automatically</li>
                                <li>Date formats will be standardized to YYYY-MM-DD</li>
                            </ul>
                        </div>

                        <!-- Confirm Import -->
                        <div class="text-end">
                            <button type="button" class="btn btn-outline-secondary me-2" id="cancelBtn">
                                <i class="bi bi-x-circle"></i> Cancel
                            </button>
                            <button type="button" class="btn btn-success" id="confirmBtn">
                                <i class="bi bi-check-circle"></i> Import Tasks
                            </button>
                        </div>
                    </div>

                    <!-- Success Section -->
                    <div id="success-section" style="display: none;">
                        <div class="alert alert-success text-center">
                            <h5><i class="bi bi-check-circle"></i> Import Successful!</h5>
                            <p class="mb-2" id="success-message"></p>
                            <a href="{{ url_for('tasks') }}" class="btn btn-primary">
                                <i class="bi bi-list-task"></i> View Tasks
                            </a>
                        </div>
                    </div>

                    <!-- Error Section -->
                    <div id="error-section" style="display: none;">
                        <div class="alert alert-danger">
                            <h6><i class="bi bi-exclamation-triangle"></i> Import Error</h6>
                            <p class="mb-0" id="error-message"></p>
                        </div>
                        <button type="button" class="btn btn-outline-primary" id="retryBtn">
                            <i class="bi bi-arrow-clockwise"></i> Try Again
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4 col-lg-3">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">📖 Import Guidelines</h6>
                </div>
                <div class="card-body">
                    <h6>Required Columns</h6>
                    <p class="small text-muted">
                        The system will automatically detect columns, but these are recommended:
                    </p>
                    <ul class="small">
                        <li><strong>Title/Task:</strong> Task description</li>
                        <li><strong>Classification/Type:</strong> Task category</li>
                        <li><strong>Date:</strong> Task date</li>
                        <li><strong>Time/Duration:</strong> Time in minutes</li>
                    </ul>

                    <h6 class="mt-3">Supported Classifications</h6>
                    <ul class="small">
                        {% for classification in config.CLASSIFICATIONS %}
                        <li>{{ classification }}</li>
                        {% endfor %}
                    </ul>

                    <h6 class="mt-3">Date Formats</h6>
                    <p class="small text-muted">
                        Supported date formats include:
                    </p>
                    <ul class="small">
                        <li>YYYY-MM-DD</li>
                        <li>MM/DD/YYYY</li>
                        <li>DD/MM/YYYY</li>
                        <li>MM-DD-YYYY</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('fileInput');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadSection = document.getElementById('upload-section');
    const progressSection = document.getElementById('progress-section');
    const previewSection = document.getElementById('preview-section');
    const successSection = document.getElementById('success-section');
    const errorSection = document.getElementById('error-section');
    const confirmBtn = document.getElementById('confirmBtn');
    const cancelBtn = document.getElementById('cancelBtn');
    const retryBtn = document.getElementById('retryBtn');

    let previewData = null;

    // Enable upload button when file is selected
    fileInput.addEventListener('change', function() {
        uploadBtn.disabled = !this.files.length;
    });

    // Upload and preview file
    uploadBtn.addEventListener('click', function() {
        const file = fileInput.files[0];
        if (!file) return;

        const formData = new FormData();
        formData.append('file', file);

        // Show progress
        uploadSection.style.display = 'none';
        progressSection.style.display = 'block';
        hideOtherSections();

        fetch('/api/import', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            progressSection.style.display = 'none';

            if (data.success) {
                showPreview(data.preview);
            } else {
                showError(data.error);
            }
        })
        .catch(error => {
            progressSection.style.display = 'none';
            showError('Upload failed: ' + error.message);
        });
    });

    // Confirm import
    confirmBtn.addEventListener('click', function() {
        if (!previewData) return;

        confirmBtn.disabled = true;
        confirmBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Importing...';

        fetch('/api/import/confirm', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                tasks: previewData.tasks
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message, data.tasks_added, data.total_tasks);
            } else {
                showError(data.error);
            }
        })
        .catch(error => {
            showError('Import failed: ' + error.message);
        })
        .finally(() => {
            confirmBtn.disabled = false;
            confirmBtn.innerHTML = '<i class="bi bi-check-circle"></i> Import Tasks';
        });
    });

    // Cancel/Reset
    function resetImport() {
        fileInput.value = '';
        uploadBtn.disabled = true;
        previewData = null;
        uploadSection.style.display = 'block';
        hideOtherSections();
    }

    cancelBtn.addEventListener('click', resetImport);
    retryBtn.addEventListener('click', resetImport);

    function hideOtherSections() {
        previewSection.style.display = 'none';
        successSection.style.display = 'none';
        errorSection.style.display = 'none';
    }

    function showPreview(preview) {
        previewData = preview;

        // Populate file info
        document.getElementById('preview-filename').textContent = preview.file_name;
        document.getElementById('preview-rows').textContent = preview.rows_processed;
        document.getElementById('preview-task-count').textContent = preview.total_tasks;

        // Populate column mapping
        const mappingDiv = document.getElementById('column-mapping');
        mappingDiv.innerHTML = '';
        for (const [field, column] of Object.entries(preview.column_mapping)) {
            mappingDiv.innerHTML += `<div>• ${field}: ${column}</div>`;
        }
        if (Object.keys(preview.column_mapping).length === 0) {
            mappingDiv.innerHTML = '<div class="text-muted">• No automatic mapping found</div>';
        }

        // Populate preview table
        const tbody = document.getElementById('preview-table-body');
        tbody.innerHTML = '';
        preview.tasks.forEach(task => {
            const row = document.createElement('tr');
            const description = (task.description || '').substring(0, 50) +
                                ((task.description || '').length > 50 ? '...' : '');
            row.innerHTML = `
                <td>${task.title || ''}</td>
                <td>${task.classification || ''}</td>
                <td>${task.date || ''}</td>
                <td>${task.est_time || ''}</td>
                <td class="small text-muted">${description}</td>
            `;
            tbody.appendChild(row);
        });

        previewSection.style.display = 'block';
    }

    function showSuccess(message, tasksAdded, totalTasks) {
        document.getElementById('success-message').innerHTML = `
            <strong>File:</strong> ${previewData.file_name}<br>
            <strong>Tasks imported:</strong> ${tasksAdded}<br>
            <strong>Total tasks:</strong> ${totalTasks}
        `;
        hideOtherSections();
        successSection.style.display = 'block';
    }

    function showError(errorMessage) {
        document.getElementById('error-message').textContent = errorMessage;
        hideOtherSections();
        errorSection.style.display = 'block';
    }
});
</script>
{% endblock %}
