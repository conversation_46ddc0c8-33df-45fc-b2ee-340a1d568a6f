{% extends "base.html" %}

{% block title %}All Tasks - AdhocLog{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-list"></i> All Tasks</h1>
            <div>
                <a href="{{ url_for('add_task') }}" class="btn btn-primary">
                    <i class="bi bi-plus"></i> Add Task
                </a>
                <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#importModal">
                    <i class="bi bi-upload"></i> Import
                </button>
                <a href="{{ url_for('export_csv', **filters) }}" class="btn btn-success">
                    <i class="bi bi-download"></i> Export CSV
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-funnel"></i> Filters</h5>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ url_for('task_list') }}">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date"
                                   value="{{ filters.get('start_date', '') }}">
                        </div>
                        <div class="col-md-3">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date"
                                   value="{{ filters.get('end_date', '') }}">
                        </div>
                        <div class="col-md-3">
                            <label for="classification" class="form-label">Classification</label>
                            <select class="form-select" id="classification" name="classification">
                                <option value="">All Classifications</option>
                                {% for cls in classifications %}
                                <option value="{{ cls }}" {% if filters.get('classification') == cls %}selected{% endif %}>
                                    {{ cls }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   placeholder="Title or description..." value="{{ filters.get('search', '') }}">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> Apply Filters
                            </button>
                            <a href="{{ url_for('task_list') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i> Clear Filters
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Tasks Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-table"></i> Tasks
                    <span class="badge bg-primary">{{ tasks|length }}</span>
                </h5>
                <div id="bulkActionsContainer" style="display: none;">
                    <button type="button" class="btn btn-warning btn-sm" id="bulkArchiveBtn">
                        <i class="bi bi-archive"></i> Archive Selected
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if tasks %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAllTasks" title="Select all tasks">
                                    </th>
                                    <th>Date</th>
                                    <th>Title</th>
                                    <th>Classification</th>
                                    <th>Category</th>
                                    <th>Description</th>
                                    <th>Time (min)</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for task in tasks %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="task-checkbox" value="{{ task.id }}" title="Select task">
                                    </td>
                                    <td>{{ task.date }}</td>
                                    <td><strong>{{ task.title }}</strong></td>
                                    <td>
                                        <span class="badge bg-secondary">{{ task.classification }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ task.category }}</span>
                                    </td>
                                    <td>
                                        <span class="text-truncate d-inline-block" style="max-width: 200px;"
                                              title="{{ task.description }}">
                                            {{ task.description }}
                                        </span>
                                    </td>
                                    <td>{{ task.est_time }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('edit_task', task_id=task.id) }}"
                                               class="btn btn-outline-primary" title="Edit">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-warning"
                                                    onclick="confirmArchive({{ task.id }}, '{{ task.title }}')" title="Archive">
                                                <i class="bi bi-archive"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% include 'pagination.html' %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-inbox fs-1 text-muted"></i>
                        <p class="text-muted mt-2">No tasks found matching your criteria.</p>
                        <a href="{{ url_for('add_task') }}" class="btn btn-primary">
                            <i class="bi bi-plus"></i> Add Your First Task
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Archive Confirmation Modal -->
<div class="modal fade" id="archiveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Archive</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to archive the task "<span id="taskTitle"></span>"?</p>
                <p class="text-muted">You can restore it later from the Archive section.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="archiveForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-warning">
                        <i class="bi bi-archive"></i> Archive
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Archive Confirmation Modal -->
<div class="modal fade" id="bulkArchiveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Bulk Archive</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to archive <span id="selectedTaskCount"></span> selected task(s)?</p>
                <p class="text-muted">You can restore them later from the Archive section.</p>
                <div id="selectedTasksList" class="mt-3"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" id="confirmBulkArchive">
                    <i class="bi bi-archive"></i> Archive Selected
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importModalLabel">
                    <i class="bi bi-upload"></i> Import Excel/CSV Data
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- File Upload Section -->
                <div id="upload-section">
                    <div class="mb-3">
                        <label for="fileInput" class="form-label">Select File</label>
                        <input type="file" class="form-control" id="fileInput" accept=".xlsx,.xls,.csv" required>
                        <div class="form-text">
                            Supported formats: Excel (.xlsx, .xls) and CSV (.csv)
                        </div>
                    </div>

                    <div class="mb-3">
                        <button type="button" class="btn btn-primary" id="uploadBtn" disabled>
                            <i class="bi bi-upload"></i> Upload and Preview
                        </button>
                    </div>
                </div>

                <!-- Progress Section -->
                <div id="progress-section" style="display: none;">
                    <div class="mb-3">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 role="progressbar" style="width: 100%">
                                Processing file...
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preview Section -->
                <div id="preview-section" style="display: none;">
                    <hr>
                    <h6>📋 Import Preview</h6>

                    <!-- File Info -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-1">File Information</h6>
                                    <p class="card-text mb-0 small">
                                        <strong>File:</strong> <span id="preview-filename"></span><br>
                                        <strong>Tasks found:</strong> <span id="preview-task-count"></span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-1">Column Mapping</h6>
                                    <div id="column-mapping" class="small">
                                        <!-- Column mapping will be populated here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Task Preview Table -->
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">Task Preview</h6>
                        <div class="small text-muted">
                            Showing <span id="preview-page-info">1-10</span> of <span id="preview-total-display">0</span>
                        </div>
                    </div>
                    <div class="table-responsive mb-3" style="max-height: 400px; overflow-y: auto;">
                        <table class="table table-sm table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>Title</th>
                                    <th>Team Member</th>
                                    <th>Classification</th>
                                    <th>Category</th>
                                    <th>Date</th>
                                    <th>Time (min)</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody id="preview-table-body">
                                <!-- Preview tasks will be populated here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Preview Pagination -->
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="prevPageBtn" disabled>
                                <i class="bi bi-chevron-left"></i> Previous
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="nextPageBtn" disabled>
                                Next <i class="bi bi-chevron-right"></i>
                            </button>
                        </div>
                        <div class="small text-muted">
                            Page <span id="current-page">1</span> of <span id="total-pages">1</span>
                        </div>
                    </div>

                    <!-- Import Notes -->
                    <div class="alert alert-info small">
                        <strong>Import Notes:</strong> Team member will be set to current user. Tasks will be added to existing data.
                    </div>
                </div>

                <!-- Success Section -->
                <div id="success-section" style="display: none;">
                    <div class="alert alert-success text-center">
                        <h6><i class="bi bi-check-circle"></i> Import Successful!</h6>
                        <p class="mb-2 small" id="success-message"></p>
                    </div>
                </div>

                <!-- Error Section -->
                <div id="error-section" style="display: none;">
                    <div class="alert alert-danger">
                        <h6><i class="bi bi-exclamation-triangle"></i> Import Error</h6>
                        <p class="mb-0 small" id="error-message"></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="closeModalBtn">Close</button>
                <button type="button" class="btn btn-outline-secondary" id="cancelBtn" style="display: none;">
                    <i class="bi bi-x-circle"></i> Cancel
                </button>
                <button type="button" class="btn btn-success" id="confirmBtn" style="display: none;">
                    <i class="bi bi-check-circle"></i> Import Tasks
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmArchive(taskId, taskTitle) {
    document.getElementById('taskTitle').textContent = taskTitle;
    document.getElementById('archiveForm').action = '/tasks/delete/' + taskId;
    new bootstrap.Modal(document.getElementById('archiveModal')).show();
}

// Bulk operations functionality
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAllTasks');
    const taskCheckboxes = document.querySelectorAll('.task-checkbox');
    const bulkActionsContainer = document.getElementById('bulkActionsContainer');
    const bulkArchiveBtn = document.getElementById('bulkArchiveBtn');

    // Handle select all
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            taskCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActionsVisibility();
        });
    }

    // Handle individual checkbox changes
    taskCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBulkActionsVisibility();
            updateSelectAllState();
        });
    });

    // Handle bulk archive button
    if (bulkArchiveBtn) {
        bulkArchiveBtn.addEventListener('click', function() {
            const selectedTasks = getSelectedTasks();
            if (selectedTasks.length > 0) {
                showBulkArchiveModal(selectedTasks);
            }
        });
    }

    // Handle bulk archive confirmation
    document.getElementById('confirmBulkArchive').addEventListener('click', function() {
        const selectedTasks = getSelectedTasks();
        performBulkArchive(selectedTasks);
    });

    function updateBulkActionsVisibility() {
        const selectedCount = document.querySelectorAll('.task-checkbox:checked').length;
        bulkActionsContainer.style.display = selectedCount > 0 ? 'block' : 'none';
    }

    function updateSelectAllState() {
        const checkedCount = document.querySelectorAll('.task-checkbox:checked').length;
        const totalCount = taskCheckboxes.length;

        selectAllCheckbox.checked = checkedCount === totalCount && totalCount > 0;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
    }

    function getSelectedTasks() {
        const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
        return Array.from(selectedCheckboxes).map(cb => ({
            id: parseInt(cb.value),
            title: cb.closest('tr').querySelector('td:nth-child(3) strong').textContent
        }));
    }

    function showBulkArchiveModal(selectedTasks) {
        document.getElementById('selectedTaskCount').textContent = selectedTasks.length;

        const tasksList = document.getElementById('selectedTasksList');
        tasksList.innerHTML = '<strong>Tasks to archive:</strong><ul class="mt-2">' +
            selectedTasks.map(task => `<li>${task.title}</li>`).join('') +
            '</ul>';

        new bootstrap.Modal(document.getElementById('bulkArchiveModal')).show();
    }

    function performBulkArchive(selectedTasks) {
        const taskIds = selectedTasks.map(task => task.id);

        // Show loading state
        const confirmBtn = document.getElementById('confirmBulkArchive');
        confirmBtn.disabled = true;
        confirmBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Archiving...';

        fetch('/tasks/bulk_archive', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ task_ids: taskIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Hide modal
                bootstrap.Modal.getInstance(document.getElementById('bulkArchiveModal')).hide();

                // Show success message
                showNotification(`Successfully archived ${data.archived_count} task(s)`, 'success');

                // Reload page after a short delay
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showNotification(data.message || 'Error archiving tasks', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error archiving tasks', 'error');
        })
        .finally(() => {
            // Reset button state
            confirmBtn.disabled = false;
            confirmBtn.innerHTML = '<i class="bi bi-archive"></i> Archive Selected';
        });
    }

    function showNotification(message, type) {
        const alertClass = type === 'error' ? 'alert-danger' : `alert-${type}`;
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alert);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }

    // Import functionality
    const fileInput = document.getElementById('fileInput');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadSection = document.getElementById('upload-section');
    const progressSection = document.getElementById('progress-section');
    const previewSection = document.getElementById('preview-section');
    const successSection = document.getElementById('success-section');
    const errorSection = document.getElementById('error-section');
    const confirmBtn = document.getElementById('confirmBtn');
    const cancelBtn = document.getElementById('cancelBtn');
    const closeModalBtn = document.getElementById('closeModalBtn');

    let previewData = null;

    // Enable upload button when file is selected
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            uploadBtn.disabled = !this.files.length;
        });
    }

    // Upload and preview file
    if (uploadBtn) {
        uploadBtn.addEventListener('click', function() {
            const file = fileInput.files[0];
            if (!file) return;

            const formData = new FormData();
            formData.append('file', file);

            // Show progress
            uploadSection.style.display = 'none';
            progressSection.style.display = 'block';
            hideImportSections();

            fetch('/api/import', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                progressSection.style.display = 'none';

                if (data.success) {
                    showImportPreview(data.preview);
                } else {
                    showImportError(data.error);
                }
            })
            .catch(error => {
                progressSection.style.display = 'none';
                showImportError('Upload failed: ' + error.message);
            });
        });
    }

    // Confirm import
    if (confirmBtn) {
        confirmBtn.addEventListener('click', function() {
            if (!previewData) return;

            confirmBtn.disabled = true;
            confirmBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Importing...';

            fetch('/api/import/confirm', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    tasks: previewData.tasks
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showImportSuccess(data.message, data.tasks_added, data.total_tasks);
                    // Reload the page after success to show new tasks
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    showImportError(data.error);
                }
            })
            .catch(error => {
                showImportError('Import failed: ' + error.message);
            })
            .finally(() => {
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = '<i class="bi bi-check-circle"></i> Import Tasks';
            });
        });
    }

    // Cancel/Reset import
    function resetImport() {
        fileInput.value = '';
        uploadBtn.disabled = true;
        previewData = null;
        uploadSection.style.display = 'block';
        hideImportSections();
    }

    if (cancelBtn) cancelBtn.addEventListener('click', resetImport);

    // Reset when modal is closed
    document.getElementById('importModal').addEventListener('hidden.bs.modal', resetImport);

    function hideImportSections() {
        previewSection.style.display = 'none';
        successSection.style.display = 'none';
        errorSection.style.display = 'none';
        confirmBtn.style.display = 'none';
        cancelBtn.style.display = 'none';
        closeModalBtn.style.display = 'block';
    }

    function showImportPreview(preview) {
        previewData = preview;

        // Populate file info
        document.getElementById('preview-filename').textContent = preview.file_name;
        document.getElementById('preview-task-count').textContent = preview.total_tasks;

        // Populate column mapping
        const mappingDiv = document.getElementById('column-mapping');
        mappingDiv.innerHTML = '';
        for (const [field, column] of Object.entries(preview.column_mapping)) {
            mappingDiv.innerHTML += `<div>• ${field}: ${column}</div>`;
        }
        if (Object.keys(preview.column_mapping).length === 0) {
            mappingDiv.innerHTML = '<div class="text-muted">• Auto-detected columns</div>';
        }

        // Set up pagination variables
        window.previewCurrentPage = 1;
        window.previewPerPage = 10;
        window.previewTotalPages = Math.ceil(preview.total_tasks / window.previewPerPage);

        // Update pagination info
        document.getElementById('preview-total-display').textContent = preview.total_tasks;
        document.getElementById('current-page').textContent = window.previewCurrentPage;
        document.getElementById('total-pages').textContent = window.previewTotalPages;

        // Populate preview table with pagination
        updatePreviewTable();
        setupPreviewPagination();

        previewSection.style.display = 'block';
        confirmBtn.style.display = 'inline-block';
        cancelBtn.style.display = 'inline-block';
        closeModalBtn.style.display = 'none';
    }

    function updatePreviewTable() {
        const tbody = document.getElementById('preview-table-body');
        tbody.innerHTML = '';

        const startIndex = (window.previewCurrentPage - 1) * window.previewPerPage;
        const endIndex = Math.min(startIndex + window.previewPerPage, previewData.total_tasks);
        const tasksToShow = previewData.tasks.slice(startIndex, endIndex);

        tasksToShow.forEach(task => {
            const row = document.createElement('tr');
            const description = (task.description || '').substring(0, 50) +
                              ((task.description || '').length > 50 ? '...' : '');
            row.innerHTML = `
                <td class="small">${task.title || ''}</td>
                <td class="small">${task.team_member || ''}</td>
                <td class="small">${task.classification || ''}</td>
                <td class="small">${task.category || ''}</td>
                <td class="small">${task.date || ''}</td>
                <td class="small">${task.est_time || ''}</td>
                <td class="small text-muted">${description}</td>
            `;
            tbody.appendChild(row);
        });

        // Update page info
        document.getElementById('preview-page-info').textContent =
            `${startIndex + 1}-${endIndex}`;
        document.getElementById('current-page').textContent = window.previewCurrentPage;

        // Update pagination buttons
        document.getElementById('prevPageBtn').disabled = window.previewCurrentPage <= 1;
        document.getElementById('nextPageBtn').disabled = window.previewCurrentPage >= window.previewTotalPages;
    }

    function setupPreviewPagination() {
        const prevBtn = document.getElementById('prevPageBtn');
        const nextBtn = document.getElementById('nextPageBtn');

        prevBtn.onclick = function() {
            if (window.previewCurrentPage > 1) {
                window.previewCurrentPage--;
                updatePreviewTable();
            }
        };

        nextBtn.onclick = function() {
            if (window.previewCurrentPage < window.previewTotalPages) {
                window.previewCurrentPage++;
                updatePreviewTable();
            }
        };
    }

    function showImportSuccess(message, tasksAdded, totalTasks) {
        document.getElementById('success-message').innerHTML = `
            <strong>File:</strong> ${previewData.file_name}<br>
            <strong>Tasks imported:</strong> ${tasksAdded}
        `;
        hideImportSections();
        successSection.style.display = 'block';
        closeModalBtn.style.display = 'block';
    }

    function showImportError(errorMessage) {
        document.getElementById('error-message').textContent = errorMessage;
        hideImportSections();
        errorSection.style.display = 'block';
        closeModalBtn.style.display = 'block';
    }
});
</script>
{% endblock %}
