{% extends "base.html" %}

{% block title %}Statistics - AdhocLog{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-2">
            <i class="bi bi-graph-up"></i> Statistics
        </h1>
        <p class="text-muted mb-4">Monthly task analytics and insights</p>
    </div>
</div>

<!-- Month Filter -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="d-flex align-items-center">
                    <label for="month" class="form-label me-3 mb-0">
                        <i class="bi bi-calendar3"></i> Select Month:
                    </label>
                    <select name="month" id="month" class="form-select me-3" onchange="this.form.submit()">
                        {% for option in month_options %}
                        <option value="{{ option.value }}" {% if option.value == selected_month %}selected{% endif %}>
                            {{ option.label }}
                        </option>
                        {% endfor %}
                    </select>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summary Stats -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card bg-primary text-white">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="bi bi-check-circle-fill fs-2 me-3"></i>
                    <div>
                        <h2 class="card-title mb-0 display-4 fw-bold">{{ total_tasks }}</h2>
                        <p class="card-text mb-0">Total Tasks</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card bg-success text-white">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="bi bi-clock-fill fs-2 me-3"></i>
                    <div>
                        <h2 class="card-title mb-0 display-4 fw-bold">{{ total_time }}</h2>
                        <p class="card-text mb-0">Total Minutes</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card bg-info text-white">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="bi bi-calendar-day fs-2 me-3"></i>
                    <div>
                        <h2 class="card-title mb-0 display-4 fw-bold">{{ avg_tasks_per_day }}</h2>
                        <p class="card-text mb-0">Avg Tasks/Day</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card bg-warning text-white">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="bi bi-speedometer2 fs-2 me-3"></i>
                    <div>
                        <h2 class="card-title mb-0 display-4 fw-bold">{{ avg_time_per_task }}</h2>
                        <p class="card-text mb-0">Avg Min/Task</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Classification Breakdown -->
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-pie-chart"></i> Tasks by Classification
                </h5>
            </div>
            <div class="card-body">
                {% if classification_stats %}
                    {% for classification, stats in classification_stats.items() %}
                    <div class="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                        <div>
                            <h6 class="mb-1">{{ classification }}</h6>
                            <small class="text-muted">{{ stats.count }} tasks</small>
                        </div>
                        <div class="text-end">
                            <h6 class="mb-1 text-primary">{{ stats.time }} min</h6>
                            <small class="text-muted">{{ "%.1f"|format(stats.time/total_time*100 if total_time > 0 else 0) }}%</small>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-pie-chart fs-1 text-muted"></i>
                        <p class="text-muted mt-2">No data for selected month</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Category Breakdown -->
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-bar-chart"></i> Tasks by Category
                </h5>
            </div>
            <div class="card-body">
                {% if category_stats %}
                    {% for category, stats in category_stats.items() %}
                    <div class="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                        <div>
                            <h6 class="mb-1">{{ category }}</h6>
                            <small class="text-muted">{{ stats.count }} tasks</small>
                        </div>
                        <div class="text-end">
                            <h6 class="mb-1 text-success">{{ stats.time }} min</h6>
                            <small class="text-muted">{{ "%.1f"|format(stats.time/total_time*100 if total_time > 0 else 0) }}%</small>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-bar-chart fs-1 text-muted"></i>
                        <p class="text-muted mt-2">No data for selected month</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Daily Activity -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-activity"></i> Daily Activity for {{ month_name }}
                </h5>
            </div>
            <div class="card-body">
                {% if daily_stats %}
                    <div class="row">
                        {% for date, stats in daily_stats.items() %}
                        <div class="col-md-4 col-lg-3 mb-3">
                            <div class="card border-left-primary h-100">
                                <div class="card-body p-3">
                                    <h6 class="card-title mb-1">{{ date }}</h6>
                                    <p class="card-text mb-1">
                                        <span class="badge bg-primary">{{ stats.count }} tasks</span>
                                    </p>
                                    <p class="card-text">
                                        <i class="bi bi-clock"></i> {{ stats.time }} min
                                    </p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-activity fs-1 text-muted"></i>
                        <p class="text-muted mt-2">No daily activity for selected month</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Tasks in Selected Month -->
{% if month_tasks %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-list-ul"></i> Recent Tasks from {{ month_name }}
                </h5>
                <a href="{{ url_for('task_list', start_date=selected_month+'-01') }}" class="btn btn-outline-primary btn-sm">
                    View All
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Title</th>
                                <th>Classification</th>
                                <th>Category</th>
                                <th>Time</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in month_tasks %}
                            <tr>
                                <td>{{ task.date }}</td>
                                <td>{{ task.title }}</td>
                                <td>
                                    <span class="badge bg-secondary">{{ task.classification }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ task.category }}</span>
                                </td>
                                <td>{{ task.est_time }} min</td>
                                <td>
                                    <a href="{{ url_for('edit_task', task_id=task.id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<style>
.border-left-primary {
    border-left: 4px solid #3b82f6 !important;
}

.stats-card {
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}
</style>
{% endblock %}
