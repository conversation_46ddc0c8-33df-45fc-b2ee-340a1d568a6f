{% extends "base.html" %}

{% block title %}Archive - AdhocLog{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-archive"></i> Archived Tasks</h1>
            <a href="{{ url_for('task_list') }}" class="btn btn-outline-primary">
                <i class="bi bi-arrow-left"></i> Back to Active Tasks
            </a>
        </div>
    </div>
</div>

<!-- Archived Tasks Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-archive-fill"></i> Archived Tasks
                    <span class="badge bg-secondary">{{ archived_tasks|length }}</span>
                </h5>
                <div id="bulkActionsContainer" style="display: none;">
                    <button type="button" class="btn btn-success btn-sm me-2" id="bulkRestoreBtn">
                        <i class="bi bi-arrow-counterclockwise"></i> Restore Selected
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" id="bulkDeleteBtn">
                        <i class="bi bi-trash"></i> Delete Selected
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if archived_tasks %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAllTasks" title="Select all tasks">
                                    </th>
                                    <th>Date</th>
                                    <th>Title</th>
                                    <th>Classification</th>
                                    <th>Category</th>
                                    <th>Description</th>
                                    <th>Time (min)</th>
                                    <th>Archived</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for task in archived_tasks %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="task-checkbox" value="{{ task.id }}" title="Select task">
                                    </td>
                                    <td>{{ task.date }}</td>
                                    <td><strong>{{ task.title }}</strong></td>
                                    <td>
                                        <span class="badge bg-secondary">{{ task.classification }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ task.category }}</span>
                                    </td>
                                    <td>
                                        <span class="text-truncate d-inline-block" style="max-width: 200px;"
                                              title="{{ task.description }}">
                                            {{ task.description }}
                                        </span>
                                    </td>
                                    <td>{{ task.est_time }}</td>
                                    <td>
                                        <small class="text-muted">{{ task.archived_date }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-success"
                                                    onclick="confirmRestore({{ task.id }}, '{{ task.title }}')" title="Restore">
                                                <i class="bi bi-arrow-counterclockwise"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger"
                                                    onclick="confirmPermanentDelete({{ task.id }}, '{{ task.title }}')" title="Permanently Delete">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% include 'pagination.html' %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-archive fs-1 text-muted"></i>
                        <p class="text-muted mt-2">No archived tasks found.</p>
                        <a href="{{ url_for('task_list') }}" class="btn btn-primary">
                            <i class="bi bi-list"></i> View Active Tasks
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Restore Confirmation Modal -->
<div class="modal fade" id="restoreModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Restore</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to restore the task "<span id="restoreTaskTitle"></span>"?</p>
                <p class="text-muted">This will move the task back to your active tasks list.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="restoreForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-arrow-counterclockwise"></i> Restore
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Permanent Delete Confirmation Modal -->
<div class="modal fade" id="permanentDeleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Permanent Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to <strong>permanently delete</strong> the task "<span id="deleteTaskTitle"></span>"?</p>
                <p class="text-danger"><strong>Warning:</strong> This action cannot be undone!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="permanentDeleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash"></i> Permanently Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Restore Confirmation Modal -->
<div class="modal fade" id="bulkRestoreModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Bulk Restore</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to restore <span id="selectedTaskCountRestore"></span> selected task(s)?</p>
                <p class="text-muted">These tasks will be moved back to your active tasks list.</p>
                <div id="selectedTasksListRestore" class="mt-3"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirmBulkRestore">
                    <i class="bi bi-arrow-counterclockwise"></i> Restore Selected
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Delete Confirmation Modal -->
<div class="modal fade" id="bulkDeleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Bulk Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to <strong>permanently delete</strong> <span id="selectedTaskCountDelete"></span> selected task(s)?</p>
                <p class="text-danger"><strong>Warning:</strong> This action cannot be undone!</p>
                <div id="selectedTasksListDelete" class="mt-3"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmBulkDelete">
                    <i class="bi bi-trash"></i> Delete Selected
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmRestore(taskId, taskTitle) {
    document.getElementById('restoreTaskTitle').textContent = taskTitle;
    document.getElementById('restoreForm').action = '/tasks/restore/' + taskId;
    new bootstrap.Modal(document.getElementById('restoreModal')).show();
}

function confirmPermanentDelete(taskId, taskTitle) {
    document.getElementById('deleteTaskTitle').textContent = taskTitle;
    document.getElementById('permanentDeleteForm').action = '/tasks/permanent_delete/' + taskId;
    new bootstrap.Modal(document.getElementById('permanentDeleteModal')).show();
}

// Bulk operations functionality
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAllTasks');
    const taskCheckboxes = document.querySelectorAll('.task-checkbox');
    const bulkActionsContainer = document.getElementById('bulkActionsContainer');
    const bulkRestoreBtn = document.getElementById('bulkRestoreBtn');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');

    // Handle select all
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            taskCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActionsVisibility();
        });
    }

    // Handle individual checkbox changes
    taskCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBulkActionsVisibility();
            updateSelectAllState();
        });
    });

    // Handle bulk restore button
    if (bulkRestoreBtn) {
        bulkRestoreBtn.addEventListener('click', function() {
            const selectedTasks = getSelectedTasks();
            if (selectedTasks.length > 0) {
                showBulkRestoreModal(selectedTasks);
            }
        });
    }

    // Handle bulk delete button
    if (bulkDeleteBtn) {
        bulkDeleteBtn.addEventListener('click', function() {
            const selectedTasks = getSelectedTasks();
            if (selectedTasks.length > 0) {
                showBulkDeleteModal(selectedTasks);
            }
        });
    }

    // Handle bulk restore confirmation
    document.getElementById('confirmBulkRestore').addEventListener('click', function() {
        const selectedTasks = getSelectedTasks();
        performBulkRestore(selectedTasks);
    });

    // Handle bulk delete confirmation
    document.getElementById('confirmBulkDelete').addEventListener('click', function() {
        const selectedTasks = getSelectedTasks();
        performBulkDelete(selectedTasks);
    });

    function updateBulkActionsVisibility() {
        const selectedCount = document.querySelectorAll('.task-checkbox:checked').length;
        bulkActionsContainer.style.display = selectedCount > 0 ? 'block' : 'none';
    }

    function updateSelectAllState() {
        const checkedCount = document.querySelectorAll('.task-checkbox:checked').length;
        const totalCount = taskCheckboxes.length;

        selectAllCheckbox.checked = checkedCount === totalCount && totalCount > 0;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
    }

    function getSelectedTasks() {
        const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
        return Array.from(selectedCheckboxes).map(cb => ({
            id: parseInt(cb.value),
            title: cb.closest('tr').querySelector('td:nth-child(3) strong').textContent
        }));
    }

    function showBulkRestoreModal(selectedTasks) {
        document.getElementById('selectedTaskCountRestore').textContent = selectedTasks.length;

        const tasksList = document.getElementById('selectedTasksListRestore');
        tasksList.innerHTML = '<strong>Tasks to restore:</strong><ul class="mt-2">' +
            selectedTasks.map(task => `<li>${task.title}</li>`).join('') +
            '</ul>';

        new bootstrap.Modal(document.getElementById('bulkRestoreModal')).show();
    }

    function showBulkDeleteModal(selectedTasks) {
        document.getElementById('selectedTaskCountDelete').textContent = selectedTasks.length;

        const tasksList = document.getElementById('selectedTasksListDelete');
        tasksList.innerHTML = '<strong>Tasks to permanently delete:</strong><ul class="mt-2">' +
            selectedTasks.map(task => `<li>${task.title}</li>`).join('') +
            '</ul>';

        new bootstrap.Modal(document.getElementById('bulkDeleteModal')).show();
    }

    function performBulkRestore(selectedTasks) {
        const taskIds = selectedTasks.map(task => task.id);

        // Show loading state
        const confirmBtn = document.getElementById('confirmBulkRestore');
        confirmBtn.disabled = true;
        confirmBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Restoring...';

        fetch('/tasks/bulk_restore', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ task_ids: taskIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Hide modal
                bootstrap.Modal.getInstance(document.getElementById('bulkRestoreModal')).hide();

                // Show success message
                showNotification(`Successfully restored ${data.restored_count} task(s)`, 'success');

                // Reload page after a short delay
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showNotification(data.message || 'Error restoring tasks', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error restoring tasks', 'error');
        })
        .finally(() => {
            // Reset button state
            confirmBtn.disabled = false;
            confirmBtn.innerHTML = '<i class="bi bi-arrow-counterclockwise"></i> Restore Selected';
        });
    }

    function performBulkDelete(selectedTasks) {
        const taskIds = selectedTasks.map(task => task.id);

        // Show loading state
        const confirmBtn = document.getElementById('confirmBulkDelete');
        confirmBtn.disabled = true;
        confirmBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Deleting...';

        fetch('/tasks/bulk_delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ task_ids: taskIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Hide modal
                bootstrap.Modal.getInstance(document.getElementById('bulkDeleteModal')).hide();

                // Show success message
                showNotification(`Successfully deleted ${data.deleted_count} task(s)`, 'success');

                // Reload page after a short delay
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showNotification(data.message || 'Error deleting tasks', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error deleting tasks', 'error');
        })
        .finally(() => {
            // Reset button state
            confirmBtn.disabled = false;
            confirmBtn.innerHTML = '<i class="bi bi-trash"></i> Delete Selected';
        });
    }

    function showNotification(message, type) {
        const alertClass = type === 'error' ? 'alert-danger' : `alert-${type}`;
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alert);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
});
</script>
{% endblock %}
