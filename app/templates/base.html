<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}AdhocLog{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{{ url_for('static', filename='images/favicon-96x96.png') }}" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='images/favicon.svg') }}" />
    <link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}" />
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='images/apple-touch-icon.png') }}" />
    <meta name="apple-mobile-web-app-title" content="AdhocLog" />
    <link rel="manifest" href="{{ url_for('static', filename='images/site.webmanifest') }}" />

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- AI Chatbot Styles -->
    <link href="{{ url_for('static', filename='css/ai_chatbot.css') }}" rel="stylesheet">

    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            background-color: #f8fafc;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .navbar {
            background: var(--primary-gradient) !important;
            box-shadow: var(--card-shadow);
            border: none;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.25rem;
            letter-spacing: -0.025em;
        }

        .nav-link {
            font-weight: 500;
            transition: var(--transition);
            border-radius: 8px;
            margin: 0 4px;
        }

        .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-1px);
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            transition: var(--transition);
            background: white;
        }

        .card:hover {
            box-shadow: var(--card-shadow-hover);
            transform: translateY(-2px);
        }

        .card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid #e2e8f0;
            border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
            font-weight: 600;
            color: #334155;
        }

        .task-card {
            transition: var(--transition);
            border-left: 4px solid #3b82f6;
        }

        .task-card:hover {
            transform: translateY(-3px);
            border-left-color: #1d4ed8;
        }

        .stats-card {
            background: var(--primary-gradient);
            color: white;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: var(--transition);
            border: none;
            padding: 0.5rem 1rem;
        }

        .btn-primary {
            background: var(--primary-gradient);
            box-shadow: 0 4px 14px 0 rgba(102, 126, 234, 0.39);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px 0 rgba(102, 126, 234, 0.5);
        }

        .btn-success {
            background: var(--success-gradient);
            box-shadow: 0 4px 14px 0 rgba(79, 172, 254, 0.39);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px 0 rgba(79, 172, 254, 0.5);
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            transition: var(--transition);
            padding: 0.75rem 1rem;
        }

        .form-control:focus, .form-select:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .badge {
            border-radius: 6px;
            font-weight: 500;
            padding: 0.35em 0.65em;
        }

        .table {
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .table th {
            background-color: #f8fafc;
            border-bottom: 2px solid #e2e8f0;
            font-weight: 600;
            color: #475569;
        }

        .footer {
            margin-top: auto;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border-top: 1px solid #e2e8f0;
        }

        .alert {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--card-shadow);
        }

        .list-group-item {
            border-radius: 8px !important;
            border: 1px solid #e2e8f0;
            margin-bottom: 4px;
            transition: var(--transition);
        }

        .list-group-item:hover {
            background-color: #f8fafc;
            transform: translateX(4px);
        }

        .spinner-border {
            color: #3b82f6;
        }

        h1, h2, h3, h4, h5, h6 {
            color: #1e293b;
            font-weight: 700;
            letter-spacing: -0.025em;
        }

        .text-muted {
            color: #64748b !important;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <img src="{{ url_for('static', filename='images/favicon.svg') }}" alt="AdhocLog" width="24" height="24" class="me-2"> AdhocLog
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}"
                           href="{{ url_for('dashboard') }}">
                            <i class="bi bi-house"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'task_list' %}active{% endif %}"
                           href="{{ url_for('task_list') }}">
                            <i class="bi bi-list"></i> All Tasks
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'add_task' %}active{% endif %}"
                           href="{{ url_for('add_task') }}">
                            <i class="bi bi-plus-circle"></i> Add Task
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'statistics' %}active{% endif %}"
                           href="{{ url_for('statistics') }}">
                            <i class="bi bi-graph-up"></i> Statistics
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'archive_list' %}active{% endif %}"
                           href="{{ url_for('archive_list') }}">
                            <i class="bi bi-archive"></i> Archive
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-robot"></i> AdBot
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="AIChatbotUtils.openWithTaskCreation(); return false;">
                                <i class="bi bi-plus-circle me-2"></i>Create Tasks
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="AIChatbotUtils.openWithScheduleRequest(); return false;">
                                <i class="bi bi-calendar me-2"></i>Show Schedule
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="AIChatbotUtils.openWithSuggestions(); return false;">
                                <i class="bi bi-lightbulb me-2"></i>Get Suggestions
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="AIChatbotUtils.resetChat(); return false;">
                                <i class="bi bi-arrow-clockwise me-2"></i>Reset Chat
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person"></i> {{ current_user or 'User' }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('export_csv') }}">
                                <i class="bi bi-download"></i> Export CSV
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    <div class="container mt-3">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <!-- Main Content -->
    <main class="container my-4 flex-grow-1">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer bg-light py-3 mt-5">
        <div class="container text-center">
            <span class="text-muted">AdhocLog &copy; 2025 | Client Systems Engineering</span>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AI Chatbot -->
    <script src="{{ url_for('static', filename='js/ai_chatbot.js') }}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
