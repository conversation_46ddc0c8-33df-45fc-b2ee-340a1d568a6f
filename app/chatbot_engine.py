"""
AI Chatbot Engine for AdhocLog
Provides conversational interface for task management and AI assistance

Features:
- Natural language task creation
- Conversational task management
- Intelligent suggestions and help
- Context-aware responses
- Privacy-focused local processing
"""

import re
import json
import calendar
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional, Tu<PERSON>
from dataclasses import dataclass, field
from .ai_engine import AITaskEngine
from .data_manager import DataManager
from .analytics import AnalyticsEngine


@dataclass
class ChatMessage:
    """Data class for chat messages"""
    id: str
    content: str
    sender: str  # 'user' or 'assistant'
    timestamp: datetime
    message_type: str  # 'text', 'task_created', 'task_list', 'suggestion'
    metadata: Optional[Dict] = None


@dataclass
class ConversationContext:
    """Maintains conversation context and state"""
    last_topic: Optional[str] = None
    pending_tasks: Optional[List[Dict]] = None
    user_preferences: Optional[Dict] = None
    session_history: Optional[List[str]] = None

    # Enhanced context tracking
    last_intent: Optional[str] = None
    last_response: Optional[ChatMessage] = None
    last_user_message: Optional[str] = None
    conversation_history: List[Dict] = field(default_factory=list)
    current_filter_context: Optional[Dict] = None  # Track current filtered results
    last_mentioned_date: Optional[str] = None
    last_mentioned_tasks: Optional[List[Dict]] = None
    follow_up_count: int = 0
    last_offer_type: Optional[str] = None  # Track what the bot offered to do

    def add_interaction(self, user_message: str, bot_response: ChatMessage, intent: str):
        """Add an interaction to conversation history"""
        interaction = {
            'timestamp': datetime.now().isoformat(),
            'user_message': user_message,
            'bot_response': bot_response.content,
            'intent': intent,
            'response_type': bot_response.message_type,
            'metadata': bot_response.metadata
        }
        self.conversation_history.append(interaction)

        # Keep only last 10 interactions to prevent memory bloat
        if len(self.conversation_history) > 10:
            self.conversation_history = self.conversation_history[-10:]

        # Update current context
        self.last_intent = intent
        self.last_response = bot_response
        self.last_user_message = user_message

        # Extract useful context from the interaction
        if intent == 'show_tasks_by_date' and bot_response.metadata:
            self.last_mentioned_date = bot_response.metadata.get('date')
            self.last_mentioned_tasks = bot_response.metadata.get('tasks')
        elif intent in ['show_tasks_by_status', 'show_tasks_by_classification'] and bot_response.metadata:
            self.current_filter_context = bot_response.metadata
            self.last_mentioned_tasks = bot_response.metadata.get('tasks')

    def get_recent_context(self, limit: int = 3) -> List[Dict]:
        """Get recent conversation context"""
        return self.conversation_history[-limit:] if self.conversation_history else []

    def has_recent_task_context(self) -> bool:
        """Check if we have recent task-related context"""
        return (self.last_mentioned_tasks is not None or
                self.current_filter_context is not None or
                self.last_intent in ['show_tasks', 'show_tasks_by_date', 'show_tasks_by_status', 'show_tasks_by_classification'])

    def clear_context(self):
        """Clear conversation context (for new sessions)"""
        self.last_topic = None
        self.last_intent = None
        self.last_response = None
        self.last_user_message = None
        self.current_filter_context = None
        self.last_mentioned_date = None
        self.last_mentioned_tasks = None
        self.follow_up_count = 0
        self.last_offer_type = None


class ChatbotEngine:
    """AI Chatbot for conversational task management"""

    def __init__(self, data_manager: DataManager, ai_engine: AITaskEngine, analytics_engine: AnalyticsEngine):
        self.data_manager = data_manager
        self.ai_engine = ai_engine
        self.analytics_engine = analytics_engine
        self.context = ConversationContext()

        # Domain guard-rails blacklist
        self.domain_blacklist = [
            'patient', 'medication', 'blood', 'bank', 'investment', 'medical', 'doctor',
            'hospital', 'insurance', 'loan', 'credit', 'health', 'symptom', 'disease',
            'diagnosis', 'treatment', 'pharmacy', 'prescription', 'surgery', 'therapy',
            'mortgage', 'stock', 'trading', 'portfolio', 'finance', 'revenue', 'profit',
            'weather', 'traffic', 'restaurant', 'recipe', 'cooking', 'sports', 'game',
            'movie', 'music', 'celebrity', 'news', 'politics', 'election', 'vote'
        ]

        # Command patterns for natural language understanding
        self.command_patterns = {
            'out_of_scope': [
                r'\b(?:' + '|'.join(self.domain_blacklist) + r')\b',
                r'(?:medical|health|financial|personal).*(?:advice|help|assistance)',
                r'(?:weather|traffic|news|sports|entertainment).*(?:today|tomorrow)',
                r'(?:cook|recipe|food|restaurant).*(?:suggestion|recommendation)'
            ],
            'create_task': [
                r'create (?:a )?task (?:for |to )?(.+)',
                r'add (?:a )?task (?:for |to )?(.+)',
                r'make (?:a )?task (?:for |to )?(.+)',
                r'new task (?:for |to )?(.+)'
            ],
            'create_multiple_tasks': [
                r'create (\d+) tasks? (?:for |to )?(.+)',
                r'generate (\d+) tasks? (?:for |to )?(.+)',
                r'make (\d+) tasks? (?:for |to )?(.+)',
                r'break down (.+) into (\d+) tasks?'
            ],
            'break_down_task': [
                r'break down (.+)',
                r'split (.+) into subtasks',
                r'create subtasks for (.+)',
                r'decompose (.+)'
            ],
            'show_tasks': [
                r'show (?:me )?(?:my )?tasks?',
                r'list (?:my )?tasks?',
                r'what are my tasks?',
                r'(?:show|list) (?:all )?(?:my )?tasks?'
            ],
            'show_tasks_by_date': [
                # Specific date formats
                r'(?:show|list) (?:me )?(?:the )?tasks? (?:for |from |on )?(?:january|february|march|april|may|june|july|august|september|october|november|december) \d{1,2}',
                r'(?:show|list) (?:me )?(?:the )?tasks? (?:for |from |on )?(\d{1,2}/\d{1,2}/\d{4})',
                r'(?:show|list) (?:me )?(?:the )?tasks? (?:for |from |on )?(\d{4}-\d{2}-\d{2})',
                r'(?:show|list) (?:me )?(?:the )?tasks? (?:for |from |on )?(\d{1,2}/\d{1,2})',
                r'tasks? (?:for |from |on )?(?:january|february|march|april|may|june|july|august|september|october|november|december) \d{1,2}',
                r'tasks? (?:for |from |on )?(\d{1,2}/\d{1,2}/\d{4})',
                r'tasks? (?:for |from |on )?(\d{1,2}/\d{1,2})',
                # Relative dates
                r'(?:show|list) (?:me )?(?:my )?tasks? (?:for |from |on )?(?:today|yesterday|tomorrow)',
                r'(?:show|list) (?:me )?(?:my )?tasks? (?:for |from |on )?(?:this week|last week|next week)',
                r'(?:yesterday|today|tomorrow)\'?s? tasks?',
                r'tasks? (?:for |from |on )?(?:yesterday|today|tomorrow)',
                r'tasks? (?:for |from |on )?(?:this week|last week|next week)',
                r'what did i work on (?:yesterday|today|last week)',
                r'what tasks? (?:did i have|were scheduled) (?:yesterday|today|last week)',
                r'what tasks? do i have (?:today|yesterday|tomorrow)',
                r'(?:show|list|what) (?:all )?(?:my )?tasks? (?:from |for )?(?:today|yesterday|tomorrow)',
                r'tasks? (?:i have )?(?:today|yesterday|tomorrow)',
                # Enhanced natural language temporal expressions
                r'(?:show|list) (?:me )?(?:my )?tasks? (?:from |for )?(?:\d+ )?(?:days?|weeks?|months?|years?) ago',
                r'(?:show|list) (?:me )?(?:my )?tasks? (?:from |for )?(?:last|past|previous) (?:\d+ )?(?:days?|weeks?|months?|years?)',
                r'(?:show|list) (?:me )?(?:my )?tasks? (?:from |for )?(?:this|current) (?:week|month|year|quarter)',
                r'(?:show|list) (?:me )?(?:my )?tasks? (?:from |for )?(?:next|upcoming) (?:week|month|year|quarter)',
                r'what (?:did i|was i) (?:work on|working on|doing) (?:\d+ )?(?:days?|weeks?|months?|years?) ago',
                r'what (?:did i|was i) (?:work on|working on|doing) (?:last|past) (?:\d+ )?(?:days?|weeks?|months?|years?)',
                r'what (?:am i|will i be) (?:work on|working on|doing) (?:next|upcoming) (?:\d+ )?(?:days?|weeks?|months?|years?)',
                r'tasks? (?:from |for )?(?:the )?(?:last|past|previous) (?:\d+ )?(?:days?|weeks?|months?|years?)',
                r'tasks? (?:from |for )?(?:the )?(?:next|upcoming|following) (?:\d+ )?(?:days?|weeks?|months?|years?)',
                # Quarter patterns
                r'(?:show|list) (?:me )?(?:my )?tasks? (?:from |for )?(?:Q[1-4]|quarter [1-4]|(?:first|second|third|fourth) quarter)',
                r'tasks? (?:from |for )?(?:Q[1-4]|quarter [1-4]|(?:first|second|third|fourth) quarter)',
                # Week number patterns
                r'(?:show|list) (?:me )?(?:my )?tasks? (?:from |for )?(?:week )\d{1,2}',
                r'tasks? (?:from |for )?(?:week )\d{1,2}',
                # More flexible patterns for time periods
                r'(?:show|list) (?:me )?(?:my )?work (?:from |for )?(?:last|past|previous) (?:\d+ )?(?:days?|weeks?|months?|years?)',
                r'(?:show|list) (?:me )?(?:my )?work (?:from |for )?(?:\d+ )?(?:days?|weeks?|months?|years?) ago',
                r'what (?:have i|did i) (?:been )?(?:work on|working on|doing) (?:in |during )?(?:the )?(?:last|past) (?:\d+ )?(?:days?|weeks?|months?|years?)',
                r'what (?:was|were) (?:my )?(?:tasks?|work|activities) (?:in |during )?(?:the )?(?:last|past) (?:\d+ )?(?:days?|weeks?|months?|years?)',
                # Scheduled/planned patterns with temporal expressions
                r'what (?:do i have|is) scheduled (?:for |in )?(?:the )?(?:next|upcoming) (?:\d+ )?(?:days?|weeks?|months?)',
                r'what (?:did i have|was) scheduled (?:for |in )?(?:the )?(?:last|past) (?:\d+ )?(?:days?|weeks?|months?)',
                r'(?:show|list) (?:me )?(?:my )?schedule (?:for |from )?(?:the )?(?:last|past|next|upcoming) (?:\d+ )?(?:days?|weeks?|months?)'
            ],
            'show_tasks_by_status': [
                r'(?:show|list) (?:me )?(?:my )?(?:completed|finished|done) tasks?',
                r'(?:show|list) (?:me )?(?:my )?(?:pending|incomplete|unfinished) tasks?',
                r'(?:show|list) (?:me )?(?:my )?(?:high|medium|low) priority tasks?',
                r'what tasks? (?:have i|did i) (?:completed?|finished?)',
                r'what tasks? (?:are|remain) (?:incomplete|unfinished|pending)',
                r'(?:show|list) (?:me )?(?:my )?completed tasks?',
                r'(?:show|list) (?:me )?(?:my )?pending tasks?'
            ],
            'show_tasks_by_classification': [
                r'(?:show|list) (?:my )?(?:planning|execution|business|operational|offline|processing) tasks?',
                r'what (?:planning|execution|business|operational|offline|processing) tasks? do i have',
                r'(?:show|list) (?:all )?(?:my )?(?:planning|execution|business|operational|offline|processing) related tasks?',
                r'(?:show|list) (?:my )?(.+) (?:type|category|classification) tasks?'
            ],
            'edit_task': [
                r'(?:edit|modify|change|update) task (?:#)?(\d+)',
                r'(?:edit|modify|change|update) (.+) task',
                r'change (?:the )?(?:title|name|description|time) (?:of )?task (?:#)?(\d+)',
                r'update task (?:#)?(\d+) (?:to|with) (.+)'
            ],
            'delete_task': [
                r'(?:delete|remove) task (?:#)?(\d+)',
                r'(?:delete|remove) (?:the )?(.+) task',
                r'get rid of task (?:#)?(\d+)'
            ],
            'complete_task': [
                r'(?:mark |complete |finish )(?:task )?(?:#)?(\d+)(?: as complete)?',
                r'done (?:with )?(?:task )?(?:#)?(\d+)',
                r'finished (?:task )?(?:#)?(\d+)',
                r'(?:complete|finish) (.+) task'
            ],
            'search_tasks': [
                # Most specific patterns first (order matters!)
                r'search for (.+)',
                r'find (.+) (?:task|tasks)',
                r'look for (.+) (?:task|tasks)',
                r'search (.+) (?:task|tasks)',
                r'look for (.+)',
                r'search (.+)',
                r'find (.+)',
                r'locate (.+)',
                # Complex patterns with connectors
                r'(?:search|find|look for) (?:for )?tasks? (?:with|containing|about|related to) (.+)',
                r'find (?:me )?tasks? (?:related to|about|containing|with) (.+)',
                r'(?:search|look for|locate) (.+) in my tasks?',
                r'show (?:me )?tasks? (?:that contain|with|about) (.+)',
                r'(?:any|which) tasks? (?:contain|have|include|mention) (.+)',
                r'tasks? (?:with|containing|about|related to) (.+)',
                r'find tasks? (?:that (?:include|contain|mention)|about) (.+)',
                r'(?:search|find|look) (?:through|in) (?:my )?tasks? (?:for|about) (.+)',
                r'do i have (?:any )?tasks? (?:about|related to|containing) (.+)',
                r'what tasks? (?:do i have|are there) (?:about|for|regarding) (.+)',
                # Also handle "X tasks" patterns (be careful about word order)
                r'(.+) tasks?',
                # Patterns for search commands without terms (these should trigger the "what to search" question)
                r'^(?:search|find|look for|locate)$',
                r'^(?:search|find|look) for$',
                r'^(?:search|find|look for) tasks?$',
                r'^(?:search|find|look) (?:through|in) (?:my )?tasks?$'
            ],
            'export_tasks': [
                r'export (?:my )?tasks?',
                r'download (?:my )?tasks?',
                r'save (?:my )?tasks? (?:to|as) (?:csv|excel|file)',
                r'generate (?:a )?report (?:of )?(?:my )?tasks?'
            ],
            'get_suggestions': [
                r'what should i work on (?:next)?',
                r'suggest (?:a )?task',
                r'recommend (?:something|tasks?)',
                r'help me (?:choose|decide|prioritize)',
                r'what\'s next',
                r'what to do next'
            ],
            'schedule_optimization': [
                r'optimize my schedule',
                r'how should i plan my (?:day|week)',
                r'best order for my tasks',
                r'prioritize my tasks',
                r'organize my (?:day|schedule|tasks)',
                r'help me (?:plan|organize) my (?:day|week|schedule)'
            ],
            'analytics_request': [
                r'show (?:me )?(?:my )?(?:productivity|stats|analytics)',
                r'how am i doing',
                r'my performance',
                r'task insights',
                r'(?:productivity|performance) (?:stats|statistics|metrics)',
                r'how (?:productive|efficient) (?:am i|have i been)'
            ],
            'time_tracking': [
                r'how much time (?:did i|have i|do i) (?:spend|spent|have) (?:on|working|scheduled)',
                r'(?:time|hours) spent (?:on|working) (?:today|yesterday|this week)',
                r'total time for (.+)',
                r'track my time',
                r'how much time (?:do i have|did i spend|have i spent) (?:today|yesterday|this week)',
                r'time (?:scheduled|planned|estimated) (?:for )?(?:today|yesterday|this week)',
                r'what\'s my (?:time|schedule) (?:for )?(?:today|yesterday|this week)',
                r'how long (?:will|did) (?:today|yesterday)\'?s? tasks take'
            ],
            'time_summary': [
                r'how many minutes (?:did i spend|last) (?:quarter|month|year)',
                r'time spent (?:in|during) (?:Q[1-4]|quarter [1-4]|last month|this month|last year|this year)',
                r'total time (?:for|in|during) (?:Q[1-4]|quarter [1-4]|last month|this month|last year|this year)',
                r'(?:show|tell me) time (?:breakdown|summary) (?:for|in) (?:Q[1-4]|quarter [1-4]|last month|this month|last year|this year)',
                r'how much time (?:in|during) (?:week \d+|last week|this week)',
                # Enhanced natural language time summary patterns
                r'how much time (?:did i spend|have i spent) (?:in |during )?(?:the )?(?:last|past|previous) (?:\d+ )?(?:days?|weeks?|months?|years?)',
                r'(?:total )?time (?:spent|worked) (?:in |during )?(?:the )?(?:last|past|previous) (?:\d+ )?(?:days?|weeks?|months?|years?)',
                r'(?:show|tell me) (?:my )?(?:time|work) (?:breakdown|summary) (?:for |from )?(?:the )?(?:last|past|previous) (?:\d+ )?(?:days?|weeks?|months?|years?)',
                r'how many (?:hours|minutes) (?:did i work|have i worked) (?:in |during )?(?:the )?(?:last|past|previous) (?:\d+ )?(?:days?|weeks?|months?|years?)',
                r'what\'s my (?:total )?(?:time|work|effort) (?:for |from )?(?:the )?(?:last|past|previous) (?:\d+ )?(?:days?|weeks?|months?|years?)',
                r'how productive (?:was i|have i been) (?:in |during )?(?:the )?(?:last|past|previous) (?:\d+ )?(?:days?|weeks?|months?|years?)',
                r'(?:productivity|performance) (?:summary|report|breakdown) (?:for |from )?(?:the )?(?:last|past|previous) (?:\d+ )?(?:days?|weeks?|months?|years?)',
                # Time analysis for future periods
                r'how much time (?:do i have|is scheduled) (?:for |in )?(?:the )?(?:next|upcoming) (?:\d+ )?(?:days?|weeks?|months?)',
                r'what\'s my (?:workload|schedule) (?:for |in )?(?:the )?(?:next|upcoming) (?:\d+ )?(?:days?|weeks?|months?)',
                # Specific temporal ranges
                r'(?:time|work) (?:analysis|breakdown|summary) (?:\d+ )?(?:days?|weeks?|months?|years?) ago',
                r'how busy (?:was i|am i|will i be) (?:\d+ )?(?:days?|weeks?|months?|years?) (?:ago|from now)',
                # Quarter and period-specific patterns
                r'(?:Q[1-4]|quarter [1-4]) (?:time|work|productivity) (?:summary|breakdown|analysis)',
                r'(?:monthly|weekly|yearly) (?:time|work|productivity) (?:summary|breakdown|analysis)',
                r'how much (?:time|work) (?:in |during )?(?:january|february|march|april|may|june|july|august|september|october|november|december)',
                # Flexible time period queries
                r'time (?:summary|breakdown|analysis) (?:for |from )?(?:last|past|this|current|next|upcoming) (?:week|month|quarter|year)',
                r'work (?:summary|breakdown|analysis) (?:for |from )?(?:last|past|this|current|next|upcoming) (?:week|month|quarter|year)'
            ],
            'help_request': [
                r'help',
                r'what can you do',
                r'(?:show|list) (?:commands|options|features)',
                r'how (?:do i|can i) (.+)',
                r'(?:instructions|guide|tutorial)',
                r'help me with (?:the )?(.+)',
                r'(?:chatbot|adbot) (?:help|features|instructions)',
                r'what (?:can|do) you (?:do|help with)'
            ]
        }

        # Response templates
        self.response_templates = {
            'greeting': [
                "Hi! I'm AdBot, your AI task assistant. I can help you create tasks, manage your schedule, and optimize your productivity. What would you like to do?",
                "Hello! I'm AdBot, here to help with your task management. Try asking me to create tasks, show your schedule, or provide suggestions.",
                "Hey there! I'm AdBot. Ready to tackle your tasks? I can help you create, organize, and prioritize your work."
            ],
            'task_created': [
                "✅ Created task: '{title}' with {duration} min duration",
                "✅ Task added: '{title}' ({classification})",
                "✅ New task ready: '{title}'"
            ],
            'multiple_tasks_created': [
                "✅ Created {count} tasks for '{project}'",
                "✅ Generated {count} tasks to help with '{project}'",
                "✅ Added {count} new tasks for your project"
            ],
            'no_tasks': [
                "You have no tasks for that time period. Would you like me to suggest some based on your patterns?",
                "No tasks found. How about we create some? Just tell me what you need to work on.",
                "Your schedule is clear! Perfect time to plan ahead. Want me to help you add some tasks?"
            ],
            'clarification': [
                "Could you provide more details about what you'd like to do?",
                "I need a bit more information to help you with that.",
                "Can you be more specific about what you need?"
            ],
            'error': [
                "I encountered an issue while processing that. Could you try rephrasing?",
                "Something went wrong. Please try again with a different approach.",
                "I'm having trouble with that request. Can you try asking differently?"
            ]
        }

    def process_message(self, user_message: str) -> ChatMessage:
        """Process user message and generate appropriate response"""
        user_message = user_message.strip().lower()

        # Track analytics
        self.analytics_engine.track_event('chatbot_interaction', {
            'message_length': len(user_message),
            'timestamp': datetime.now().isoformat()
        })

        # Detect intent and extract information
        intent, extracted_info = self._understand_intent(user_message)

        # Generate response based on intent
        response = self._generate_response(intent, extracted_info, user_message)

        # Update conversation context
        self._update_context(intent, extracted_info, user_message, response)

        return response

    def _understand_intent(self, message: str) -> Tuple[str, Dict]:
        """Enhanced intelligent intent understanding with natural language processing"""
        original_message = message
        message = message.strip().lower()

        # Check for domain blacklist keywords first (domain guard-rails)
        if any(keyword in message for keyword in self.domain_blacklist):
            return 'out_of_scope', {
                'original_message': original_message,
                'cleaned_message': message,
                'blacklist_keywords': [k for k in self.domain_blacklist if k in message]
            }

        # Extract all potential information first
        extracted_info = {
            'original_message': original_message,
            'cleaned_message': message,
            'contains_date': False,
            'contains_task_keywords': False,
            'contains_status_keywords': False,
            'contains_classification_keywords': False,
            'date_entities': [],
            'task_action': None,
            'is_follow_up': False,
            'context_reference': None,
            'groups': []  # For regex groups
        }

        # Check for follow-up questions first (using conversation context)
        follow_up_intent, follow_up_info = self._detect_follow_up_intent(message, extracted_info)
        if follow_up_intent:
            return follow_up_intent, follow_up_info

        # Check for greetings (using word boundaries to avoid false positives)
        import re
        greeting_patterns = ['hi', 'hello', 'hey', 'good morning', 'good afternoon', 'good evening']
        if any(re.search(r'\b' + re.escape(greeting) + r'\b', message, re.IGNORECASE) for greeting in greeting_patterns):
            return 'greeting', extracted_info

        # Enhanced task creation detection with regex patterns
        task_creation_groups = self._extract_task_creation_info(message)
        if task_creation_groups:
            extracted_info['groups'] = task_creation_groups
            # Check if it's multiple tasks
            if any(char.isdigit() for char in message) and ('generate' in message or 'create' in message):
                # Try to extract number
                import re
                number_match = re.search(r'(\d+)', message)
                if number_match:
                    extracted_info['groups'] = [number_match.group(1), task_creation_groups[0]]
                    return 'create_multiple_tasks', extracted_info
            elif 'break down' in message:
                return 'break_down_task', extracted_info
            else:
                return 'create_task', extracted_info

        # Enhanced date detection with multiple strategies
        date_info = self._detect_date_references(message)
        if date_info['found']:
            extracted_info.update(date_info)
            extracted_info['contains_date'] = True

        # Detect task-related actions
        task_action = self._detect_task_action(message)
        if task_action:
            extracted_info['task_action'] = task_action

        # Detect status keywords
        status_keywords = self._detect_status_keywords(message)
        if status_keywords:
            extracted_info['contains_status_keywords'] = True
            extracted_info['status_keywords'] = status_keywords

        # Detect classification keywords
        classification_keywords = self._detect_classification_keywords(message)
        if classification_keywords:
            extracted_info['contains_classification_keywords'] = True
            extracted_info['classification_keywords'] = classification_keywords

        # Detect general task keywords (expanded)
        task_related_keywords = [
            'task', 'work', 'project', 'job', 'assignment', 'todo', 'activity',
            'scheduled', 'appointment', 'meeting', 'doing', 'planned', 'anything'
        ]
        if any(word in message for word in task_related_keywords):
            extracted_info['contains_task_keywords'] = True

        # Smart intent determination based on context and keywords
        intent = self._determine_smart_intent(message, extracted_info)

        return intent, extracted_info

    def _extract_task_creation_info(self, message: str) -> List[str]:
        """Extract task description from creation requests"""
        import re

        # Patterns for task creation with proper capture groups
        creation_patterns = [
            r'create (?:a )?task (?:for |to )?(.+)',
            r'add (?:a )?task (?:for |to )?(.+)',
            r'make (?:a )?task (?:for |to )?(.+)',
            r'new task (?:for |to )?(.+)',
            r'generate (?:\d+ )?tasks? (?:for |to )?(.+)',
            r'break down \'?([^\']+)\'? into (?:steps|subtasks)',
            r'break down (.+)',
        ]

        for pattern in creation_patterns:
            match = re.search(pattern, message, re.IGNORECASE)
            if match:
                return [match.group(1).strip()]

        return []

    def _detect_date_references(self, message: str) -> Dict:
        """Enhanced date detection with multiple strategies including quarters, months, years, and natural language temporal expressions"""
        date_info = {
            'found': False,
            'date_entities': [],
            'relative_dates': [],
            'specific_dates': [],
            'date_keywords': [],
            'quarters': [],
            'months': [],
            'years': [],
            'weeks': [],
            'temporal_expressions': [],  # New: for "2 months ago", "3 weeks ago", etc.
            'temporal_ranges': []  # New: for "last 3 months", "past 2 weeks", etc.
        }

        import re

        # Enhanced temporal expression patterns with natural language support
        temporal_expression_patterns = [
            # Weeks ago/from now patterns
            r'(\d+)\s+weeks?\s+ago',
            r'(\d+)\s+weeks?\s+back',
            r'(\d+)\s+weeks?\s+in\s+the\s+past',
            r'(\d+)\s+weeks?\s+from\s+now',
            r'(\d+)\s+weeks?\s+ahead',
            r'(\d+)\s+weeks?\s+in\s+the\s+future',
            # Months ago/from now patterns
            r'(\d+)\s+months?\s+ago',
            r'(\d+)\s+months?\s+back',
            r'(\d+)\s+months?\s+in\s+the\s+past',
            r'(\d+)\s+months?\s+from\s+now',
            r'(\d+)\s+months?\s+ahead',
            r'(\d+)\s+months?\s+in\s+the\s+future',
            # Years ago/from now patterns
            r'(\d+)\s+years?\s+ago',
            r'(\d+)\s+years?\s+back',
            r'(\d+)\s+years?\s+in\s+the\s+past',
            r'(\d+)\s+years?\s+from\s+now',
            r'(\d+)\s+years?\s+ahead',
            r'(\d+)\s+years?\s+in\s+the\s+future',
            # Days ago/from now patterns
            r'(\d+)\s+days?\s+ago',
            r'(\d+)\s+days?\s+back',
            r'(\d+)\s+days?\s+in\s+the\s+past',
            r'(\d+)\s+days?\s+from\s+now',
            r'(\d+)\s+days?\s+ahead',
            r'(\d+)\s+days?\s+in\s+the\s+future',
        ]

        for pattern in temporal_expression_patterns:
            matches = re.finditer(pattern, message, re.IGNORECASE)
            for match in matches:
                date_info['found'] = True
                number = int(match.group(1))
                full_match = match.group(0)

                # Determine direction (past or future)
                direction = 'past'
                if any(word in full_match.lower() for word in ['from now', 'ahead', 'future']):
                    direction = 'future'

                # Determine unit
                unit = 'days'
                if 'week' in full_match.lower():
                    unit = 'weeks'
                elif 'month' in full_match.lower():
                    unit = 'months'
                elif 'year' in full_match.lower():
                    unit = 'years'

                date_info['temporal_expressions'].append({
                    'number': number,
                    'unit': unit,
                    'direction': direction,
                    'text': full_match,
                    'type': 'relative_offset'
                })
                date_info['date_keywords'].append(full_match)

        # Enhanced temporal range patterns (last N periods, past N periods, next N periods)
        temporal_range_patterns = [
            # Last/past N periods
            r'(?:last|past|previous)\s+(\d+)\s+(weeks?|months?|years?|days?)',
            r'(?:the\s+)?(?:last|past|previous)\s+(\d+)\s+(weeks?|months?|years?|days?)',
            # Next/upcoming N periods
            r'(?:next|upcoming|following)\s+(\d+)\s+(weeks?|months?|years?|days?)',
            r'(?:the\s+)?(?:next|upcoming|following)\s+(\d+)\s+(weeks?|months?|years?|days?)',
            # In the last/past N periods
            r'(?:in\s+)?(?:the\s+)?(?:last|past|previous)\s+(\d+)\s+(weeks?|months?|years?|days?)',
            # Over the last/past N periods
            r'(?:over\s+)?(?:the\s+)?(?:last|past|previous)\s+(\d+)\s+(weeks?|months?|years?|days?)',
            # During the last/past N periods
            r'(?:during\s+)?(?:the\s+)?(?:last|past|previous)\s+(\d+)\s+(weeks?|months?|years?|days?)',
        ]

        for pattern in temporal_range_patterns:
            matches = re.finditer(pattern, message, re.IGNORECASE)
            for match in matches:
                date_info['found'] = True
                number = int(match.group(1))
                unit = match.group(2).lower().rstrip('s')  # Remove plural 's'
                full_match = match.group(0)

                # Determine direction based on keywords
                direction = 'past'
                if any(word in full_match.lower() for word in ['next', 'upcoming', 'following']):
                    direction = 'future'

                date_info['temporal_ranges'].append({
                    'number': number,
                    'unit': unit,
                    'direction': direction,
                    'text': full_match,
                    'type': 'relative_range'
                })
                date_info['date_keywords'].append(full_match)

        # Enhanced natural language time expressions
        natural_time_expressions = {
            # Flexible past expressions
            'last_week': [
                'last week', 'previous week', 'past week', 'the week before',
                'last weeks', 'previous weeks', 'past weeks'
            ],
            'last_month': [
                'last month', 'previous month', 'past month', 'the month before',
                'last months', 'previous months', 'past months'
            ],
            'last_year': [
                'last year', 'previous year', 'past year', 'the year before',
                'last years', 'previous years', 'past years'
            ],
            # Flexible current expressions
            'this_week': [
                'this week', 'current week', 'the current week', 'this weeks'
            ],
            'this_month': [
                'this month', 'current month', 'the current month', 'this months'
            ],
            'this_year': [
                'this year', 'current year', 'the current year', 'this years'
            ],
            # Flexible future expressions
            'next_week': [
                'next week', 'following week', 'upcoming week', 'the week ahead',
                'next weeks', 'following weeks', 'upcoming weeks'
            ],
            'next_month': [
                'next month', 'following month', 'upcoming month', 'the month ahead',
                'next months', 'following months', 'upcoming months'
            ],
            'next_year': [
                'next year', 'following year', 'upcoming year', 'the year ahead',
                'next years', 'following years', 'upcoming years'
            ],
            # Additional flexible expressions
            'yesterday': [
                'yesterday', 'the day before', 'the previous day', 'a day ago'
            ],
            'today': [
                'today', 'this day', 'right now', 'currently', 'at present'
            ],
            'tomorrow': [
                'tomorrow', 'the next day', 'the following day', 'a day from now'
            ],
        }

        for time_key, patterns in natural_time_expressions.items():
            for pattern in patterns:
                if pattern in message.lower():
                    date_info['found'] = True
                    if time_key.endswith('_week'):
                        if time_key not in date_info['weeks']:
                            date_info['weeks'].append(time_key)
                    elif time_key.endswith('_month'):
                        if time_key not in date_info['months']:
                            date_info['months'].append(time_key)
                    elif time_key.endswith('_year'):
                        if time_key not in date_info['years']:
                            date_info['years'].append(time_key)
                    else:
                        if time_key not in date_info['relative_dates']:
                            date_info['relative_dates'].append(time_key)

                    date_info['date_keywords'].append(pattern)
                    break  # Use first match for each time_key

        # Quarter patterns (enhanced)
        quarter_patterns = {
            'Q1': ['q1', 'quarter 1', 'first quarter', '1st quarter', 'q 1'],
            'Q2': ['q2', 'quarter 2', 'second quarter', '2nd quarter', 'q 2'],
            'Q3': ['q3', 'quarter 3', 'third quarter', '3rd quarter', 'q 3'],
            'Q4': ['q4', 'quarter 4', 'fourth quarter', '4th quarter', 'q 4']
        }

        for quarter, patterns in quarter_patterns.items():
            if any(pattern in message.lower() for pattern in patterns):
                date_info['found'] = True
                if quarter not in date_info['quarters']:
                    date_info['quarters'].append(quarter)
                date_info['date_keywords'].extend([p for p in patterns if p in message.lower()])

        # Enhanced week number patterns
        week_patterns = [
            r'week (\d{1,2})',
            r'week number (\d{1,2})',
            r'calendar week (\d{1,2})',
            r'week #(\d{1,2})',
            r'w(\d{1,2})',
            r'wk (\d{1,2})',
            r'(\d{1,2})(?:st|nd|rd|th)?\s+week',
        ]

        for pattern in week_patterns:
            matches = re.findall(pattern, message.lower())
            if matches:
                date_info['found'] = True
                for match in matches:
                    week_num = str(match) if isinstance(match, str) else str(match)
                    if week_num not in date_info['weeks']:
                        date_info['weeks'].append(week_num)
                    date_info['date_keywords'].append(f"week {week_num}")

        # Month names and dates - enhanced patterns
        months = [
            'january', 'february', 'march', 'april', 'may', 'june',
            'july', 'august', 'september', 'october', 'november', 'december',
            'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'
        ]

        for month in months:
            if month in message.lower():
                date_info['found'] = True
                if month not in date_info['specific_dates']:
                    date_info['specific_dates'].append(month)
                date_info['date_keywords'].append(month)

        # Enhanced date-related context keywords
        date_context_words = [
            'scheduled', 'appointment', 'meeting', 'deadline', 'due', 'planned',
            'when', 'during', 'between', 'from', 'to', 'until', 'before', 'after',
            'since', 'time', 'date', 'period', 'span', 'range'
        ]

        for word in date_context_words:
            if word in message.lower():
                date_info['date_keywords'].append(word)

        # Enhanced numeric date patterns
        date_patterns = [
            r'\d{1,2}/\d{1,2}/\d{2,4}',      # 7/10/2025
            r'\d{1,2}/\d{1,2}',              # 7/10
            r'\d{4}-\d{1,2}-\d{1,2}',        # 2025-07-10
            r'\d{1,2}(?:st|nd|rd|th)?',      # 10th, 15th
            r'\d{1,2}\.\d{1,2}\.\d{2,4}',    # 7.10.2025 (European format)
            r'\d{1,2}-\d{1,2}-\d{2,4}',      # 7-10-2025
        ]

        for pattern in date_patterns:
            matches = re.findall(pattern, message)
            if matches:
                date_info['found'] = True
                for match in matches:
                    if match not in date_info['specific_dates']:
                        date_info['specific_dates'].append(match)

        return date_info

    def _calculate_quarter_dates(self, quarter: str, year: Optional[int] = None) -> Tuple[date, date]:
        """
        Calculate start and end dates for a quarter with comprehensive edge case handling

        Args:
            quarter: Quarter identifier ('Q1', 'Q2', 'Q3', 'Q4')
            year: Optional year (defaults to current year)

        Returns:
            Tuple of (start_date, end_date) for the quarter

        Raises:
            ValueError: For invalid quarter strings or years
        """
        # Validate quarter input
        if quarter is None:
            raise ValueError("Quarter cannot be None")
        if not isinstance(quarter, str):
            raise ValueError(f"Quarter must be a string, got {type(quarter)}")

        quarter = quarter.upper().strip()
        if not quarter:  # Empty string after stripping
            raise ValueError("Quarter cannot be empty")

        valid_quarters = {'Q1', 'Q2', 'Q3', 'Q4'}
        if quarter not in valid_quarters:
            # Try to parse alternative quarter formats
            quarter_mapping = {
                'QUARTER 1': 'Q1', 'QUARTER1': 'Q1', 'FIRST QUARTER': 'Q1',
                'QUARTER 2': 'Q2', 'QUARTER2': 'Q2', 'SECOND QUARTER': 'Q2',
                'QUARTER 3': 'Q3', 'QUARTER3': 'Q3', 'THIRD QUARTER': 'Q3',
                'QUARTER 4': 'Q4', 'QUARTER4': 'Q4', 'FOURTH QUARTER': 'Q4',
                '1': 'Q1', '2': 'Q2', '3': 'Q3', '4': 'Q4'
            }
            mapped_quarter = quarter_mapping.get(quarter)
            if not mapped_quarter:
                raise ValueError(f"Invalid quarter format. Use Q1, Q2, Q3, or Q4")
            quarter = mapped_quarter

        # Validate and set year
        if year is None:
            year = date.today().year
        elif not isinstance(year, int):
            try:
                year = int(year)
            except (ValueError, TypeError):
                raise ValueError(f"Invalid year format: {year}")

        # Validate year range (reasonable business range)
        current_year = date.today().year
        min_year = current_year - 100
        max_year = current_year + 50
        if not (min_year <= year <= max_year):
            raise ValueError(f"Year {year} is outside valid range ({min_year}-{max_year})")

        # Quarter to month mapping
        quarter_months = {
            'Q1': (1, 3),   # Jan-Mar
            'Q2': (4, 6),   # Apr-Jun
            'Q3': (7, 9),   # Jul-Sep
            'Q4': (10, 12)  # Oct-Dec
        }

        try:
            start_month, end_month = quarter_months[quarter]

            # Create start date (first day of start month)
            start_date = date(year, start_month, 1)

            # Calculate last day of end month (handles leap years and varying month lengths)
            last_day = calendar.monthrange(year, end_month)[1]
            end_date = date(year, end_month, last_day)

            # Validate that start_date <= end_date (sanity check)
            if start_date > end_date:
                raise ValueError(f"Invalid date range: start {start_date} > end {end_date}")

            return start_date, end_date

        except ValueError as e:
            if "day is out of range for month" in str(e):
                raise ValueError(f"Invalid date calculation for {quarter} {year}: {e}")
            raise
        except Exception as e:
            raise ValueError(f"Unexpected error calculating quarter dates: {e}")

    def _calculate_month_dates(self, month_period: str) -> Tuple[date, date]:
        """
        Calculate start and end dates for month periods with comprehensive edge case handling

        Args:
            month_period: Month period identifier ('this_month', 'last_month', 'next_month')

        Returns:
            Tuple of (start_date, end_date) for the month period

        Raises:
            ValueError: For invalid month period strings or calculation errors
        """
        # Validate and normalize month_period input
        month_period = month_period.lower().strip().replace(' ', '_')
        valid_periods = {'this_month', 'last_month', 'next_month', 'current_month', 'previous_month', 'following_month'}

        # Handle alternative naming conventions
        period_mapping = {
            'current_month': 'this_month',
            'previous_month': 'last_month',
            'past_month': 'last_month',
            'following_month': 'next_month',
            'upcoming_month': 'next_month'
        }

        if month_period in period_mapping:
            month_period = period_mapping[month_period]
        elif month_period not in valid_periods:
            raise ValueError(f"Invalid month period: {month_period}. Use 'this_month', 'last_month', or 'next_month'")

        today = date.today()

        try:
            if month_period == 'this_month':
                start_date = date(today.year, today.month, 1)
                last_day = calendar.monthrange(today.year, today.month)[1]
                end_date = date(today.year, today.month, last_day)

            elif month_period == 'last_month':
                # Handle year boundary transition (January -> December of previous year)
                if today.month == 1:
                    target_month = 12
                    target_year = today.year - 1
                else:
                    target_month = today.month - 1
                    target_year = today.year

                # Validate the target year is reasonable
                if target_year < 1900:
                    raise ValueError(f"Target year {target_year} is too far in the past")

                start_date = date(target_year, target_month, 1)
                last_day = calendar.monthrange(target_year, target_month)[1]
                end_date = date(target_year, target_month, last_day)

            elif month_period == 'next_month':
                # Handle year boundary transition (December -> January of next year)
                if today.month == 12:
                    target_month = 1
                    target_year = today.year + 1
                else:
                    target_month = today.month + 1
                    target_year = today.year

                # Validate the target year is reasonable
                current_year = date.today().year
                if target_year > current_year + 50:
                    raise ValueError(f"Target year {target_year} is too far in the future")

                start_date = date(target_year, target_month, 1)
                last_day = calendar.monthrange(target_year, target_month)[1]
                end_date = date(target_year, target_month, last_day)

            else:
                # This should never happen due to validation above, but provide safe fallback
                raise ValueError(f"Unhandled month period: {month_period}")

            # Validate that start_date <= end_date (sanity check)
            if start_date > end_date:
                raise ValueError(f"Invalid date range: start {start_date} > end {end_date}")

            # Additional validation: ensure dates are in valid calendar range
            min_date = date(1900, 1, 1)
            max_date = date(9999, 12, 31)
            if not (min_date <= start_date <= max_date and min_date <= end_date <= max_date):
                raise ValueError(f"Calculated dates are outside valid range: {start_date} to {end_date}")

            return start_date, end_date

        except ValueError as e:
            if "day is out of range for month" in str(e) or "month must be in" in str(e):
                raise ValueError(f"Invalid date calculation for {month_period}: {e}")
            raise
        except Exception as e:
            raise ValueError(f"Unexpected error calculating month dates: {e}")

    def _calculate_year_dates(self, year_period: str) -> Tuple[date, date]:
        """
        Calculate start and end dates for year periods with comprehensive edge case handling

        Args:
            year_period: Year period identifier ('this_year', 'last_year', 'next_year')

        Returns:
            Tuple of (start_date, end_date) for the year period

        Raises:
            ValueError: For invalid year period strings or calculation errors
        """
        # Validate and normalize year_period input
        year_period = year_period.lower().strip().replace(' ', '_')
        valid_periods = {'this_year', 'last_year', 'next_year', 'current_year', 'previous_year', 'following_year'}

        # Handle alternative naming conventions
        period_mapping = {
            'current_year': 'this_year',
            'previous_year': 'last_year',
            'past_year': 'last_year',
            'following_year': 'next_year',
            'upcoming_year': 'next_year'
        }

        if year_period in period_mapping:
            year_period = period_mapping[year_period]
        elif year_period not in valid_periods:
            raise ValueError(f"Invalid year period: {year_period}. Use 'this_year', 'last_year', or 'next_year'")

        today = date.today()
        current_year = today.year

        try:
            if year_period == 'this_year':
                target_year = current_year

            elif year_period == 'last_year':
                target_year = current_year - 1
                # Validate reasonable range
                if target_year < 1900:
                    raise ValueError(f"Target year {target_year} is too far in the past")

            elif year_period == 'next_year':
                target_year = current_year + 1
                # Validate reasonable range
                if target_year > current_year + 50:
                    raise ValueError(f"Target year {target_year} is too far in the future")

            else:
                # This should never happen due to validation above
                raise ValueError(f"Unhandled year period: {year_period}")

            # Create start and end dates for the year
            start_date = date(target_year, 1, 1)
            end_date = date(target_year, 12, 31)

            # Validate that start_date <= end_date (sanity check)
            if start_date > end_date:
                raise ValueError(f"Invalid date range: start {start_date} > end {end_date}")

            # Additional validation: ensure dates are in valid calendar range
            min_date = date(1900, 1, 1)
            max_date = date(9999, 12, 31)
            if not (min_date <= start_date <= max_date and min_date <= end_date <= max_date):
                raise ValueError(f"Calculated dates are outside valid range: {start_date} to {end_date}")

            # Leap year validation - ensure February 29 exists if needed
            is_leap_year = calendar.isleap(target_year)
            leap_info = f" (leap year)" if is_leap_year else " (non-leap year)"

            return start_date, end_date

        except ValueError as e:
            if "day is out of range for month" in str(e):
                raise ValueError(f"Invalid date calculation for {year_period}: {e}")
            raise
        except Exception as e:
            raise ValueError(f"Unexpected error calculating year dates: {e}")

    def _calculate_week_dates(self, week_number: str, year: Optional[int] = None) -> Tuple[date, date]:
        """
        Calculate start and end dates for week numbers with comprehensive edge case handling
        Uses ISO 8601 week date system for accurate week calculations

        Args:
            week_number: Week number as string (1-53)
            year: Optional year (defaults to current year)

        Returns:
            Tuple of (start_date, end_date) for the week (Monday to Sunday)

        Raises:
            ValueError: For invalid week numbers, years, or calculation errors
        """
        # Validate and parse week_number
        try:
            week_num = int(week_number.strip())
        except (ValueError, AttributeError):
            raise ValueError(f"Invalid week number format: {week_number}. Must be an integer.")

        # Validate week number range (ISO 8601 allows weeks 1-53)
        if not (1 <= week_num <= 53):
            raise ValueError(f"Week number {week_num} is out of range. Must be between 1 and 53.")

        # Validate and set year
        if year is None:
            year = date.today().year
        elif not isinstance(year, int):
            try:
                year = int(year)
            except (ValueError, TypeError):
                raise ValueError(f"Invalid year format: {year}")

        # Validate year range (reasonable business range)
        current_year = date.today().year
        min_year = current_year - 100
        max_year = current_year + 50
        if not (min_year <= year <= max_year):
            raise ValueError(f"Year {year} is outside valid range ({min_year}-{max_year})")

        try:
            # Use ISO 8601 week date system for accurate calculations
            # ISO week 1 is the first week with at least 4 days in the new year

            # Find the first Monday of the ISO year
            jan_1 = date(year, 1, 1)
            jan_4 = date(year, 1, 4)  # Week 1 always contains January 4th

            # Find the Monday of the week containing January 4th
            # This is the start of ISO week 1
            days_since_monday = jan_4.weekday()  # 0=Monday, 6=Sunday
            first_monday = jan_4 - timedelta(days=days_since_monday)

            # Calculate the start of the requested week
            week_start = first_monday + timedelta(weeks=week_num - 1)
            week_end = week_start + timedelta(days=6)  # Sunday

            # Validate that the calculated week actually belongs to the specified year
            # For edge cases where week 1 might start in the previous year or
            # week 52/53 might extend into the next year

            # Get the year that this week primarily belongs to
            week_thursday = week_start + timedelta(days=3)  # Thursday determines the year
            week_belongs_to_year = week_thursday.year

            if week_belongs_to_year != year:
                # Handle edge cases where week boundaries cross year boundaries
                if week_num == 1 and week_belongs_to_year == year + 1:
                    # Week 1 of next year starts in December of current year
                    raise ValueError(f"Week 1 of {year} doesn't exist. Week starts in {year + 1}.")
                elif week_num >= 52 and week_belongs_to_year == year - 1:
                    # High week number that actually belongs to previous year
                    raise ValueError(f"Week {week_num} of {year} doesn't exist. Week belongs to {year - 1}.")

            # Additional validation: check if the requested week exists in the year
            # Some years have 52 weeks, others have 53
            total_weeks_in_year = self._get_weeks_in_year(year)
            if week_num > total_weeks_in_year:
                raise ValueError(f"Week {week_num} doesn't exist in {year}. Year has only {total_weeks_in_year} weeks.")

            # Final validation: ensure dates are reasonable
            min_date = date(1900, 1, 1)
            max_date = date(9999, 12, 31)
            if not (min_date <= week_start <= max_date and min_date <= week_end <= max_date):
                raise ValueError(f"Calculated week dates are outside valid range: {week_start} to {week_end}")

            # Sanity check: start should be before or equal to end
            if week_start > week_end:
                raise ValueError(f"Invalid week range: start {week_start} > end {week_end}")

            return week_start, week_end

        except ValueError as e:
            if "day is out of range for month" in str(e):
                raise ValueError(f"Invalid date calculation for week {week_num} of {year}: {e}")
            raise
        except Exception as e:
            raise ValueError(f"Unexpected error calculating week dates: {e}")

    def _get_weeks_in_year(self, year: int) -> int:
        """
        Calculate the number of ISO weeks in a given year

        Args:
            year: The year to check

        Returns:
            Number of weeks in the year (52 or 53)
        """
        # December 28th is always in the last week of the year
        dec_28 = date(year, 12, 28)
        # Get the ISO week number for December 28th
        _, week_num, _ = dec_28.isocalendar()
        return week_num

    def _validate_week_year_combination(self, week_num: int, year: int) -> bool:
        """
        Validate that a week number exists in the given year

        Args:
            week_num: Week number to validate
            year: Year to check against

        Returns:
            True if valid combination, False otherwise
        """
        try:
            total_weeks = self._get_weeks_in_year(year)
            return 1 <= week_num <= total_weeks
        except:
            return False

    def _validate_date_boundaries(self, start_date: date, end_date: date, context: str = "date range") -> bool:
        """
        Comprehensive validation for date boundaries and edge cases

        Args:
            start_date: Start date to validate
            end_date: End date to validate
            context: Context string for error messages

        Returns:
            True if dates are valid, raises ValueError if not

        Raises:
            ValueError: For invalid date combinations or boundary conditions
        """
        # Basic range validation
        if start_date > end_date:
            raise ValueError(f"Invalid {context}: start date {start_date} is after end date {end_date}")

        # Reasonable date range validation
        min_date = date(1900, 1, 1)
        max_date = date(2100, 12, 31)

        if start_date < min_date:
            raise ValueError(f"Start date {start_date} is before minimum allowed date {min_date}")
        if end_date > max_date:
            raise ValueError(f"End date {end_date} is after maximum allowed date {max_date}")

        # Check for extremely long ranges that might indicate calculation errors
        max_range_days = 366 * 2  # 2 years maximum
        range_days = (end_date - start_date).days
        if range_days > max_range_days:
            raise ValueError(f"Date range too large: {range_days} days. Maximum allowed: {max_range_days} days")

        return True

    def _handle_leap_year_edge_cases(self, target_date: date, operation: str = "calculation") -> date:
        """
        Handle leap year edge cases, particularly around February 29th

        Args:
            target_date: Date to validate and potentially adjust
            operation: Description of the operation for error messages

        Returns:
            Validated date, potentially adjusted for leap year issues

        Raises:
            ValueError: For irreconcilable leap year issues
        """
        try:
            # Try to create the date to ensure it's valid
            validated_date = date(target_date.year, target_date.month, target_date.day)
            return validated_date
        except ValueError as e:
            if "day is out of range for month" in str(e) and target_date.month == 2 and target_date.day == 29:
                # February 29th in a non-leap year
                if not calendar.isleap(target_date.year):
                    # Adjust to February 28th
                    adjusted_date = date(target_date.year, 2, 28)
                    # Note: In a production system, you might want to log this adjustment
                    return adjusted_date
            raise ValueError(f"Invalid date in {operation}: {e}")

    def _normalize_temporal_input(self, temporal_input: str) -> str:
        """
        Normalize temporal input strings to handle various formats and edge cases

        Args:
            temporal_input: Raw temporal input string

        Returns:
            Normalized temporal string
        """
        if not temporal_input or not isinstance(temporal_input, str):
            raise ValueError("Temporal input must be a non-empty string")

        # Basic normalization
        normalized = temporal_input.strip().lower()

        # Check if empty after stripping
        if not normalized:
            raise ValueError("Temporal input cannot be empty or whitespace-only")

        # Remove extra whitespace
        normalized = ' '.join(normalized.split())

        # Handle common variations
        replacements = {
            'current': 'this',
            'present': 'this',
            'previous': 'last',
            'past': 'last',
            'prior': 'last',
            'following': 'next',
            'upcoming': 'next',
            'future': 'next'
        }

        for old, new in replacements.items():
            normalized = normalized.replace(old, new)

        return normalized

    def _get_quarter_from_date(self, target_date: date) -> str:
        """
        Determine which quarter a given date falls into

        Args:
            target_date: Date to analyze

        Returns:
            Quarter string ('Q1', 'Q2', 'Q3', 'Q4')
        """
        month = target_date.month
        if 1 <= month <= 3:
            return 'Q1'
        elif 4 <= month <= 6:
            return 'Q2'
        elif 7 <= month <= 9:
            return 'Q3'
        elif 10 <= month <= 12:
            return 'Q4'
        else:
            raise ValueError(f"Invalid month {month} in date {target_date}")

    def _get_month_boundaries(self, year: int, month: int) -> Tuple[date, date]:
        """
        Get the first and last day of a specific month, handling leap years

        Args:
            year: Target year
            month: Target month (1-12)

        Returns:
            Tuple of (first_day, last_day) of the month

        Raises:
            ValueError: For invalid year/month combinations
        """
        if not (1 <= month <= 12):
            raise ValueError(f"Month {month} is out of range. Must be 1-12.")

        if not (1900 <= year <= 2100):
            raise ValueError(f"Year {year} is out of reasonable range (1900-2100).")

        try:
            first_day = date(year, month, 1)
            last_day_of_month = calendar.monthrange(year, month)[1]
            last_day = date(year, month, last_day_of_month)

            return first_day, last_day
        except ValueError as e:
            raise ValueError(f"Error calculating month boundaries for {year}-{month:02d}: {e}")

    def _handle_year_boundary_transitions(self, base_date: date, months_offset: int) -> Tuple[int, int]:
        """
        Handle year boundary transitions when adding/subtracting months

        Args:
            base_date: Starting date
            months_offset: Number of months to add (positive) or subtract (negative)

        Returns:
            Tuple of (target_year, target_month)
        """
        target_month = base_date.month + months_offset
        target_year = base_date.year

        # Handle month overflow/underflow
        while target_month > 12:
            target_month -= 12
            target_year += 1

        while target_month < 1:
            target_month += 12
            target_year -= 1

        return target_year, target_month

    def _calculate_temporal_expression_dates(self, temporal_expr: Dict, reference_date: Optional[date] = None) -> Tuple[date, date]:
        """
        Calculate dates for temporal expressions like "2 months ago", "3 weeks from now"

        Args:
            temporal_expr: Dictionary with 'number', 'unit', 'direction', 'type'
            reference_date: Optional reference date (defaults to today)

        Returns:
            Tuple of (start_date, end_date) for the expression
        """
        if reference_date is None:
            reference_date = date.today()

        number = temporal_expr.get('number', 1)
        unit = temporal_expr.get('unit', 'days')
        direction = temporal_expr.get('direction', 'past')

        # Calculate the offset
        if unit == 'days':
            offset_days = number
        elif unit == 'weeks':
            offset_days = number * 7
        elif unit == 'months':
            # Approximate months as 30 days, then adjust to actual month boundaries
            return self._calculate_month_offset_dates(reference_date, number, direction)
        elif unit == 'years':
            # Calculate year offset
            return self._calculate_year_offset_dates(reference_date, number, direction)
        else:
            raise ValueError(f"Unsupported temporal unit: {unit}")

        # Calculate target date
        if direction == 'past':
            target_date = reference_date - timedelta(days=offset_days)
        else:  # future
            target_date = reference_date + timedelta(days=offset_days)

        # For single-day expressions, start and end are the same
        return target_date, target_date

    def _calculate_temporal_range_dates(self, temporal_range: Dict, reference_date: Optional[date] = None) -> Tuple[date, date]:
        """
        Calculate dates for temporal ranges like "last 3 months", "past 2 weeks"

        Args:
            temporal_range: Dictionary with 'number', 'unit', 'direction', 'type'
            reference_date: Optional reference date (defaults to today)

        Returns:
            Tuple of (start_date, end_date) for the range
        """
        if reference_date is None:
            reference_date = date.today()

        number = temporal_range.get('number', 1)
        unit = temporal_range.get('unit', 'days')
        direction = temporal_range.get('direction', 'past')

        if direction == 'past':
            # For "last N periods", we go backwards from today
            if unit == 'days':
                start_date = reference_date - timedelta(days=number)
                end_date = reference_date
            elif unit == 'weeks':
                start_date = reference_date - timedelta(weeks=number)
                end_date = reference_date
            elif unit == 'months':
                # Calculate proper month boundaries
                start_date, _ = self._calculate_month_offset_dates(reference_date, number, 'past')
                end_date = reference_date
            elif unit == 'years':
                # Calculate proper year boundaries
                start_date, _ = self._calculate_year_offset_dates(reference_date, number, 'past')
                end_date = reference_date
            else:
                raise ValueError(f"Unsupported temporal unit: {unit}")
        else:  # future
            # For "next N periods", we go forwards from today
            if unit == 'days':
                start_date = reference_date
                end_date = reference_date + timedelta(days=number)
            elif unit == 'weeks':
                start_date = reference_date
                end_date = reference_date + timedelta(weeks=number)
            elif unit == 'months':
                # Calculate proper month boundaries
                start_date = reference_date
                _, end_date = self._calculate_month_offset_dates(reference_date, number, 'future')
            elif unit == 'years':
                # Calculate proper year boundaries
                start_date = reference_date
                _, end_date = self._calculate_year_offset_dates(reference_date, number, 'future')
            else:
                raise ValueError(f"Unsupported temporal unit: {unit}")

        return start_date, end_date

    def _calculate_month_offset_dates(self, reference_date: date, months_offset: int, direction: str) -> Tuple[date, date]:
        """
        Calculate dates with month offset, handling month boundaries properly

        Args:
            reference_date: Starting date
            months_offset: Number of months to offset
            direction: 'past' or 'future'

        Returns:
            Tuple of (start_date, end_date) for the month offset
        """
        if direction == 'past':
            # Go back N months
            target_year = reference_date.year
            target_month = reference_date.month - months_offset

            # Handle year boundaries
            while target_month <= 0:
                target_month += 12
                target_year -= 1

            # Get the first and last day of the target month
            start_date = date(target_year, target_month, 1)
            last_day = calendar.monthrange(target_year, target_month)[1]
            end_date = date(target_year, target_month, last_day)

        else:  # future
            # Go forward N months
            target_year = reference_date.year
            target_month = reference_date.month + months_offset

            # Handle year boundaries
            while target_month > 12:
                target_month -= 12
                target_year += 1

            # Get the first and last day of the target month
            start_date = date(target_year, target_month, 1)
            last_day = calendar.monthrange(target_year, target_month)[1]
            end_date = date(target_year, target_month, last_day)

        return start_date, end_date

    def _calculate_year_offset_dates(self, reference_date: date, years_offset: int, direction: str) -> Tuple[date, date]:
        """
        Calculate dates with year offset

        Args:
            reference_date: Starting date
            years_offset: Number of years to offset
            direction: 'past' or 'future'

        Returns:
            Tuple of (start_date, end_date) for the year offset
        """
        if direction == 'past':
            target_year = reference_date.year - years_offset
        else:  # future
            target_year = reference_date.year + years_offset

        start_date = date(target_year, 1, 1)
        end_date = date(target_year, 12, 31)

        return start_date, end_date

    def _parse_natural_language_date(self, message: str, date_info: Dict) -> Optional[Tuple[date, date, str]]:
        """
        Parse natural language date expressions into date ranges

        Args:
            message: Original message
            date_info: Date information extracted from message

        Returns:
            Optional tuple of (start_date, end_date, description)
        """
        today = date.today()

        # Handle temporal expressions (e.g., "2 months ago")
        if date_info.get('temporal_expressions'):
            expr = date_info['temporal_expressions'][0]  # Use first expression
            try:
                start_date, end_date = self._calculate_temporal_expression_dates(expr, today)
                description = f"{expr['number']} {expr['unit']} {expr['direction']}"
                return start_date, end_date, description
            except Exception as e:
                # Continue to other parsing methods if this fails
                pass

        # Handle temporal ranges (e.g., "last 3 months")
        if date_info.get('temporal_ranges'):
            range_expr = date_info['temporal_ranges'][0]  # Use first range
            try:
                start_date, end_date = self._calculate_temporal_range_dates(range_expr, today)
                description = f"{range_expr['direction']} {range_expr['number']} {range_expr['unit']}"
                return start_date, end_date, description
            except Exception as e:
                # Continue to other parsing methods if this fails
                pass

        # Handle quarters
        if date_info.get('quarters'):
            quarter = date_info['quarters'][0]
            try:
                start_date, end_date = self._calculate_quarter_dates(quarter)
                description = quarter
                return start_date, end_date, description
            except Exception as e:
                pass

        # Handle month periods
        if date_info.get('months'):
            month_period = date_info['months'][0]
            try:
                start_date, end_date = self._calculate_month_dates(month_period)
                description = month_period.replace('_', ' ')
                return start_date, end_date, description
            except Exception as e:
                pass

        # Handle year periods
        if date_info.get('years'):
            year_period = date_info['years'][0]
            try:
                start_date, end_date = self._calculate_year_dates(year_period)
                description = year_period.replace('_', ' ')
                return start_date, end_date, description
            except Exception as e:
                pass

        # Handle week numbers
        if date_info.get('weeks'):
            week_ref = date_info['weeks'][0]
            try:
                if week_ref.isdigit():
                    # Specific week number
                    start_date, end_date = self._calculate_week_dates(week_ref)
                    description = f"week {week_ref}"
                    return start_date, end_date, description
                elif week_ref in ['this_week', 'last_week', 'next_week']:
                    # Relative week references
                    if week_ref == 'this_week':
                        start_week = today - timedelta(days=today.weekday())
                        end_week = start_week + timedelta(days=6)
                        description = "this week"
                    elif week_ref == 'last_week':
                        start_week = today - timedelta(days=today.weekday() + 7)
                        end_week = start_week + timedelta(days=6)
                        description = "last week"
                    elif week_ref == 'next_week':
                        start_week = today + timedelta(days=7 - today.weekday())
                        end_week = start_week + timedelta(days=6)
                        description = "next week"
                    else:
                        return None
                    return start_week, end_week, description
                else:
                    return None
            except Exception as e:
                pass

        # Handle relative dates
        if date_info.get('relative_dates'):
            rel_date = date_info['relative_dates'][0]
            try:
                if rel_date == 'today':
                    start_date = end_date = today
                    description = "today"
                elif rel_date == 'yesterday':
                    start_date = end_date = today - timedelta(days=1)
                    description = "yesterday"
                elif rel_date == 'tomorrow':
                    start_date = end_date = today + timedelta(days=1)
                    description = "tomorrow"
                else:
                    return None
                return start_date, end_date, description
            except Exception as e:
                pass

        # Handle specific dates (month names, numeric dates)
        if date_info.get('specific_dates'):
            # Try to parse the specific date
            try:
                parsed_date = self._parse_date_from_message(message, today)
                if parsed_date:
                    description = parsed_date.strftime("%B %d, %Y")
                    return parsed_date, parsed_date, description
            except Exception as e:
                pass

        return None
        """
        Master method for validating and calculating temporal ranges with comprehensive edge case handling

        Args:
            temporal_type: Type of temporal calculation ('quarter', 'month', 'year', 'week')
            temporal_value: Value to calculate (e.g., 'Q1', 'this_month', 'last_year', '15')
            year: Optional year for quarter/week calculations

        Returns:
            Tuple of (start_date, end_date) for the temporal range

        Raises:
            ValueError: For invalid inputs or calculation errors
        """
        # Normalize inputs
        temporal_type = temporal_type.lower().strip()
        temporal_value = self._normalize_temporal_input(temporal_value)

        try:
            # Dispatch to appropriate calculation method with error handling
            if temporal_type == 'quarter':
                start_date, end_date = self._calculate_quarter_dates(temporal_value, year)
                context = f"quarter {temporal_value} {year or 'current year'}"

            elif temporal_type == 'month':
                start_date, end_date = self._calculate_month_dates(temporal_value)
                context = f"month period {temporal_value}"

            elif temporal_type == 'year':
                start_date, end_date = self._calculate_year_dates(temporal_value)
                context = f"year period {temporal_value}"

            elif temporal_type == 'week':
                start_date, end_date = self._calculate_week_dates(temporal_value, year)
                context = f"week {temporal_value} {year or 'current year'}"

            else:
                raise ValueError(f"Unknown temporal type: {temporal_type}. Use 'quarter', 'month', 'year', or 'week'")

            # Validate the calculated date boundaries
            self._validate_date_boundaries(start_date, end_date, context)

            # Handle any leap year edge cases
            start_date = self._handle_leap_year_edge_cases(start_date, f"{context} start date")
            end_date = self._handle_leap_year_edge_cases(end_date, f"{context} end date")

            return start_date, end_date

        except ValueError as e:
            # Re-raise with additional context
            raise ValueError(f"Error calculating {temporal_type} range for '{temporal_value}': {e}")
        except Exception as e:
            # Catch any unexpected errors
            raise ValueError(f"Unexpected error in temporal calculation: {e}")

    def _detect_task_action(self, message: str) -> Optional[str]:
        """Detect what the user wants to do with tasks"""

        # Check for explicit creation patterns first
        creation_indicators = [
            'create a task for',
            'add a task',
            'new task',
            'make a task',
            'task for reviewing',
            'task for',
            'need to create',
            'generate a task'
        ]

        for indicator in creation_indicators:
            if indicator in message:
                return 'create'

        # Check for search patterns first (before general 'show' patterns)
        search_indicators = [
            'search for',
            'look for',
            'search',
            'locate'
        ]

        # Check if it's a search command (including single word "look", "find")
        for indicator in search_indicators:
            if indicator in message:
                return 'search'

        # Handle single-word search commands
        if message.strip() in ['look', 'find']:
            return 'search'

        # Then check other action patterns
        action_patterns = {
            'show': ['show', 'list', 'display', 'see', 'view', 'get', 'what', 'which', 'tell me', 'give me'],
            'create': ['create', 'add', 'make', 'new', 'generate', 'build', 'task for', 'need to', 'should do', 'plan'],
            'edit': ['edit', 'modify', 'change', 'update', 'alter'],
            'delete': ['delete', 'remove', 'drop', 'eliminate'],
            'complete': ['complete', 'finish', 'done', 'mark'],
            'help': ['help', 'assist', 'guide', 'explain', 'how'],
            'time': ['time', 'duration', 'how long', 'schedule', 'available', 'free time']
        }

        for action, keywords in action_patterns.items():
            if any(keyword in message for keyword in keywords):
                return action

        return None

    def _detect_status_keywords(self, message: str) -> List[str]:
        """Detect task status references"""
        status_patterns = {
            'completed': ['completed', 'finished', 'done', 'complete'],
            'pending': ['pending', 'incomplete', 'unfinished', 'todo', 'remaining'],
            'priority': ['priority', 'important', 'urgent', 'high', 'medium', 'low']
        }

        found_statuses = []
        for status, keywords in status_patterns.items():
            if any(keyword in message for keyword in keywords):
                found_statuses.append(status)

        return found_statuses

    def _detect_classification_keywords(self, message: str) -> List[str]:
        """Detect task classification references"""
        classifications = {
            'planning': ['planning', 'plan'],
            'execution': ['execution', 'execute', 'implement'],
            'business': ['business', 'support'],
            'operational': ['operational', 'operation'],
            'offline': ['offline', 'processing']
        }

        found_classifications = []
        for classification, keywords in classifications.items():
            if any(keyword in message for keyword in keywords):
                found_classifications.append(classification)

        return found_classifications

    def _determine_smart_intent(self, message: str, extracted_info: Dict) -> str:
        """Intelligently determine intent based on all extracted information"""

        # Priority 0: Time tracking queries (check before date queries to prevent conflicts)
        time_tracking_indicators = [
            'how much time', 'time do i have', 'available time', 'free time',
            'how long', 'duration', 'time available', 'schedule today'
        ]
        if any(indicator in message for indicator in time_tracking_indicators):
            return 'time_tracking'

        # Priority 1: Date-related queries (highest priority to fix your issue)
        if extracted_info['contains_date']:
            # Strong indicators of date-based task queries
            date_query_indicators = [
                'scheduled', 'anything', 'do i have', 'what do i have', 'what am i doing',
                'tasks', 'appointments', 'meetings', 'planned'
            ]
            if any(indicator in message for indicator in date_query_indicators):
                return 'show_tasks_by_date'

            task_action = extracted_info.get('task_action')
            if task_action in ['show', 'see', 'view', 'find', 'get'] or not task_action:
                return 'show_tasks_by_date'
            elif task_action == 'create':
                return 'create_task'  # "create a task for july 10"

        # Priority 2: Clear task creation
        if extracted_info.get('task_action') == 'create':
            if any(num_word in message for num_word in ['multiple', 'several', 'few', 'many']) or \
               any(char.isdigit() for char in message if char.isdigit() and message.count(char) > 1):
                return 'create_multiple_tasks'
            elif 'break' in message or 'split' in message or 'subtask' in message:
                return 'break_down_task'
            else:
                return 'create_task'

        # Priority 3: Status-based queries
        if extracted_info['contains_status_keywords'] and extracted_info['contains_task_keywords']:
            return 'show_tasks_by_status'

        # Priority 4: Classification-based queries
        if extracted_info['contains_classification_keywords'] and extracted_info['contains_task_keywords']:
            return 'show_tasks_by_classification'

        # Priority 5: Other task actions
        task_action = extracted_info.get('task_action')
        if task_action:
            action_mapping = {
                'show': 'show_tasks',
                'edit': 'edit_task',
                'delete': 'delete_task',
                'complete': 'complete_task',
                'search': 'search_tasks',
                'help': 'help_request'
            }
            if task_action in action_mapping:
                return action_mapping[task_action]

        # Priority 6: General task queries
        if extracted_info['contains_task_keywords']:
            return 'show_tasks'

        # Priority 7: Help and guidance
        if any(help_word in message for help_word in ['help', 'how', 'what can', 'explain', 'guide']):
            return 'help_request'

        # Priority 8: Time tracking
        if any(time_word in message for time_word in ['time', 'hours', 'minutes', 'duration', 'long']):
            return 'time_tracking'

        # Priority 9: Suggestions
        if any(suggest_word in message for suggest_word in ['suggest', 'recommend', 'what should', 'next']):
            return 'get_suggestions'

        # Default: Unknown intent with helpful response
        return 'unknown'

    def _detect_follow_up_intent(self, message: str, extracted_info: Dict) -> Tuple[Optional[str], Dict]:
        """Detect follow-up questions based on conversation context"""

        # If no previous context, this is not a follow-up
        if not self.context.conversation_history:
            return None, extracted_info

        # First check: if the message contains complete standalone keywords, it's not a follow-up
        standalone_indicators = [
            # Complete task queries that should be standalone
            r'\bshow me (all|my)?\s*(tasks|schedule)',
            r'\bcreate (a )?task',
            r'\bget suggestions',
            # Classification or date-specific queries should be standalone
            r'\bbusiness support activities',
            r'\bplanning\b.*tasks?\b',
            r'\bexecution\b.*tasks?\b',
            r'\btoday\'?s?\s+tasks?',
            r'\byesterday\b.*tasks?',
            r'\bthis week\b',
            r'\blast month\b',
            # Specific help guide patterns that should be standalone
            r'\bshow me all my\b',
            r'\bwhat tasks did i have\b',
            r'\bhow productive have i been\b',
            r'\bwhat do i have scheduled\b',
            r'\bshow me my (completed|schedule)\b',
            r'\bhow much time did i spend\b'
        ]

        is_standalone = any(re.search(pattern, message, re.IGNORECASE) for pattern in standalone_indicators)

        # If it looks standalone, don't treat as follow-up regardless of other patterns
        if is_standalone:
            return None, extracted_info

        # Enhanced follow-up patterns - including affirmative responses
        follow_up_patterns = [
            # Clear references to previous results with demonstrative pronouns
            r'\b(those tasks?|these tasks?|them|that specific|this one|it)\b',
            # Question words specifically about previous context
            r'\b(which one|what about that|how about those)\b',
            # Continuation words that clearly reference previous conversation
            r'\b(also show|more of those|other ones|additional ones)\b',
            # Clear pronoun references to previous tasks/results
            r'\bwhat about (they|their|it)\b',
            # Affirmative responses to offers
            r'\b(yes|yeah|yep|sure|okay|ok|absolutely|definitely|please|of course)\b',
            # Negative responses to offers
            r'\b(no|nope|not now|maybe later|not really|no thanks)\b'
        ]

        is_potential_follow_up = any(re.search(pattern, message, re.IGNORECASE) for pattern in follow_up_patterns)

        # Additional context-specific follow-up detection
        recent_context = self.context.get_recent_context(2)

        # Special case: if we're waiting for a search term, treat any reasonable response as a follow-up
        if recent_context and self.context.last_offer_type == 'search_term_request':
            # Don't require traditional follow-up patterns when we asked for a search term
            is_potential_follow_up = True

        if is_potential_follow_up and recent_context:
            last_interaction = recent_context[-1]
            last_intent = last_interaction.get('intent')

            if last_intent:  # Only proceed if we have a valid last intent
                # Mark as follow-up and add context
                extracted_info['is_follow_up'] = True
                extracted_info['context_reference'] = last_intent
                extracted_info['previous_tasks'] = self.context.last_mentioned_tasks
                extracted_info['previous_date'] = self.context.last_mentioned_date
                extracted_info['previous_filter'] = self.context.current_filter_context
                extracted_info['last_offer_type'] = self.context.last_offer_type

                # Increment follow-up count
                self.context.follow_up_count += 1

                # Determine follow-up intent based on message content and context
                return self._determine_follow_up_intent(message, extracted_info, last_intent)

        return None, extracted_info

    def _determine_follow_up_intent(self, message: str, extracted_info: Dict, previous_intent: str) -> Tuple[str, Dict]:
        """Determine the specific intent for follow-up questions"""

        # Analyze what the user is asking about in relation to previous context
        message_lower = message.lower()

        # Handle affirmative responses to offers
        affirmative_patterns = [r'\b(yes|yeah|yep|sure|okay|ok|absolutely|definitely|please|of course)\b']
        negative_patterns = [r'\b(no|nope|not now|maybe later|not really|no thanks)\b']

        is_affirmative = any(re.search(pattern, message_lower) for pattern in affirmative_patterns)
        is_negative = any(re.search(pattern, message_lower) for pattern in negative_patterns)

        # Check if we have a recent offer from the bot
        last_offer_type = extracted_info.get('last_offer_type')

        if (is_affirmative or is_negative) and last_offer_type:
            if is_affirmative:
                # User accepted the offer
                if last_offer_type == 'task_creation_help':
                    return 'follow_up_accept_task_creation', extracted_info
                elif last_offer_type == 'planning_help':
                    return 'follow_up_accept_planning', extracted_info
                elif last_offer_type == 'suggestion_help':
                    return 'follow_up_accept_suggestions', extracted_info
            else:
                # User declined the offer
                return 'follow_up_decline_offer', extracted_info

        # Handle search term follow-up - when user responds to "What would you like to search for?"
        if last_offer_type == 'search_term_request':
            # User is providing search term in response to our question
            # Create a mock search request with the user's message as the search term
            extracted_info['groups'] = [message.strip()]
            return 'search_tasks', extracted_info

        # Filter/refine requests on previous results
        if any(word in message for word in ['only', 'just', 'filter', 'completed', 'pending', 'high', 'low']):
            if previous_intent in ['show_tasks', 'show_tasks_by_date', 'show_tasks_by_classification']:
                return 'follow_up_filter', extracted_info

        # Details about specific tasks from previous results
        if any(word in message for word in ['details', 'more info', 'tell me about', 'which one', 'what about']):
            if self.context.last_mentioned_tasks:
                return 'follow_up_task_details', extracted_info

        # Time-related follow-ups
        if any(word in message for word in ['how long', 'time', 'duration', 'when']):
            if previous_intent in ['show_tasks', 'show_tasks_by_date']:
                return 'follow_up_time_info', extracted_info

        # Modification requests
        if any(word in message for word in ['edit', 'change', 'update', 'modify']):
            if self.context.last_mentioned_tasks:
                return 'follow_up_edit', extracted_info

        # Completion requests
        if any(word in message for word in ['complete', 'done', 'finished', 'mark']):
            if self.context.last_mentioned_tasks:
                return 'follow_up_complete', extracted_info

        # Export/save requests
        if any(word in message for word in ['export', 'save', 'download', 'send']):
            if self.context.last_mentioned_tasks:
                return 'follow_up_export', extracted_info

        # Add to the list/create similar
        if any(word in message for word in ['add', 'create', 'make', 'new', 'another']):
            return 'follow_up_create_similar', extracted_info

        # Default follow-up - clarification or more info
        return 'follow_up_clarification', extracted_info

    def _generate_response(self, intent: str, extracted_info: Dict, original_message: str) -> ChatMessage:
        """Generate appropriate response based on intent"""
        try:
            if intent == 'greeting':
                return self._create_message(
                    self.response_templates['greeting'][0],
                    'text'
                )

            elif intent == 'create_task':
                return self._handle_create_task(extracted_info)

            elif intent == 'create_multiple_tasks':
                return self._handle_create_multiple_tasks(extracted_info)

            elif intent == 'break_down_task':
                return self._handle_break_down_task(extracted_info)

            elif intent == 'show_tasks':
                return self._handle_show_tasks(extracted_info)

            elif intent == 'show_tasks_by_date':
                return self._handle_show_tasks_by_date(extracted_info, original_message)

            elif intent == 'show_tasks_by_status':
                return self._handle_show_tasks_by_status(extracted_info, original_message)

            elif intent == 'show_tasks_by_classification':
                return self._handle_show_tasks_by_classification(extracted_info, original_message)

            elif intent == 'edit_task':
                return self._handle_edit_task(extracted_info)

            elif intent == 'delete_task':
                return self._handle_delete_task(extracted_info)

            elif intent == 'search_tasks':
                return self._handle_search_tasks(extracted_info, original_message)

            elif intent == 'export_tasks':
                return self._handle_export_tasks()

            elif intent == 'time_tracking':
                return self._handle_time_tracking(extracted_info, original_message)

            elif intent == 'help_request':
                return self._handle_help_request(extracted_info)

            # Follow-up intent handlers
            elif intent == 'follow_up_filter':
                return self._handle_follow_up_filter(extracted_info, original_message)

            elif intent == 'follow_up_task_details':
                return self._handle_follow_up_task_details(extracted_info, original_message)

            elif intent == 'follow_up_time_info':
                return self._handle_follow_up_time_info(extracted_info, original_message)

            elif intent == 'follow_up_edit':
                return self._handle_follow_up_edit(extracted_info, original_message)

            elif intent == 'follow_up_complete':
                return self._handle_follow_up_complete(extracted_info, original_message)

            elif intent == 'follow_up_export':
                return self._handle_follow_up_export(extracted_info, original_message)

            elif intent == 'follow_up_create_similar':
                return self._handle_follow_up_create_similar(extracted_info, original_message)

            elif intent == 'follow_up_clarification':
                return self._handle_follow_up_clarification(extracted_info, original_message)

            elif intent == 'follow_up_accept_task_creation':
                return self._handle_follow_up_accept_task_creation(extracted_info, original_message)

            elif intent == 'follow_up_accept_planning':
                return self._handle_follow_up_accept_planning(extracted_info, original_message)

            elif intent == 'follow_up_accept_suggestions':
                return self._handle_follow_up_accept_suggestions(extracted_info, original_message)

            elif intent == 'follow_up_decline_offer':
                return self._handle_follow_up_decline_offer(extracted_info, original_message)

            elif intent == 'complete_task':
                return self._handle_complete_task(extracted_info)

            elif intent == 'get_suggestions':
                return self._handle_get_suggestions()

            elif intent == 'schedule_optimization':
                return self._handle_schedule_optimization()

            elif intent == 'analytics_request':
                return self._handle_analytics_request()

            elif intent == 'task_related':
                return self._handle_general_task_query(extracted_info)

            elif intent == 'time_summary':
                return self._handle_time_summary(extracted_info, original_message)

            elif intent == 'out_of_scope':
                return self._handle_out_of_scope(extracted_info)

            elif intent == 'unknown':
                return self._handle_unknown_request(extracted_info, original_message)

            else:
                return self._create_message(
                    self.response_templates['clarification'][0],
                    'text'
                )

        except Exception as e:
            return self._create_message(
                self.response_templates['error'][0],
                'text'
            )

    def _handle_create_task(self, extracted_info: Dict) -> ChatMessage:
        """Handle single task creation"""
        task_description = extracted_info['groups'][0] if extracted_info.get('groups') else ''

        if not task_description:
            return self._create_message(
                "What task would you like me to create? Please describe what you need to do.",
                'text'
            )

        # Use AI engine to analyze the task
        user_tasks = self.data_manager.get_all_tasks()
        analysis = self.ai_engine.analyze_task(task_description, '', user_tasks)

        # Create the task
        task_data = {
            'title': task_description,
            'classification': analysis['classification']['classification'],
            'description': f"Created via AdBot on {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            'est_time': analysis['duration']['duration'],
            'date': date.today().strftime('%Y-%m-%d')
        }

        try:
            new_task = self.data_manager.add_task(task_data)

            response_text = f"✅ Created task: '{new_task['title']}'\n" \
                          f"📊 Classification: {new_task['classification']}\n" \
                          f"⏱️ Estimated time: {new_task['est_time']} minutes\n" \
                          f"🤖 AI Confidence: {int(analysis['classification']['confidence'] * 100)}%"

            return self._create_message(
                response_text,
                'task_created',
                {'task': new_task, 'analysis': analysis}
            )

        except Exception as e:
            return self._create_message(
                f"Sorry, I couldn't create that task. Error: {str(e)}",
                'text'
            )

    def _handle_create_multiple_tasks(self, extracted_info: Dict) -> ChatMessage:
        """Handle multiple task creation"""
        groups = extracted_info.get('groups', [])

        if len(groups) >= 2:
            count = int(groups[0])
            project_description = groups[1]
        else:
            return self._create_message(
                "Please specify how many tasks and what project they're for.",
                'text'
            )

        # Generate task suggestions based on project description
        suggestions = self._generate_task_breakdown(project_description, count)

        created_tasks = []
        for suggestion in suggestions[:count]:
            task_data = {
                'title': suggestion['title'],
                'classification': suggestion['classification'],
                'description': suggestion['description'],
                'est_time': suggestion['est_time'],
                'date': date.today().strftime('%Y-%m-%d')
            }

            try:
                new_task = self.data_manager.add_task(task_data)
                created_tasks.append(new_task)
            except Exception as e:
                continue

        if created_tasks:
            task_list = '\n'.join([f"• {task['title']} ({task['est_time']} min)" for task in created_tasks])
            response_text = f"✅ Created {len(created_tasks)} tasks for '{project_description}':\n\n{task_list}"

            return self._create_message(
                response_text,
                'task_list',
                {'tasks': created_tasks, 'project': project_description}
            )
        else:
            return self._create_message(
                "Sorry, I couldn't create the tasks. Please try with more specific details.",
                'text'
            )

    def _handle_break_down_task(self, extracted_info: Dict) -> ChatMessage:
        """Handle task breakdown into subtasks"""
        task_description = extracted_info['groups'][0] if extracted_info.get('groups') else ''

        if not task_description:
            return self._create_message(
                "What task would you like me to break down into smaller parts?",
                'text'
            )

        # Generate subtask breakdown
        subtasks = self._generate_task_breakdown(task_description, 5)

        created_tasks = []
        for i, subtask in enumerate(subtasks[:5], 1):
            task_data = {
                'title': f"{task_description} - Part {i}: {subtask['title']}",
                'classification': subtask['classification'],
                'description': subtask['description'],
                'est_time': subtask['est_time'],
                'date': date.today().strftime('%Y-%m-%d')
            }

            try:
                new_task = self.data_manager.add_task(task_data)
                created_tasks.append(new_task)
            except Exception as e:
                continue

        if created_tasks:
            task_list = '\n'.join([f"• {task['title']} ({task['est_time']} min)" for task in created_tasks])
            response_text = f"✅ Broke down '{task_description}' into {len(created_tasks)} subtasks:\n\n{task_list}"

            return self._create_message(
                response_text,
                'task_list',
                {'tasks': created_tasks, 'parent_task': task_description}
            )
        else:
            return self._create_message(
                "I couldn't break down that task. Try providing more specific details about what needs to be done.",
                'text'
            )

    def _handle_show_tasks(self, extracted_info: Dict) -> ChatMessage:
        """Handle task listing requests"""
        # Determine time period
        groups = extracted_info.get('groups', [])
        time_filter = groups[0] if groups else 'today'

        # Get tasks based on time filter
        if time_filter in ['today', '']:
            today = date.today().strftime('%Y-%m-%d')
            result = self.data_manager.filter_tasks({'start_date': today, 'end_date': today})
            tasks = result['tasks']
            title = "Today's Tasks"
        elif time_filter == 'tomorrow':
            tomorrow = (date.today() + timedelta(days=1)).strftime('%Y-%m-%d')
            result = self.data_manager.filter_tasks({'start_date': tomorrow, 'end_date': tomorrow})
            tasks = result['tasks']
            title = "Tomorrow's Tasks"
        else:
            # Show recent tasks
            result = self.data_manager.filter_tasks({}, per_page=10)
            tasks = result['tasks']
            title = "Recent Tasks"

        if not tasks:
            # Set offer type in context for no tasks response
            self.context.last_offer_type = 'task_creation_help'
            return self._create_message(
                self.response_templates['no_tasks'][0],
                'text'
            )

        # Format task list
        task_list = []
        total_time = 0
        for task in tasks:
            status = "✅" if task.get('completed') else "⏳"
            task_list.append(f"{status} {task['title']} ({task['est_time']} min)")
            total_time += task.get('est_time', 0)

        response_text = f"📋 {title} ({len(tasks)} tasks, {total_time} min total):\n\n" + "\n".join(task_list)

        return self._create_message(
            response_text,
            'task_list',
            {'tasks': tasks, 'time_filter': time_filter}
        )

    def _handle_complete_task(self, extracted_info: Dict) -> ChatMessage:
        """Handle task completion"""
        # For now, return a helpful message
        # In a full implementation, this would integrate with task completion logic
        return self._create_message(
            "Task completion feature coming soon! For now, you can mark tasks complete in the main interface.",
            'text'
        )

    def _handle_get_suggestions(self) -> ChatMessage:
        """Handle suggestion requests"""
        try:
            user_tasks = self.data_manager.get_all_tasks()

            # Get AI-powered suggestions
            suggestions = []
            if user_tasks:
                # Analyze patterns and suggest next tasks
                recent_tasks = sorted(user_tasks, key=lambda x: x.get('date', ''), reverse=True)[:10]

                # Simple suggestion logic (can be enhanced)
                suggestions = [
                    "Based on your patterns, consider scheduling time for planning tasks",
                    "You might want to review and update existing project tasks",
                    "Consider adding buffer time for unexpected issues"
                ]
            else:
                suggestions = [
                    "Start by creating your first task - what's the most important thing to do today?",
                    "Consider planning your week ahead with some organizational tasks",
                    "Think about breaking down larger projects into smaller, manageable tasks"
                ]

            response_text = "💡 Here are some suggestions:\n\n" + "\n".join([f"• {s}" for s in suggestions])

            return self._create_message(
                response_text,
                'suggestion',
                {'suggestions': suggestions}
            )

        except Exception as e:
            return self._create_message(
                "I'm having trouble generating suggestions right now. Try asking about specific tasks or projects.",
                'text'
            )

    def _handle_schedule_optimization(self) -> ChatMessage:
        """Handle schedule optimization requests"""
        user_tasks = self.data_manager.get_all_tasks()

        if not user_tasks:
            return self._create_message(
                "You don't have any tasks to optimize yet. Create some tasks first, and I'll help you organize them efficiently!",
                'text'
            )

        # Simple optimization suggestions
        suggestions = [
            "🎯 Focus on high-priority tasks in the morning when energy is highest",
            "⏰ Group similar tasks together to maintain focus",
            "📊 Balance quick wins with longer, complex tasks",
            "🔄 Include breaks between intensive tasks"
        ]

        response_text = "🚀 Schedule Optimization Tips:\n\n" + "\n".join(suggestions)

        return self._create_message(
            response_text,
            'suggestion',
            {'optimization_tips': suggestions}
        )

    def _handle_analytics_request(self) -> ChatMessage:
        """Handle analytics and insights requests"""
        try:
            user_tasks = self.data_manager.get_all_tasks()

            if not user_tasks:
                return self._create_message(
                    "No analytics available yet. Create some tasks and I'll provide insights about your productivity patterns!",
                    'text'
                )

            # Basic analytics
            total_tasks = len(user_tasks)
            total_time = sum(task.get('est_time', 0) for task in user_tasks)
            completed_tasks = len([t for t in user_tasks if t.get('completed')])

            # Classification breakdown
            classifications = {}
            for task in user_tasks:
                cls = task.get('classification', 'Unknown')
                classifications[cls] = classifications.get(cls, 0) + 1

            top_classification = max(classifications.keys(), key=lambda k: classifications[k]) if classifications else 'N/A'

            response_text = f"📊 Your Task Analytics:\n\n" \
                          f"📈 Total tasks: {total_tasks}\n" \
                          f"✅ Completed: {completed_tasks}\n" \
                          f"⏱️ Total estimated time: {total_time} minutes\n" \
                          f"🎯 Most common type: {top_classification}\n" \
                          f"⚡ Average task duration: {total_time // total_tasks if total_tasks > 0 else 0} minutes"

            return self._create_message(
                response_text,
                'analytics',
                {
                    'total_tasks': total_tasks,
                    'completed_tasks': completed_tasks,
                    'total_time': total_time,
                    'classifications': classifications
                }
            )

        except Exception as e:
            return self._create_message(
                "I couldn't generate analytics right now. Please try again later.",
                'text'
            )

    def _handle_general_task_query(self, extracted_info: Dict) -> ChatMessage:
        """Handle general task-related queries"""
        message = extracted_info.get('message', '')

        # Provide helpful guidance
        if 'help' in message or 'how' in message:
            response = """🤖 I can help you with:

• **Create tasks**: "Create a task for reviewing reports"
• **Multiple tasks**: "Generate 3 tasks for project planning"
• **Break down work**: "Break down 'implement login system' into subtasks"
• **Show tasks**: "Show me today's tasks"
• **Get suggestions**: "What should I work on next?"
• **Analytics**: "Show my productivity stats"
• **Optimize**: "Help me prioritize my tasks"

Just tell me what you need in natural language!"""
        else:
            response = "I understand you're asking about tasks. Could you be more specific? For example, you could ask me to create a task, show your tasks, or provide suggestions."

        return self._create_message(response, 'text')

    def _generate_task_breakdown(self, project_description: str, max_tasks: int = 5) -> List[Dict]:
        """Generate task breakdown for a project"""
        # This is a simplified version - in a full implementation,
        # this would use more sophisticated AI to break down projects

        breakdown_templates = {
            'planning': ['Research and analysis', 'Requirements gathering', 'Design phase', 'Planning documentation'],
            'implementation': ['Setup environment', 'Core implementation', 'Testing', 'Documentation', 'Deployment'],
            'review': ['Initial review', 'Detailed analysis', 'Documentation', 'Summary report'],
            'meeting': ['Preparation', 'Agenda creation', 'Conduct meeting', 'Follow-up actions'],
            'default': ['Planning', 'Execution', 'Review', 'Documentation', 'Follow-up']
        }

        # Determine project type
        project_lower = project_description.lower()
        if any(word in project_lower for word in ['plan', 'design', 'strategy']):
            template_key = 'planning'
        elif any(word in project_lower for word in ['implement', 'build', 'create', 'develop']):
            template_key = 'implementation'
        elif any(word in project_lower for word in ['review', 'analyze', 'evaluate']):
            template_key = 'review'
        elif any(word in project_lower for word in ['meeting', 'discussion', 'sync']):
            template_key = 'meeting'
        else:
            template_key = 'default'

        template = breakdown_templates[template_key]

        # Generate tasks
        tasks = []
        for i, task_name in enumerate(template[:max_tasks]):
            # Use AI to estimate classification and duration
            full_title = f"{task_name} for {project_description}"
            user_tasks = self.data_manager.get_all_tasks()
            analysis = self.ai_engine.analyze_task(full_title, '', user_tasks)

            tasks.append({
                'title': task_name,
                'classification': analysis['classification']['classification'],
                'description': f"Part of: {project_description}",
                'est_time': max(15, analysis['duration']['duration'])  # Minimum 15 minutes
            })

        return tasks

    def _handle_show_tasks_by_date(self, extracted_info: Dict, original_message: str) -> ChatMessage:
        """Enhanced date-specific task requests with comprehensive natural language understanding"""

        # First try the new natural language date parsing
        date_info = extracted_info
        parsed_result = self._parse_natural_language_date(original_message, date_info)

        if parsed_result:
            start_date, end_date, description = parsed_result
            return self._get_tasks_in_date_range(start_date, end_date, description)

        # Fallback to existing parsing methods for backward compatibility
        # Use the enhanced date detection from extracted_info
        date_entities = extracted_info.get('date_entities', [])
        relative_dates = extracted_info.get('relative_dates', [])
        specific_dates = extracted_info.get('specific_dates', [])

        today = date.today()
        target_date = None
        time_description = ""

        # Handle relative dates with natural language
        if relative_dates:
            for rel_date in relative_dates:
                if rel_date == 'today':
                    target_date = today
                    time_description = "today"
                elif rel_date == 'yesterday':
                    target_date = today - timedelta(days=1)
                    time_description = "yesterday"
                elif rel_date == 'tomorrow':
                    target_date = today + timedelta(days=1)
                    time_description = "tomorrow"
                elif rel_date == 'this_week':
                    start_week = today - timedelta(days=today.weekday())
                    end_week = start_week + timedelta(days=6)
                    return self._get_tasks_in_date_range(start_week, end_week, "this week")
                elif rel_date == 'last_week':
                    start_week = today - timedelta(days=today.weekday() + 7)
                    end_week = start_week + timedelta(days=6)
                    return self._get_tasks_in_date_range(start_week, end_week, "last week")
                elif rel_date == 'next_week':
                    start_week = today + timedelta(days=7 - today.weekday())
                    end_week = start_week + timedelta(days=6)
                    return self._get_tasks_in_date_range(start_week, end_week, "next week")
                break  # Use the first match

        # Handle specific dates with multiple parsing strategies
        if not target_date and (specific_dates or any(char.isdigit() for char in original_message)):
            target_date = self._parse_date_from_message(original_message, today)
            if target_date:
                time_description = target_date.strftime("%B %d, %Y")

        # If we found a date, get tasks for that date
        if target_date:
            return self._get_tasks_for_specific_date(target_date, time_description, original_message)

        # Enhanced help message with examples of supported formats
        help_examples = [
            "**Specific Dates:**",
            "• \"Show me tasks for July 10\"",
            "• \"Tasks for 7/15/2025\"",
            "• \"What tasks do I have on July 10th?\"",
            "",
            "**Relative Dates:**",
            "• \"Show me today's tasks\"",
            "• \"Yesterday's tasks\"",
            "• \"Tasks for this week\"",
            "",
            "**Natural Language:**",
            "• \"Tasks from 2 months ago\"",
            "• \"Show me last month's work\"",
            "• \"What did I do 3 weeks ago?\"",
            "• \"Tasks from the past 2 weeks\"",
            "",
            "**Time Periods:**",
            "• \"Tasks for Q1\"",
            "• \"Show me this year's work\"",
            "• \"Tasks for week 15\"",
            "",
            "What date or time period were you thinking of?"
        ]

        return self._create_message(
            f"I understand you're asking about tasks for a specific date, but I couldn't quite figure out which date you meant.\n\n"
            f"Here are some ways you can ask:\n\n" + "\n".join(help_examples),
            'structured_fallback',
            {
                'category': 'date_parsing_help',
                'suggestions': [
                    "Show me tasks for July 10",
                    "Tasks from last month",
                    "What did I do 2 weeks ago?",
                    "Show me this week's tasks",
                    "Tasks for Q2"
                ]
            }
        )

    def _parse_date_from_message(self, message: str, reference_date: date) -> Optional[date]:
        """Enhanced date parsing with multiple strategies and natural language"""
        message_lower = message.lower()

        # Strategy 1: Use existing specific date parser
        parsed_date = self._parse_specific_date(message_lower, reference_date)
        if parsed_date:
            return parsed_date

        # Strategy 2: More flexible month + day parsing
        months_map = {
            'january': 1, 'jan': 1, 'february': 2, 'feb': 2, 'march': 3, 'mar': 3,
            'april': 4, 'apr': 4, 'may': 5, 'june': 6, 'jun': 6,
            'july': 7, 'jul': 7, 'august': 8, 'aug': 8, 'september': 9, 'sep': 9, 'sept': 9,
            'october': 10, 'oct': 10, 'november': 11, 'nov': 11, 'december': 12, 'dec': 12
        }

        # Find month names in message
        found_month = None
        for month_name, month_num in months_map.items():
            if month_name in message_lower:
                found_month = month_num
                break

        if found_month:
            # Look for day numbers near the month
            import re
            day_patterns = [
                r'\b(\d{1,2})(?:st|nd|rd|th)?\b',  # 10th, 15th, 25
                r'\b(\d{1,2})\b'  # just numbers
            ]

            for pattern in day_patterns:
                day_matches = re.findall(pattern, message_lower)
                for day_str in day_matches:
                    try:
                        day = int(day_str)
                        if 1 <= day <= 31:
                            year = reference_date.year
                            # Try this year first, then next year if date has passed
                            try:
                                target_date = date(year, found_month, day)
                                return target_date
                            except ValueError:
                                try:
                                    target_date = date(year + 1, found_month, day)
                                    return target_date
                                except ValueError:
                                    continue
                    except (ValueError, TypeError):
                        continue

        return None

    def _get_tasks_for_specific_date(self, target_date: date, time_description: str, original_message: str) -> ChatMessage:
        """Get tasks for a specific date with human-like responses"""
        date_str = target_date.strftime('%Y-%m-%d')
        result = self.data_manager.filter_tasks({'start_date': date_str, 'end_date': date_str})
        tasks = result['tasks']

        if not tasks:
            # More natural "no tasks" response with offer to help
            responses = [
                f"I don't see any tasks scheduled for {time_description}. Would you like to plan something for that day?",
                f"Looks like {time_description} is free! Want me to help you add some tasks?",
                f"No tasks found for {time_description}. This could be a good day to catch up or plan ahead!"
            ]
            import random
            selected_response = random.choice(responses)

            # Set offer type in context if we're offering to help
            if "would you like" in selected_response.lower() or "want me to help" in selected_response.lower():
                self.context.last_offer_type = 'task_creation_help'

            return self._create_message(selected_response, 'text')

        # Format tasks with natural language
        task_list = []
        total_time = 0
        completed_count = 0

        for task in tasks:
            status = "✅" if task.get('completed') else "⏳"
            if task.get('completed'):
                completed_count += 1
            task_list.append(f"{status} {task['title']} ({task['est_time']} min)")
            total_time += task.get('est_time', 0)

        # Create human-like response
        if time_description in ['today', 'yesterday', 'tomorrow']:
            date_phrase = time_description
        else:
            date_phrase = f"on {time_description}"

        status_text = ""
        if completed_count > 0:
            status_text = f" ({completed_count}/{len(tasks)} completed)"

        time_text = f"{total_time // 60}h {total_time % 60}m" if total_time >= 60 else f"{total_time} min"

        response_text = f"Here's what you have {date_phrase}{status_text}:\n\n" + "\n".join(task_list)
        response_text += f"\n\n📊 Total time: {time_text}"

        return self._create_message(
            response_text,
            'task_list',
            {'tasks': tasks, 'date': date_str, 'time_description': time_description}
        )

    def _handle_unknown_request(self, extracted_info: Dict, original_message: str) -> ChatMessage:
        """Handle unknown requests with intelligent structured fallback responses"""

        # Analyze what the user might be asking about
        message = original_message.lower()

        # Check if it's task-related but we couldn't understand the specifics
        if any(word in message for word in ['task', 'work', 'project', 'job', 'assignment', 'todo']):
            suggestions = [
                "I can help you with tasks! Here are some things you can ask me:",
                "",
                "📝 **Create Tasks:**",
                "• \"Create a task for reviewing reports\"",
                "• \"Generate 3 tasks for project setup\"",
                "• \"Break down 'system upgrade' into subtasks\"",
                "",
                "📋 **View Tasks:**",
                "• \"Show me my tasks for today\"",
                "• \"List my completed tasks\"",
                "• \"What tasks do I have for July 10?\"",
                "",
                "🔍 **Search & Filter:**",
                "• \"Find tasks about budget\"",
                "• \"Show me planning tasks\"",
                "",
                "What would you like to do with your tasks?"
            ]
            return self._create_message(
                "\n".join(suggestions),
                'structured_fallback',
                {'category': 'task_related', 'suggestions': [
                    "Create a task for reviewing reports",
                    "Show me my tasks for today",
                    "List my completed tasks",
                    "Find tasks about budget"
                ]}
            )

        # Check if it's date-related but unclear
        if any(word in message for word in ['date', 'day', 'week', 'month', 'when', 'time', 'schedule']):
            suggestions = [
                "I can help you find tasks by date! Try asking like this:",
                "",
                "📅 **Specific Dates:**",
                "• \"Show me tasks for July 10\"",
                "• \"What tasks do I have tomorrow?\"",
                "• \"Tasks for 7/15/2025\"",
                "",
                "📊 **Time Periods:**",
                "• \"Tasks for this week\"",
                "• \"Show me last month's work\"",
                "• \"How much time did I spend in Q2?\"",
                "",
                "⏱️ **Time Analysis:**",
                "• \"How much time do I have today?\"",
                "• \"Time breakdown for this week\"",
                "",
                "What date or time period were you interested in?"
            ]
            return self._create_message(
                "\n".join(suggestions),
                'structured_fallback',
                {'category': 'date_related', 'suggestions': [
                    "Show me tasks for July 10",
                    "What tasks do I have tomorrow?",
                    "Tasks for this week",
                    "How much time do I have today?"
                ]}
            )

        # Check if it's analytics/productivity related
        if any(word in message for word in ['productivity', 'analytics', 'stats', 'performance', 'insights']):
            suggestions = [
                "I can provide productivity insights! Here's what I can analyze:",
                "",
                "📊 **Analytics:**",
                "• \"Show my productivity stats\"",
                "• \"How am I doing this week?\"",
                "• \"Time breakdown by task type\"",
                "",
                "💡 **Suggestions:**",
                "• \"What should I work on next?\"",
                "• \"Help me prioritize my tasks\"",
                "• \"Optimize my schedule\"",
                "",
                "🎯 **Insights:**",
                "• \"Most productive time of day\"",
                "• \"Task completion patterns\"",
                "",
                "What kind of insights would you like?"
            ]
            return self._create_message(
                "\n".join(suggestions),
                'structured_fallback',
                {'category': 'analytics_related', 'suggestions': [
                    "Show my productivity stats",
                    "What should I work on next?",
                    "Help me prioritize my tasks",
                    "Time breakdown by task type"
                ]}
            )

        # General helpful response with comprehensive structured suggestions
        responses = [
            "I'd love to help! I'm AdBot, your personal task assistant. I can help you:",
            "",
            "📝 **Task Management:**",
            "• Create new tasks and projects",
            "• Show tasks by date, status, or type",
            "• Search through your tasks",
            "",
            "📅 **Schedule & Planning:**",
            "• View tasks for specific dates",
            "• Track your time and progress",
            "• Get productivity suggestions",
            "",
            "📊 **Analytics & Insights:**",
            "• View productivity statistics",
            "• Time breakdowns and summaries",
            "• Task optimization recommendations",
            "",
            "💡 **Smart Features:**",
            "• Natural language task creation",
            "• Context-aware suggestions",
            "• Intelligent scheduling help",
            "",
            "🎯 **Quick Examples:**",
            "• \"What do I have scheduled for July 10?\"",
            "• \"Create a task for the quarterly review\"",
            "• \"Show me my completed work\"",
            "• \"How much time did I spend last month?\"",
            "",
            "What would you like to do?"
        ]

        return self._create_message(
            "\n".join(responses),
            'structured_fallback',
            {'category': 'general', 'suggestions': [
                "What do I have scheduled for July 10?",
                "Create a task for the quarterly review",
                "Show me my completed work",
                "How much time did I spend last month?",
                "What should I work on next?",
                "Help me prioritize my tasks"
            ]}
        )

    def _handle_out_of_scope(self, extracted_info: Dict) -> ChatMessage:
        """Handle out-of-scope (non-IT) requests with domain guard-rails"""
        blacklist_keywords = extracted_info.get('blacklist_keywords', [])

        response_parts = [
            "I'm designed to help with IT and project management tasks, but I noticed you're asking about something outside my expertise area.",
            "",
            "🎯 **I can help you with:**",
            "• Task creation and management",
            "• Project planning and tracking",
            "• Time management and scheduling",
            "• Productivity analytics and insights",
            "• Work organization and prioritization",
            "",
            "💡 **Try asking me things like:**",
            "• \"Create a task for system maintenance\"",
            "• \"Show me my tasks for this week\"",
            "• \"How much time did I spend on planning tasks?\"",
            "• \"Help me prioritize my project tasks\"",
            "• \"Generate tasks for quarterly review\"",
            "",
            "How can I assist you with your work and projects today?"
        ]

        return self._create_message(
            "\n".join(response_parts),
            'out_of_scope',
            {'rejected_keywords': blacklist_keywords}
        )

    def _handle_time_summary(self, extracted_info: Dict, original_message: str) -> ChatMessage:
        """Handle extended time period summary requests with enhanced natural language support"""
        message_lower = original_message.lower()
        user_tasks = self.data_manager.get_all_tasks()

        if not user_tasks:
            return self._create_message(
                "No tasks available for time analysis. Create some tasks first!",
                'text'
            )

        # Try the new natural language date parsing first
        parsed_result = self._parse_natural_language_date(original_message, extracted_info)

        if parsed_result:
            start_date, end_date, period_description = parsed_result
        else:
            # Fallback to existing parsing methods
            start_date = None
            end_date = None
            period_description = ""

            # Check for quarters
            if any(q in message_lower for q in ['q1', 'quarter 1', 'first quarter']):
                start_date, end_date = self._calculate_quarter_dates('Q1')
                period_description = "Q1"
            elif any(q in message_lower for q in ['q2', 'quarter 2', 'second quarter']):
                start_date, end_date = self._calculate_quarter_dates('Q2')
                period_description = "Q2"
            elif any(q in message_lower for q in ['q3', 'quarter 3', 'third quarter']):
                start_date, end_date = self._calculate_quarter_dates('Q3')
                period_description = "Q3"
            elif any(q in message_lower for q in ['q4', 'quarter 4', 'fourth quarter']):
                start_date, end_date = self._calculate_quarter_dates('Q4')
                period_description = "Q4"

            # Check for months
            elif any(m in message_lower for m in ['last month', 'previous month']):
                start_date, end_date = self._calculate_month_dates('last_month')
                period_description = "last month"
            elif any(m in message_lower for m in ['this month', 'current month']):
                start_date, end_date = self._calculate_month_dates('this_month')
                period_description = "this month"
            elif any(m in message_lower for m in ['next month', 'following month']):
                start_date, end_date = self._calculate_month_dates('next_month')
                period_description = "next month"

            # Check for years
            elif any(y in message_lower for y in ['last year', 'previous year']):
                start_date, end_date = self._calculate_year_dates('last_year')
                period_description = "last year"
            elif any(y in message_lower for y in ['this year', 'current year']):
                start_date, end_date = self._calculate_year_dates('this_year')
                period_description = "this year"
            elif any(y in message_lower for y in ['next year', 'following year']):
                start_date, end_date = self._calculate_year_dates('next_year')
                period_description = "next year"

            # Check for week numbers
            import re
            week_match = re.search(r'week (\d{1,2})', message_lower)
            if week_match:
                week_num = week_match.group(1)
                start_date, end_date = self._calculate_week_dates(week_num)
                period_description = f"week {week_num}"

        if not start_date or not end_date:
            return self._create_message(
                "I couldn't determine which time period you're asking about. Try being more specific like:\n\n"
                "**Natural Language Examples:**\n"
                "• \"How much time did I spend last quarter?\"\n"
                "• \"Time summary for the past 2 months\"\n"
                "• \"Show me last month's work hours\"\n"
                "• \"How productive was I 3 weeks ago?\"\n"
                "• \"Work breakdown for the last 30 days\"\n\n"
                "**Specific Periods:**\n"
                "• \"Time spent in Q2\"\n"
                "• \"This year's productivity summary\"\n"
                "• \"Week 15 time analysis\"",
                'structured_fallback',
                {
                    'category': 'time_summary_help',
                    'suggestions': [
                        "Time summary for the past 2 months",
                        "How much time did I spend last quarter?",
                        "Show me last month's work hours",
                        "Work breakdown for the last 30 days",
                        "This year's productivity summary"
                    ]
                }
            )

        # Filter tasks for the date range
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        result = self.data_manager.filter_tasks({'start_date': start_str, 'end_date': end_str})
        period_tasks = result['tasks']

        if not period_tasks:
            return self._create_message(
                f"No tasks found for {period_description}. You were either very efficient or taking a well-deserved break!",
                'time_summary'
            )

        # Calculate time aggregations
        total_minutes = sum(task.get('est_time', 0) for task in period_tasks)
        completed_tasks = [task for task in period_tasks if task.get('completed')]
        completed_minutes = sum(task.get('est_time', 0) for task in completed_tasks)

        # Break down by classification
        classification_breakdown = {}
        for task in period_tasks:
            cls = task.get('classification', 'Unknown')
            if cls not in classification_breakdown:
                classification_breakdown[cls] = {'count': 0, 'minutes': 0}
            classification_breakdown[cls]['count'] += 1
            classification_breakdown[cls]['minutes'] += task.get('est_time', 0)

        # Format the response
        total_hours = total_minutes // 60
        total_mins = total_minutes % 60
        completed_hours = completed_minutes // 60
        completed_mins = completed_minutes % 60

        # Enhanced response with better formatting
        response_parts = [
            f"📊 **Time Summary for {period_description}**",
            f"📅 Period: {start_date.strftime('%B %d, %Y')} to {end_date.strftime('%B %d, %Y')}",
            "",
            f"⏱️ **Total estimated time:** {total_hours}h {total_mins}m ({total_minutes} minutes)",
            f"✅ **Completed work:** {completed_hours}h {completed_mins}m ({completed_minutes} minutes)",
            f"📋 **Total tasks:** {len(period_tasks)} ({len(completed_tasks)} completed)",
        ]

        # Add completion percentage if we have data
        completion_percentage = 0
        if total_minutes > 0:
            completion_percentage = (completed_minutes / total_minutes) * 100
            response_parts.append(f"📈 **Completion rate:** {completion_percentage:.1f}%")

        response_parts.append("")

        if classification_breakdown:
            response_parts.append("📈 **Breakdown by type:**")
            for cls, data in sorted(classification_breakdown.items(), key=lambda x: x[1]['minutes'], reverse=True):
                cls_hours = data['minutes'] // 60
                cls_mins = data['minutes'] % 60
                percentage = (data['minutes'] / total_minutes * 100) if total_minutes > 0 else 0
                response_parts.append(f"• {cls}: {cls_hours}h {cls_mins}m ({data['count']} tasks, {percentage:.1f}%)")
            response_parts.append("")

        if period_tasks:
            avg_time = total_minutes // len(period_tasks)
            response_parts.append(f"� **Average task duration:** {avg_time} minutes")

        # Add productivity insights
        if len(period_tasks) > 0:
            response_parts.append("")
            response_parts.append("💡 **Insights:**")

            if completion_percentage >= 80:
                response_parts.append("• Excellent completion rate! You're staying on top of your tasks.")
            elif completion_percentage >= 60:
                response_parts.append("• Good progress on your tasks. Consider focusing on completion.")
            else:
                response_parts.append("• Opportunity to improve task completion. Consider breaking tasks into smaller parts.")

            # Most productive classification
            if classification_breakdown:
                top_classification = max(classification_breakdown.keys(), key=lambda k: classification_breakdown[k]['minutes'])
                response_parts.append(f"• Most time spent on: {top_classification}")

        return self._create_message(
            "\n".join(response_parts),
            'time_summary',
            {
                'period': period_description,
                'start_date': start_str,
                'end_date': end_str,
                'total_minutes': total_minutes,
                'completed_minutes': completed_minutes,
                'tasks': period_tasks,
                'classification_breakdown': classification_breakdown
            }
        )

    # Follow-up Intent Handlers
    def _handle_follow_up_filter(self, extracted_info: Dict, original_message: str) -> ChatMessage:
        """Handle follow-up requests to filter previous results"""
        previous_tasks = extracted_info.get('previous_tasks', [])

        if not previous_tasks:
            return self._create_message(
                "I don't have any previous task results to filter. Could you ask me to show some tasks first?",
                'text'
            )

        message_lower = original_message.lower()
        filtered_tasks = []
        filter_description = ""

        # Apply filters based on the message
        if any(word in message_lower for word in ['completed', 'done', 'finished']):
            filtered_tasks = [t for t in previous_tasks if t.get('completed')]
            filter_description = "completed"
        elif any(word in message_lower for word in ['pending', 'incomplete', 'unfinished']):
            filtered_tasks = [t for t in previous_tasks if not t.get('completed')]
            filter_description = "pending"
        elif any(word in message_lower for word in ['high', 'priority', 'urgent']):
            filtered_tasks = [t for t in previous_tasks if t.get('urgency') == 'high']
            filter_description = "high priority"
        elif any(word in message_lower for word in ['planning']):
            filtered_tasks = [t for t in previous_tasks if 'planning' in t.get('classification', '').lower()]
            filter_description = "planning"
        else:
            # Generic filter - show all previous tasks again
            filtered_tasks = previous_tasks
            filter_description = "all"

        if not filtered_tasks:
            return self._create_message(
                f"I didn't find any {filter_description} tasks in the previous results.",
                'text'
            )

        # Format the filtered results
        task_list = []
        total_time = 0
        for task in filtered_tasks:
            status = "✅" if task.get('completed') else "⏳"
            task_list.append(f"{status} {task['title']} ({task['est_time']} min)")
            total_time += task.get('est_time', 0)

        response_text = f"📋 Filtered to {filter_description} tasks ({len(filtered_tasks)} tasks, {total_time} min total):\n\n" + "\n".join(task_list)

        # Update context with filtered results
        self.context.last_mentioned_tasks = filtered_tasks

        return self._create_message(
            response_text,
            'task_list',
            {'tasks': filtered_tasks, 'filter': filter_description}
        )

    def _handle_follow_up_task_details(self, extracted_info: Dict, original_message: str) -> ChatMessage:
        """Handle requests for more details about specific tasks"""
        previous_tasks = extracted_info.get('previous_tasks', [])

        if not previous_tasks:
            return self._create_message(
                "I don't have any previous tasks to provide details about. Could you ask me to show some tasks first?",
                'text'
            )

        # For now, provide summary details about all tasks
        # In a more advanced implementation, you could parse which specific task they're asking about

        details = []
        for i, task in enumerate(previous_tasks[:5], 1):  # Limit to first 5 tasks
            status = "✅ Completed" if task.get('completed') else "⏳ Pending"
            details.append(f"**{i}. {task['title']}**")
            details.append(f"   📊 Classification: {task.get('classification', 'Unknown')}")
            details.append(f"   ⏱️ Estimated time: {task['est_time']} minutes")
            details.append(f"   📅 Date: {task.get('date', 'No date set')}")
            details.append(f"   📝 Status: {status}")
            if task.get('description'):
                details.append(f"   📄 Description: {task['description']}")
            details.append("")

        response_text = f"📋 Task Details:\n\n" + "\n".join(details)

        return self._create_message(response_text, 'text')

    def _handle_follow_up_time_info(self, extracted_info: Dict, original_message: str) -> ChatMessage:
        """Handle follow-up requests about time/duration"""
        previous_tasks = extracted_info.get('previous_tasks', [])

        if not previous_tasks:
            return self._create_message(
                "I don't have any previous tasks to analyze time for. Could you ask me to show some tasks first?",
                'text'
            )

        # Calculate time information
        total_time = sum(task.get('est_time', 0) for task in previous_tasks)
        completed_time = sum(task.get('est_time', 0) for task in previous_tasks if task.get('completed'))
        remaining_time = total_time - completed_time

        hours = total_time // 60
        minutes = total_time % 60

        completed_hours = completed_time // 60
        completed_minutes = completed_time % 60

        remaining_hours = remaining_time // 60
        remaining_minutes = remaining_time % 60

        response_parts = [
            f"⏱️ **Time Analysis for {len(previous_tasks)} tasks:**",
            "",
            f"📊 **Total estimated time:** {hours}h {minutes}m ({total_time} minutes)",
            f"✅ **Completed work:** {completed_hours}h {completed_minutes}m ({completed_time} minutes)",
            f"⏳ **Remaining work:** {remaining_hours}h {remaining_minutes}m ({remaining_time} minutes)"
        ]

        if previous_tasks:
            avg_time = total_time // len(previous_tasks)
            response_parts.append(f"📈 **Average task duration:** {avg_time} minutes")

        return self._create_message("\n".join(response_parts), 'text')

    def _handle_follow_up_edit(self, extracted_info: Dict, original_message: str) -> ChatMessage:
        """Handle follow-up requests to edit tasks"""
        return self._create_message(
            "Task editing through chat is coming soon! For now, you can edit tasks in the main interface. "
            "Which task would you like to modify?",
            'text'
        )

    def _handle_follow_up_complete(self, extracted_info: Dict, original_message: str) -> ChatMessage:
        """Handle follow-up requests to complete tasks"""
        return self._create_message(
            "Task completion through chat is coming soon! For now, you can mark tasks complete in the main interface. "
            "Which task did you finish?",
            'text'
        )

    def _handle_follow_up_export(self, extracted_info: Dict, original_message: str) -> ChatMessage:
        """Handle follow-up requests to export tasks"""
        previous_tasks = extracted_info.get('previous_tasks', [])

        if not previous_tasks:
            return self._create_message(
                "I don't have any previous tasks to export. Could you ask me to show some tasks first?",
                'text'
            )

        return self._create_message(
            f"I'd love to export those {len(previous_tasks)} tasks for you! "
            f"Export functionality through chat is coming soon. For now, you can export tasks using the main interface.",
            'text'
        )

    def _handle_follow_up_create_similar(self, extracted_info: Dict, original_message: str) -> ChatMessage:
        """Handle follow-up requests to create similar tasks"""
        previous_tasks = extracted_info.get('previous_tasks', [])

        if previous_tasks:
            # Use the last task as a template
            template_task = previous_tasks[-1]
            return self._create_message(
                f"I can help you create a task similar to '{template_task['title']}'! "
                f"What would you like to call the new task? I'll use similar settings "
                f"(Classification: {template_task.get('classification', 'Unknown')}, "
                f"Estimated time: {template_task['est_time']} min).",
                'text'
            )
        else:
            return self._create_message(
                "What kind of task would you like me to create? Just describe what you need to do.",
                'text'
            )

    def _handle_follow_up_clarification(self, extracted_info: Dict, original_message: str) -> ChatMessage:
        """Handle general follow-up clarification requests"""
        context_ref = extracted_info.get('context_reference')

        if context_ref == 'show_tasks_by_date':
            return self._create_message(
                "I showed you tasks for a specific date. Would you like me to:\n"
                "• Show details about any of those tasks?\n"
                "• Filter them (e.g., 'show only completed ones')?\n"
                "• See the time breakdown?\n"
                "• Create a similar task?\n\n"
                "Just let me know what you'd like to do!",
                'text'
            )
        elif context_ref in ['show_tasks', 'show_tasks_by_status', 'show_tasks_by_classification']:
            return self._create_message(
                "I showed you a list of tasks. You can ask me to:\n"
                "• Filter them further (e.g., 'only the high priority ones')\n"
                "• Get more details about specific tasks\n"
                "• See the total time estimate\n"
                "• Export them\n"
                "• Create a similar task\n\n"
                "What would you like to know?",
                'text'
            )
        else:
            return self._create_message(
                "I'm not sure what you're referring to. Could you be more specific about what you'd like to do?",
                'text'
            )

    def _handle_follow_up_accept_task_creation(self, extracted_info: Dict, original_message: str) -> ChatMessage:
        """Handle when user accepts offer to help with task creation"""
        return self._create_message(
            "Perfect! I'd be happy to help you create some tasks. Here are a few ways we can get started:\n\n"
            "📝 **Quick Task Creation:**\n"
            "• \"Create a task for reviewing quarterly reports\"\n"
            "• \"Generate 3 tasks for project planning\"\n"
            "• \"Break down 'system maintenance' into subtasks\"\n\n"
            "🎯 **I can suggest tasks based on common work activities:**\n"
            "• Planning and preparation tasks\n"
            "• Meeting and collaboration tasks\n"
            "• Documentation and review tasks\n"
            "• Project execution tasks\n\n"
            "Just tell me what you need to work on, or ask me to suggest some tasks for you!",
            'structured_fallback',
            {
                'category': 'task_creation_help',
                'suggestions': [
                    "Create a task for team status meeting",
                    "Generate 4 tasks for system maintenance",
                    "Suggest some planning tasks for this week",
                    "Break down 'quarterly review' into steps"
                ]
            }
        )

    def _handle_follow_up_accept_planning(self, extracted_info: Dict, original_message: str) -> ChatMessage:
        """Handle when user accepts offer to help with planning"""
        return self._create_message(
            "Great! I can help you plan and organize your work. Here's what I can do:\n\n"
            "📅 **Schedule Planning:**\n"
            "• Show your tasks by date or time period\n"
            "• Analyze your workload and time estimates\n"
            "• Suggest optimal task ordering\n\n"
            "⚡ **Productivity Optimization:**\n"
            "• Identify patterns in your work\n"
            "• Recommend task prioritization\n"
            "• Balance different types of activities\n\n"
            "📊 **Time Management:**\n"
            "• Break down time by task type\n"
            "• Track estimated vs actual time\n"
            "• Find opportunities for efficiency\n\n"
            "What aspect of planning would you like help with?",
            'structured_fallback',
            {
                'category': 'planning_help',
                'suggestions': [
                    "Help me organize this week's tasks",
                    "Show me my time breakdown",
                    "What should I prioritize today?",
                    "Optimize my task schedule"
                ]
            }
        )

    def _handle_follow_up_accept_suggestions(self, extracted_info: Dict, original_message: str) -> ChatMessage:
        """Handle when user accepts offer for suggestions"""
        try:
            user_tasks = self.data_manager.get_all_tasks()

            suggestions = []
            if user_tasks:
                # Analyze patterns and suggest next tasks
                recent_tasks = sorted(user_tasks, key=lambda x: x.get('date', ''), reverse=True)[:10]

                # Get classification patterns
                classifications = {}
                for task in recent_tasks:
                    cls = task.get('classification', 'Unknown')
                    classifications[cls] = classifications.get(cls, 0) + 1

                if classifications:
                    top_classification = max(classifications.keys(), key=lambda k: classifications[k])
                    suggestions = [
                        f"Continue with {top_classification} tasks - you've been productive in this area",
                        "Consider balancing with some planning activities",
                        "Add buffer time for unexpected issues or follow-ups",
                        "Schedule time to review and update existing tasks"
                    ]
                else:
                    suggestions = [
                        "Focus on high-impact planning activities first",
                        "Break down complex projects into manageable steps",
                        "Schedule regular check-ins and progress reviews"
                    ]
            else:
                suggestions = [
                    "Start with planning tasks to set up your week",
                    "Create some organizational and setup tasks",
                    "Think about your key priorities and break them down",
                    "Add both quick wins and longer-term project tasks"
                ]

            suggestion_text = "💡 **Here are my suggestions based on your patterns:**\n\n" + \
                            "\n".join([f"• {s}" for s in suggestions])

            return self._create_message(
                suggestion_text,
                'suggestion',
                {
                    'suggestions': suggestions,
                    'based_on': 'user_patterns' if user_tasks else 'general_productivity'
                }
            )

        except Exception as e:
            return self._create_message(
                "I'd be happy to provide suggestions! Here are some general recommendations:\n\n"
                "• Start with planning and preparation tasks\n"
                "• Balance quick wins with longer-term projects\n"
                "• Include time for reviews and follow-ups\n"
                "• Consider your energy levels throughout the day\n\n"
                "What specific area would you like suggestions for?",
                'suggestion'
            )

    def _handle_follow_up_decline_offer(self, extracted_info: Dict, original_message: str) -> ChatMessage:
        """Handle when user declines an offer"""
        return self._create_message(
            "No problem! I'm here whenever you need help. Feel free to ask me:\n\n"
            "• To show your existing tasks\n"
            "• For productivity insights\n"
            "• To help organize your schedule\n"
            "• Any questions about task management\n\n"
            "What else can I help you with?",
            'text'
        )

    def _parse_specific_date(self, message: str, reference_date: date) -> Optional[date]:
        """Parse specific dates from the message"""
        import re
        from datetime import datetime

        # Month name mapping
        months = {
            'january': 1, 'jan': 1,
            'february': 2, 'feb': 2,
            'march': 3, 'mar': 3,
            'april': 4, 'apr': 4,
            'may': 5,
            'june': 6, 'jun': 6,
            'july': 7, 'jul': 7,
            'august': 8, 'aug': 8,
            'september': 9, 'sep': 9, 'sept': 9,
            'october': 10, 'oct': 10,
            'november': 11, 'nov': 11,
            'december': 12, 'dec': 12
        }

        # Try different date patterns
        patterns = [
            # "July 10", "july 10", "Jul 10"
            r'(january|february|march|april|may|june|july|august|september|october|november|december|jan|feb|mar|apr|may|jun|jul|aug|sep|sept|oct|nov|dec)\s+(\d{1,2})',
            # "10 July", "10 Jul"
            r'(\d{1,2})\s+(january|february|march|april|may|june|july|august|september|october|november|december|jan|feb|mar|apr|may|jun|jul|aug|sep|sept|oct|nov|dec)',
            # "2025-07-10"
            r'(\d{4})-(\d{1,2})-(\d{1,2})',
            # "7/10/2025", "7/10/25", "07/10/2025"
            r'(\d{1,2})/(\d{1,2})/(\d{2,4})',
            # "7/10", "07/10" (assume current year) - more specific pattern
            r'(\d{1,2})/(\d{1,2})(?!/\d)(?!\d)'
        ]

        for pattern in patterns:
            match = re.search(pattern, message)
            if match:
                groups = match.groups()

                try:
                    if len(groups) == 2:
                        # Month name + day or day + month name
                        if groups[0].isdigit():
                            # "10 July" format
                            day = int(groups[0])
                            month_str = groups[1]
                        else:
                            # "July 10" format
                            month_str = groups[0]
                            day = int(groups[1])

                        month = months.get(month_str.lower())
                        if month:
                            year = reference_date.year
                            return date(year, month, day)

                    elif len(groups) == 3:
                        if pattern.endswith(r'(\d{4})-(\d{1,2})-(\d{1,2})'):
                            # "2025-07-10" format
                            year, month, day = int(groups[0]), int(groups[1]), int(groups[2])
                        else:
                            # "7/10/2025" or "7/10/25" format
                            month, day, year = int(groups[0]), int(groups[1]), int(groups[2])
                            if year < 100:  # Handle 2-digit years
                                year = 2000 + year if year < 50 else 1900 + year

                        return date(year, month, day)

                    elif len(groups) == 2 and '/' in pattern:
                        # "7/10" format (assume current year)
                        month, day = int(groups[0]), int(groups[1])
                        year = reference_date.year
                        return date(year, month, day)

                except (ValueError, TypeError):
                    continue  # Invalid date, try next pattern

        return None

    def _get_tasks_in_date_range(self, start_date: date, end_date: date, description: str) -> ChatMessage:
        """Get tasks in a date range"""
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')

        result = self.data_manager.filter_tasks({'start_date': start_str, 'end_date': end_str})
        tasks = result['tasks']

        if not tasks:
            return self._create_message(
                f"No tasks found for {description.lower()}. Would you like me to help you plan some?",
                'text'
            )

        # Group tasks by date
        tasks_by_date = {}
        total_time = 0
        for task in tasks:
            task_date = task.get('date', start_str)
            if task_date not in tasks_by_date:
                tasks_by_date[task_date] = []
            tasks_by_date[task_date].append(task)
            total_time += task.get('est_time', 0)

        response_parts = [f"📋 {description} Tasks ({len(tasks)} total, {total_time} min):\n"]

        for date_str in sorted(tasks_by_date.keys()):
            date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
            day_name = date_obj.strftime('%A, %B %d')
            response_parts.append(f"\n📅 **{day_name}**:")

            for task in tasks_by_date[date_str]:
                status = "✅" if task.get('completed') else "⏳"
                response_parts.append(f"  {status} {task['title']} ({task['est_time']} min)")

        return self._create_message(
            "\n".join(response_parts),
            'task_list',
            {'tasks': tasks, 'date_range': f"{start_str} to {end_str}"}
        )

    def _handle_show_tasks_by_status(self, extracted_info: Dict, original_message: str) -> ChatMessage:
        """Handle status-based task filtering"""
        message_lower = original_message.lower()

        user_tasks = self.data_manager.get_all_tasks()
        if not user_tasks:
            return self._create_message(
                "You don't have any tasks yet. Would you like me to help you create some?",
                'text'
            )

        filtered_tasks = []
        status_description = ""

        if any(word in message_lower for word in ['completed', 'finished', 'done']):
            filtered_tasks = [task for task in user_tasks if task.get('completed', False)]
            status_description = "Completed"
        elif any(word in message_lower for word in ['pending', 'incomplete', 'unfinished']):
            filtered_tasks = [task for task in user_tasks if not task.get('completed', False)]
            status_description = "Pending"
        elif 'high priority' in message_lower or 'high' in message_lower:
            # This would need priority field in tasks - for now, use urgency from AI analysis
            filtered_tasks = user_tasks  # Placeholder
            status_description = "High Priority"

        if not filtered_tasks:
            return self._create_message(
                f"No {status_description.lower()} tasks found.",
                'text'
            )

        task_list = []
        total_time = 0
        for task in filtered_tasks:
            task_list.append(f"• {task['title']} ({task['est_time']} min) - {task.get('date', 'No date')}")
            total_time += task.get('est_time', 0)

        response_text = f"📋 {status_description} Tasks ({len(filtered_tasks)} tasks, {total_time} min total):\n\n" + "\n".join(task_list)

        return self._create_message(
            response_text,
            'task_list',
            {'tasks': filtered_tasks, 'filter': status_description}
        )

    def _handle_show_tasks_by_classification(self, extracted_info: Dict, original_message: str) -> ChatMessage:
        """Handle classification-based task filtering"""
        groups = extracted_info.get('groups', [])
        if not groups:
            return self._create_message(
                "What type of tasks are you looking for? (e.g., 'planning tasks', 'execution tasks')",
                'text'
            )

        classification_query = groups[0].lower()

        # Map common terms to classifications
        classification_mapping = {
            'planning': 'Planning',
            'execution': 'Execution',
            'business': 'Business Support Activities',
            'operational': 'Operational Project Involvement',
            'offline': 'Offline Processing',
            'processing': 'Offline Processing'
        }

        target_classification = None
        for key, value in classification_mapping.items():
            if key in classification_query:
                target_classification = value
                break

        if not target_classification:
            # Try direct match
            user_tasks = self.data_manager.get_all_tasks()
            classifications = set(task.get('classification', '') for task in user_tasks)
            for cls in classifications:
                if cls.lower() in classification_query or classification_query in cls.lower():
                    target_classification = cls
                    break

        if not target_classification:
            return self._create_message(
                f"I couldn't find tasks matching '{classification_query}'. Available types: Planning, Execution, Business Support Activities, Operational Project Involvement, Offline Processing.",
                'text'
            )

        user_tasks = self.data_manager.get_all_tasks()
        filtered_tasks = [task for task in user_tasks if task.get('classification', '') == target_classification]

        if not filtered_tasks:
            return self._create_message(
                f"No {target_classification} tasks found.",
                'text'
            )

        task_list = []
        total_time = 0
        for task in filtered_tasks:
            task_list.append(f"• {task['title']} ({task['est_time']} min) - {task.get('date', 'No date')}")
            total_time += task.get('est_time', 0)

        response_text = f"📋 {target_classification} Tasks ({len(filtered_tasks)} tasks, {total_time} min total):\n\n" + "\n".join(task_list)

        return self._create_message(
            response_text,
            'task_list',
            {'tasks': filtered_tasks, 'classification': target_classification}
        )

    def _handle_edit_task(self, extracted_info: Dict) -> ChatMessage:
        """Handle task editing requests"""
        return self._create_message(
            "Task editing through chat is coming soon! For now, you can edit tasks in the main interface by clicking on them.",
            'text'
        )

    def _handle_delete_task(self, extracted_info: Dict) -> ChatMessage:
        """Handle task deletion requests"""
        return self._create_message(
            "Task deletion through chat is coming soon! For now, you can delete tasks in the main interface.",
            'text'
        )

    def _handle_search_tasks(self, extracted_info: Dict, original_message: str = '') -> ChatMessage:
        """Handle task search requests"""
        import re

        message_to_check = original_message.lower().strip() if original_message else extracted_info.get('cleaned_message', '').lower().strip()

        # Check if the user just said a search command without specifying what to search for
        incomplete_search_patterns = [
            r'^(?:search|find|look for|locate)$',
            r'^(?:search|find|look) for$',
            r'^(?:search|find|look for) tasks?$',
            r'^(?:search|find|look) (?:through|in) (?:my )?tasks?$'
        ]

        is_incomplete_search = any(re.match(pattern, message_to_check) for pattern in incomplete_search_patterns)

        if is_incomplete_search:
            # Set context to indicate we're waiting for a search term
            self.context.last_offer_type = 'search_term_request'
            return self._create_message(
                "What would you like to search for in your tasks?",
                'text'
            )

        # Extract search term by removing command words
        search_term = message_to_check

        # Remove search command prefixes to get just the search term
        search_prefixes = [
            r'^search for (.+)',
            r'^look for (.+)',
            r'^find (.+)',
            r'^search (.+)',
            r'^locate (.+)'
        ]

        for pattern in search_prefixes:
            match = re.match(pattern, message_to_check)
            if match:
                search_term = match.group(1).strip()
                break

        # If no extraction pattern matched, check if the search term is just a search command word
        if search_term in ['search', 'find', 'look', 'locate', 'search for', 'look for']:
            # Set context to indicate we're waiting for a search term
            self.context.last_offer_type = 'search_term_request'
            return self._create_message(
                "What would you like to search for in your tasks?",
                'text'
            )

        user_tasks = self.data_manager.get_all_tasks()

        # Search in title and description
        matching_tasks = []
        for task in user_tasks:
            title = task.get('title', '').lower()
            description = task.get('description', '').lower()
            if search_term in title or search_term in description:
                matching_tasks.append(task)

        if not matching_tasks:
            return self._create_message(
                f"No tasks found containing '{search_term}'. Try a different search term.",
                'text'
            )

        task_list = []
        for task in matching_tasks:
            task_list.append(f"• {task['title']} ({task['est_time']} min) - {task.get('date', 'No date')}")

        response_text = f"🔍 Found {len(matching_tasks)} tasks containing '{search_term}':\n\n" + "\n".join(task_list)

        return self._create_message(
            response_text,
            'task_list',
            {'tasks': matching_tasks, 'search_term': search_term}
        )

    def _handle_export_tasks(self) -> ChatMessage:
        """Handle task export requests"""
        return self._create_message(
            "📁 To export your tasks:\n\n1. Go to the main Tasks page\n2. Click the 'Export CSV' button\n3. Your tasks will be downloaded as a CSV file\n\nThis feature allows you to backup your data or import it into other tools like Excel.",
            'text'
        )

    def _handle_time_tracking(self, extracted_info: Dict, original_message: str) -> ChatMessage:
        """Handle time tracking requests"""
        user_tasks = self.data_manager.get_all_tasks()
        if not user_tasks:
            return self._create_message(
                "You don't have any tasks to analyze time for yet.",
                'text'
            )

        message_lower = original_message.lower()

        if 'today' in message_lower:
            today = date.today().strftime('%Y-%m-%d')
            today_tasks = [task for task in user_tasks if task.get('date') == today]
            total_time = sum(task.get('est_time', 0) for task in today_tasks)
            return self._create_message(
                f"⏱️ Today's estimated time: {total_time} minutes ({total_time // 60}h {total_time % 60}m) across {len(today_tasks)} tasks.",
                'analytics'
            )
        elif 'yesterday' in message_lower:
            yesterday = (date.today() - timedelta(days=1)).strftime('%Y-%m-%d')
            yesterday_tasks = [task for task in user_tasks if task.get('date') == yesterday]
            total_time = sum(task.get('est_time', 0) for task in yesterday_tasks)
            return self._create_message(
                f"⏱️ Yesterday's estimated time: {total_time} minutes ({total_time // 60}h {total_time % 60}m) across {len(yesterday_tasks)} tasks.",
                'analytics'
            )
        elif 'this week' in message_lower:
            today = date.today()
            start_week = today - timedelta(days=today.weekday())
            week_tasks = []
            for task in user_tasks:
                task_date = datetime.strptime(task.get('date', '1900-01-01'), '%Y-%m-%d').date()
                if task_date >= start_week:
                    week_tasks.append(task)
            total_time = sum(task.get('est_time', 0) for task in week_tasks)
            return self._create_message(
                f"⏱️ This week's estimated time: {total_time} minutes ({total_time // 60}h {total_time % 60}m) across {len(week_tasks)} tasks.",
                'analytics'
            )

        # Total time across all tasks
        total_time = sum(task.get('est_time', 0) for task in user_tasks)
        return self._create_message(
            f"⏱️ Total estimated time across all tasks: {total_time} minutes ({total_time // 60}h {total_time % 60}m) for {len(user_tasks)} tasks.",
            'analytics'
        )

    def _handle_help_request(self, extracted_info: Dict) -> ChatMessage:
        """Handle help and instruction requests"""
        help_text = """🤖 **AdBot Help Guide**

I can help you with comprehensive task management! Here's what I can do:

**📝 Creating Tasks:**
• "Create a task for reviewing quarterly reports"
• "Generate 3 tasks for project setup"
• "Break down 'implement authentication' into subtasks"

**📋 Viewing Tasks:**
• "Show me today's tasks" / "Show yesterday's tasks"
• "List my completed tasks" / "Show pending tasks"
• "Show me planning tasks" / "List execution tasks"
• "Show tasks for this week" / "Tasks from last week"

**🔍 Finding Tasks:**
• "Search for tasks about budget"
• "Find tasks containing 'meeting'"

**📊 Analytics & Insights:**
• "Show my productivity stats"
• "How much time did I spend today?"
• "What should I work on next?"
• "Optimize my schedule"

**💡 Smart Features:**
• "Suggest tasks based on my patterns"
• "Help me prioritize my tasks"
• "Export my tasks"

**🎯 Examples:**
• "Show me all my Business Support Activities tasks"
• "What tasks did I have yesterday?"
• "Create 5 tasks for Q1 planning"
• "How productive have I been this week?"

Just ask in natural language - I understand context and can help with almost anything related to your tasks!"""

        return self._create_message(help_text, 'text')

    def _create_message(self, content: str, message_type: str, metadata: Optional[Dict] = None) -> ChatMessage:
        """Create a chat message object"""
        return ChatMessage(
            id=f"msg_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(content) % 10000}",
            content=content,
            sender='assistant',
            timestamp=datetime.now(),
            message_type=message_type,
            metadata=metadata or {}
        )

    def _update_context(self, intent: str, extracted_info: Dict, user_message: str, response: ChatMessage):
        """Update conversation context with enhanced tracking"""
        # Use the new enhanced context tracking
        self.context.add_interaction(user_message, response, intent)

        # Legacy support for existing session_history
        self.context.last_topic = intent

        if not self.context.session_history:
            self.context.session_history = []

        self.context.session_history.append(intent)

        # Keep only last 10 interactions for context
        if len(self.context.session_history) > 10:
            self.context.session_history = self.context.session_history[-10:]

        # Only clear offer type if user explicitly chose a different conversation path
        # Don't clear it if the bot just made an offer during this turn
        if not extracted_info.get('is_follow_up'):
            # Check if the response contains an offer - if so, don't clear the offer type
            response_content = response.content.lower()
            offer_indicators = ['would you like', 'want me to help', 'how about', 'shall i']
            has_offer = any(indicator in response_content for indicator in offer_indicators)

            if not has_offer and intent not in ['follow_up_accept_task_creation', 'follow_up_accept_planning', 'follow_up_accept_suggestions', 'follow_up_decline_offer']:
                # Only clear if it's a new conversation thread and no offer was made
                self.context.last_offer_type = None

    def get_conversation_suggestions(self) -> List[str]:
        """Get conversation starter suggestions"""
        return [
            "Create a task for weekly status meeting prep",
            "Show me my tasks for today",
            "Generate 4 tasks for system maintenance",
            "What should I prioritize this week?",
            "Break down 'quarterly review preparation' into steps",
            "Create a task for updating project documentation",
            "How much time did I spend on planning tasks?",
            "Show me completed tasks from last week",
            "Create a task for team collaboration session",
            "Help me organize tomorrow's schedule"
        ]
