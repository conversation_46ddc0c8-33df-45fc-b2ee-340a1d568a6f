/* Advanced Features Enhancements */

/* Action Card Styles */
.card .card-header h5 small {
    font-size: 0.75rem;
    opacity: 0.7;
}

/* Advanced Action Buttons */
.btn-outline-primary:hover,
.btn-outline-success:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

/* Custom Template Modal Enhancements */
.modal-lg {
    max-width: 800px;
}

.modal-xl {
    max-width: 1200px;
}

#customTemplateForm .form-control:focus,
#customTemplateForm .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.1);
}

/* Bulk Entry Table Enhancements */
#bulkTasksTable {
    font-size: 0.9rem;
}

#bulkTasksTable th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 10;
}

#bulkTasksTable .form-control,
#bulkTasksTable .form-select {
    font-size: 0.85rem;
    padding: 0.375rem 0.5rem;
    border: 1px solid #ced4da;
}

#bulkTasksTable .form-control:focus,
#bulkTasksTable .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.1rem rgba(13, 110, 253, 0.1);
}

.table-responsive {
    max-height: 400px;
    overflow-y: auto;
}

/* Bulk task row animations */
.bulk-task-row {
    transition: all 0.3s ease;
}

.bulk-task-row.removing {
    opacity: 0;
    transform: translateX(-20px);
}

.bulk-task-row.adding {
    opacity: 0;
    animation: slideInFromLeft 0.3s ease forwards;
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Row action buttons */
.btn-sm.btn-outline-danger {
    padding: 0.2rem 0.4rem;
    font-size: 0.75rem;
    line-height: 1;
}

.btn-sm.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
}

/* Bulk task count indicator */
#bulkTaskCount {
    font-weight: 500;
    color: #6c757d;
}

#bulkTaskCount.has-tasks {
    color: #198754;
    font-weight: 600;
}

/* Template success indicators */
.template-success {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Advanced action card hover effects */
.card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: box-shadow 0.2s ease;
}

/* Modal backdrop enhancement */
.modal-backdrop {
    background-color: rgba(0,0,0,0.6);
}

/* Form validation for bulk entry */
.bulk-task-row .form-control.is-invalid,
.bulk-task-row .form-select.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6 5.8 6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Loading states */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 0.375rem;
}

.spinner-custom {
    width: 2rem;
    height: 2rem;
    border: 0.25em solid #f3f3f3;
    border-top: 0.25em solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Template management section */
.template-item {
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
    background-color: #fff;
}

.template-item:hover {
    border-color: #0d6efd;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.template-item.selected {
    border-color: #0d6efd;
    background-color: #f8f9ff;
}

/* Template quick actions */
.template-actions {
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.template-item:hover .template-actions {
    opacity: 1;
}

/* Custom template list */
.custom-template-list {
    max-height: 300px;
    overflow-y: auto;
}

.custom-template-list:empty::after {
    content: "No custom templates created yet.";
    display: block;
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 2rem;
}

/* Responsive enhancements for mobile */
@media (max-width: 768px) {
    .modal-xl,
    .modal-lg {
        max-width: 95%;
        margin: 1rem auto;
    }

    #bulkTasksTable {
        font-size: 0.8rem;
    }

    #bulkTasksTable .form-control,
    #bulkTasksTable .form-select {
        font-size: 0.8rem;
        padding: 0.25rem 0.4rem;
    }

    .table-responsive {
        max-height: 300px;
    }

    .btn-outline-primary,
    .btn-outline-success {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }

    .template-item {
        padding: 0.75rem;
    }
}
