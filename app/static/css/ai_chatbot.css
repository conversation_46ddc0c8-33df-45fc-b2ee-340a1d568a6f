/* AI Chatbot Styles for AdhocLog */

/* Floating <PERSON><PERSON> */
.chatbot-floating-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1050;
    transition: all 0.3s ease;
}

.chatbot-floating-button:hover {
    transform: scale(1.1);
}

.chatbot-floating-button .btn {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Main Chat Container */
.ai-chatbot-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 380px;
    height: 580px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid #e9ecef;
    z-index: 1060;
    display: flex;
    flex-direction: column;
    transform: translateY(100vh);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
}

.ai-chatbot-container.open {
    transform: translateY(0);
    opacity: 1;
}

/* Header */
.chatbot-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    padding: 16px 20px;
    border-radius: 16px 16px 0 0;
    display: flex;
    justify-content: between;
    align-items: center;
    min-height: 60px;
    box-shadow: 0 2px 20px rgba(0, 123, 255, 0.3);
}

.chatbot-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 1.1rem;
    flex: 1;
}

.chatbot-title i {
    margin-right: 8px;
    font-size: 1.2rem;
}

.chatbot-controls {
    display: flex;
    gap: 8px;
}

.chatbot-controls .btn {
    width: 32px;
    height: 32px;
    padding: 0;
    border: 1px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}

.chatbot-controls .btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Messages Container */
.chatbot-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    background: #f8f9fa;
}

.chatbot-messages::-webkit-scrollbar {
    width: 6px;
}

.chatbot-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chatbot-messages::-webkit-scrollbar-thumb {
    background: #dee2e6;
    border-radius: 3px;
}

.chatbot-messages::-webkit-scrollbar-thumb:hover {
    background: #ced4da;
}

/* Messages */
.message {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    animation: fadeInUp 0.3s ease;
}

.user-message {
    align-items: flex-end;
}

.assistant-message {
    align-items: flex-start;
}

.message-content {
    max-width: 85%;
    padding: 12px 16px;
    border-radius: 18px;
    font-size: 0.9rem;
    line-height: 1.4;
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    white-space: pre-wrap;
    position: relative;
    display: flex;
    align-items: flex-start;
    text-align: left;
    gap: 0;
}

.user-message .message-content {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-bottom-right-radius: 6px;
    box-shadow: 0 3px 12px rgba(0, 123, 255, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: left;
    justify-content: flex-start;
}

.assistant-message .message-content {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    color: #333;
    border: 1px solid #e9ecef;
    border-bottom-left-radius: 6px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
    position: relative;
    text-align: left;
    justify-content: flex-start;
}

.message-content i {
    margin-top: 2px;
    flex-shrink: 0;
    align-self: flex-start;
}

.message-text {
    flex: 1;
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
    white-space: pre-wrap;
    min-width: 0;
    margin: 0;
    padding: 0;
}

.user-message .message-text {
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
    white-space: pre-wrap;
    min-width: 0;
    text-align: left;
    flex: 1;
    margin: 0;
    padding: 0;
}

.assistant-message .message-text {
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
    white-space: pre-wrap;
    min-width: 0;
    text-align: left;
    flex: 1;
    margin: 0;
    padding: 0;
}

/* Special message types */
.message-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: white !important;
    border: none !important;
}

.message-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
    color: white !important;
    border: none !important;
}

.message-suggestion {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
    color: #333 !important;
    border: none !important;
}

.message-analytics {
    background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%) !important;
    color: white !important;
    border: none !important;
}

.message-out-of-scope {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
    color: #856404 !important;
    border: 1px solid #ffeaa7 !important;
}

.message-time-summary {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%) !important;
    color: #0c5460 !important;
    border: 1px solid #bee5eb !important;
}

.message-structured-fallback {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    color: #495057 !important;
    border: 1px solid #dee2e6 !important;
}

.message-timestamp {
    margin-top: 4px;
    font-size: 0.75rem;
}

.user-message .message-timestamp {
    text-align: right;
}

.assistant-message .message-timestamp {
    text-align: left;
}

/* Message Actions */
.message-actions {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
    margin-top: 12px;
    padding-top: 8px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.message-actions .btn {
    font-size: 0.7rem;
    padding: 4px 8px;
    border-radius: 12px;
    line-height: 1.2;
    font-weight: 500;
    white-space: nowrap;
    min-height: auto;
    color: #333 !important;
    border: 1px solid #dee2e6 !important;
    background: rgba(255, 255, 255, 0.95) !important;
}

.message-actions .btn i {
    font-size: 0.7rem;
    margin-right: 4px;
}

.message-actions .btn-xs {
    font-size: 0.65rem;
    padding: 3px 6px;
    border-radius: 10px;
    line-height: 1.1;
    color: #333 !important;
    border: 1px solid #dee2e6 !important;
    background: rgba(255, 255, 255, 0.95) !important;
}

.message-actions .btn-xs i {
    font-size: 0.65rem;
    margin-right: 3px;
}

.message-actions .btn:hover {
    color: white !important;
    background: #007bff !important;
    border-color: #007bff !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

/* Welcome Message */
.welcome-message {
    margin-bottom: 24px;
}

.welcome-message .message-content {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    border: 1px solid #e9ecef !important;
    color: #333 !important;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08) !important;
    border-bottom-left-radius: 6px !important;
}

/* Conversation Suggestions */
.conversation-suggestions {
    margin-top: 16px;
}

.suggestions-wrapper {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    padding: 16px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.suggestions-title {
    margin-bottom: 12px;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.suggestions-title::before {
    content: '💡';
    font-size: 1em;
}

.suggestions-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.suggestion-btn {
    text-align: left;
    padding: 10px 14px;
    border-radius: 12px;
    font-size: 0.8rem;
    border: 1px solid #dee2e6;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    color: #495057;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    line-height: 1.3;
}

.suggestion-btn:hover {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
}

/* Suggestion buttons within messages */
.suggestion-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 10px;
    padding-top: 8px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.suggestion-buttons .suggestion-btn {
    font-size: 0.7rem;
    padding: 4px 10px;
    border-radius: 14px;
    white-space: nowrap;
    border: 1px solid #dee2e6;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    color: #495057;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    letter-spacing: 0.2px;
    line-height: 1.2;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: auto;
}

.suggestion-buttons .suggestion-btn:hover {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0, 123, 255, 0.2);
}

.suggestion-buttons .suggestion-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0, 123, 255, 0.1);
}

.suggestion-buttons .btn-outline-primary {
    border-color: #007bff;
    color: #007bff;
    background: transparent;
}

.suggestion-buttons .btn-outline-primary:hover {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0, 123, 255, 0.2);
}

.suggestion-buttons .btn-outline-success {
    border-color: #28a745;
    color: #28a745;
    background: transparent;
}

.suggestion-buttons .btn-outline-success:hover {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-color: #28a745;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(40, 167, 69, 0.2);
}

/* Input Area */
.chatbot-input {
    padding: 18px 20px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 0 0 16px 16px;
    border-top: 1px solid #e9ecef;
}

.chatbot-input .input-group {
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #dee2e6;
}

.chatbot-input .form-control {
    border: none;
    padding: 12px 18px;
    font-size: 0.9rem;
    background: transparent;
    color: #2d3748;
    font-weight: 500;
}

.chatbot-input .form-control:focus {
    background: transparent;
    box-shadow: none;
    border: none;
    color: #007bff;
}

.chatbot-input .form-control::placeholder {
    color: #8b949e;
    font-weight: 400;
}

.chatbot-input .btn {
    border: none;
    padding: 12px 18px;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
}

.chatbot-input .btn:disabled {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    opacity: 0.6;
}

.chatbot-input .btn:not(:disabled):hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: scale(1.02);
    box-shadow: 0 3px 12px rgba(0, 123, 255, 0.3);
}

/* Typing Indicator */
.chatbot-typing {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 12px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.typing-indicator {
    display: flex;
    gap: 4px;
}

.typing-indicator span {
    width: 6px;
    height: 6px;
    background: #6c757d;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
    animation-delay: -0.16s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Enhanced message animations */
.user-message {
    animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.assistant-message {
    animation: slideInLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Floating button enhancement */
.chatbot-floating-button .btn:hover {
    animation: pulse 2s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ai-chatbot-container {
        width: calc(100vw - 20px);
        right: 10px;
        left: 10px;
        height: calc(100vh - 40px);
        bottom: 10px;
    }

    .chatbot-floating-button {
        bottom: 15px;
        right: 15px;
    }

    .chatbot-floating-button .btn {
        width: 56px;
        height: 56px;
        font-size: 1.3rem;
    }
}

@media (max-width: 480px) {
    .ai-chatbot-container {
        width: 100vw;
        height: 100vh;
        right: 0;
        left: 0;
        bottom: 0;
        border-radius: 0;
    }

    .chatbot-header {
        border-radius: 0;
    }

    .chatbot-input {
        border-radius: 0;
    }

    .suggestion-btn {
        font-size: 0.7rem;
        padding: 6px 10px;
    }

    .suggestion-buttons .suggestion-btn {
        font-size: 0.7rem;
        padding: 5px 10px;
        max-width: 160px;
    }

    .suggestions-list {
        gap: 6px;
    }

    .suggestion-buttons {
        gap: 6px;
    }
}

/* Additional responsive breakpoint for very small containers */
@media (max-width: 380px) {
    .suggestion-buttons .suggestion-btn {
        font-size: 0.65rem;
        padding: 4px 8px;
        max-width: 140px;
        flex: 1;
        min-width: 0;
    }

    .suggestion-buttons {
        gap: 4px;
    }
}

/* Integration with existing theme */
.ai-chatbot-container {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark mode support (if implemented) */
@media (prefers-color-scheme: dark) {
    .ai-chatbot-container {
        background: #2d3748;
        border-color: #4a5568;
    }

    .chatbot-messages {
        background: #2d3748;
    }

    .assistant-message .message-content {
        background: #4a5568;
        color: #e2e8f0;
        border-color: #718096;
    }

    .chatbot-input {
        background: #2d3748;
        border-color: #4a5568;
    }

    .chatbot-input .form-control {
        background: #4a5568;
        color: #e2e8f0;
        font-weight: 500;
    }

    .chatbot-input .form-control:focus {
        background: #2d3748;
        color: #90cdf4;
    }

    .chatbot-input .form-control::placeholder {
        color: #a0aec0;
        font-weight: 400;
    }

    .suggestions-wrapper {
        background: #4a5568;
        border-color: #718096;
    }

    .suggestion-btn {
        background: #2d3748;
        border-color: #718096;
        color: #e2e8f0;
    }
}
