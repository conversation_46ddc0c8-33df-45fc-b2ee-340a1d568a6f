/**
 * Task Form Enhancement JavaScript - Phase 3
 * Enhanced with AI-powered suggestions, advanced analytics, and optimization features
 * Handles templates, auto-complete, custom templates, bulk entry, AI analysis, and improved UX
 */

class TaskFormManager {
    constructor() {
        this.templates = this.getDefaultTemplates();
        this.customTemplates = [];
        this.favorites = this.loadFavorites();
        this.bulkMode = false;
        this.bulkTasks = [];
        this.editingTemplateId = null; // For template editing
        this.templateToDelete = null; // For template deletion

        // Phase 3: Analytics features
        this.analyticsTracker = new AnalyticsTracker();
        this.suggestionInteractions = new Map(); // Track suggestion response times

        this.init();
    }

    async init() {
        this.setupTemplateButtons();
        this.setupFormAutoFill();
        this.setupKeyboardShortcuts();
        this.setupCustomTemplateModal();
        await this.loadCustomTemplates();
        this.loadRecentTasks();

        // Initialize AI features
        this.setupAIEnhancements();
        this.setupAnalyticsTracking();
    }

    // Default template definitions
    getDefaultTemplates() {
        return [
            {
                id: 'daily-standup',
                name: 'Daily Standup',
                icon: 'bi-people',
                data: {
                    title: 'Daily Stand-up',
                    classification: 'Business Support Activities',
                    description: 'Daily team standup meeting - updates, blockers, and plans',
                    est_time: 15
                }
            },
            {
                id: 'team-meeting',
                name: 'Team Meeting',
                icon: 'bi-calendar-event',
                data: {
                    title: 'Team Meeting',
                    classification: 'Business Support Activities',
                    description: 'Team meeting to discuss project progress and alignment',
                    est_time: 30
                }
            },
            {
                id: 'report-generation',
                name: 'Report Generation',
                icon: 'bi-file-text',
                data: {
                    title: 'Generate Report',
                    classification: 'Offline Processing',
                    description: 'Create and compile reports for stakeholders',
                    est_time: 60
                }
            },
            {
                id: 'planning-session',
                name: 'Planning Session',
                icon: 'bi-diagram-3',
                data: {
                    title: 'Planning Session',
                    classification: 'Planning',
                    description: 'Planning and strategy session for upcoming work',
                    est_time: 45
                }
            },
            {
                id: 'townhall-meeting',
                name: 'Townhall Meeting',
                icon: 'bi-buildings',
                data: {
                    title: 'Townhall Meeting',
                    classification: 'Business Support Activities',
                    description: 'Attending company townhall and organizational activities',
                    est_time: 60
                }
            }
        ];
    }

    // Setup template buttons
    setupTemplateButtons() {
        const container = document.getElementById('template-buttons-container');
        if (!container) return;

        container.innerHTML = '';

        this.templates.forEach(template => {
            const button = this.createTemplateButton(template);
            container.appendChild(button);
        });
    }

    createTemplateButton(template) {
        const button = document.createElement('button');
        button.type = 'button';
        button.className = 'btn btn-outline-primary btn-sm template-btn mb-2';
        button.setAttribute('data-template-id', template.id);

        // Determine badge based on template type
        const badgeHtml = template.isCustom
            ? '<span class="badge bg-info text-light ms-2" style="font-size: 0.65em;">Custom</span>'
            : '<span class="badge bg-primary text-light ms-2" style="font-size: 0.65em;">Default</span>';

        button.innerHTML = `
            <div class="d-flex align-items-center justify-content-between w-100">
                <div class="d-flex align-items-center flex-grow-1 me-2" style="min-width: 0;">
                    <i class="${template.icon} me-2 flex-shrink-0"></i>
                    <span class="fw-medium text-truncate">${template.name}</span>
                    ${badgeHtml}
                </div>
                <small class="text-muted flex-shrink-0">${template.data.est_time}min</small>
            </div>
        `;

        button.addEventListener('click', () => {
            this.applyTemplate(template);
        });

        return button;
    }

    // Apply template to form
    applyTemplate(template) {
        const form = document.querySelector('form[method="POST"]');
        if (!form) return;

        // Add visual feedback
        this.showTemplateAppliedFeedback(template.name);

        // Animate form fields as they're filled
        Object.entries(template.data).forEach(([field, value], index) => {
            setTimeout(() => {
                this.animateFieldUpdate(field, value);
            }, index * 100);
        });

        // Auto-update category based on classification
        setTimeout(() => {
            this.updateCategoryFromClassification();
        }, 300);
    }

    animateFieldUpdate(fieldName, value) {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (!field) return;

        // Add highlight animation
        field.style.transition = 'all 0.3s ease';
        field.style.backgroundColor = '#e3f2fd';
        field.style.transform = 'scale(1.02)';

        // Set value
        field.value = value;

        // Trigger change event for any listeners
        field.dispatchEvent(new Event('change', { bubbles: true }));

        // Remove animation after delay
        setTimeout(() => {
            field.style.backgroundColor = '';
            field.style.transform = '';
        }, 600);
    }

    showTemplateAppliedFeedback(templateName) {
        // Create and show toast notification
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-bg-success border-0 position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060;';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="bi bi-check-circle me-2"></i>
                    Applied "${templateName}" template
                </div>
            </div>
        `;

        document.body.appendChild(toast);

        // Auto remove after 3 seconds
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    // Apply custom template to form
    applyCustomTemplate(template) {
        const form = document.querySelector('form[method="POST"]');
        if (!form) return;

        // Add visual feedback
        this.showTemplateAppliedFeedback(template.name);

        // Apply template data
        const templateData = {
            title: template.title,
            classification: template.classification,
            description: template.description,
            est_time: template.est_time,
            category: template.category || ''
        };

        // Animate form fields as they're filled
        Object.entries(templateData).forEach(([field, value], index) => {
            setTimeout(() => {
                this.animateFieldUpdate(field, value);
            }, index * 100);
        });

        // Auto-update category based on classification
        setTimeout(() => {
            this.updateCategoryFromClassification();
        }, 300);

        // Increment usage count
        this.incrementTemplateUsage(template.id);
    }

    async incrementTemplateUsage(templateId) {
        try {
            await fetch(`/api/templates/${templateId}/use`, {
                method: 'POST'
            });
        } catch (error) {
            console.error('Error incrementing template usage:', error);
        }
    }

    // Utility method for showing notifications
    showNotification(message, type = 'info', duration = 5000) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px; max-width: 400px;';

        const iconMap = {
            'success': 'bi-check-circle',
            'error': 'bi-exclamation-triangle',
            'warning': 'bi-exclamation-triangle',
            'info': 'bi-info-circle'
        };

        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="${iconMap[type] || iconMap.info} me-2"></i>
                <div class="flex-grow-1">${message}</div>
                <button type="button" class="btn-close" aria-label="Close"></button>
            </div>
        `;

        // Add click handler for close button
        const closeBtn = notification.querySelector('.btn-close');
        closeBtn.addEventListener('click', () => {
            notification.remove();
        });

        // Add to DOM
        document.body.appendChild(notification);

        // Auto-remove after duration
        setTimeout(() => {
            if (notification.parentNode) {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 150);
            }
        }, duration);
    }

    // Category auto-update
    updateCategoryFromClassification() {
        const classificationSelect = document.querySelector('[name="classification"]');
        const categoryField = document.querySelector('[name="category"]');

        if (!classificationSelect || !categoryField) return;

        const classification = classificationSelect.value;

        // Category mapping (should match backend config)
        const categoryMap = {
            'Planning': 'Adhoc',
            'Offline Processing': 'Adhoc',
            'Execution': 'Adhoc',
            'Business Support Activities': 'Business Support Activities',
            'Operational Project Involvement': 'Adhoc'
        };

        const category = categoryMap[classification] || '';
        categoryField.value = category;
    }

    // Enhanced recent tasks loading
    loadRecentTasks() {
        fetch('/api/recent-tasks?limit=10&with_frequency=true')
            .then(response => response.json())
            .then(data => {
                this.displayEnhancedRecentTasks(data.tasks || []);
            })
            .catch(error => {
                console.error('Error loading recent tasks:', error);
                this.displayFallbackRecentTasks();
            });
    }

    displayEnhancedRecentTasks(tasks) {
        const container = document.getElementById('quickAddContainer');
        if (!container) return;

        if (tasks.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="bi bi-inbox fs-1 text-muted"></i>
                    <p class="text-muted mt-2 mb-0">No recent tasks found.</p>
                    <p class="text-muted">Use templates above to get started!</p>
                </div>
            `;
            return;
        }

        let html = '<div class="list-group list-group-flush">';

        tasks.forEach((task, index) => {
            const isFavorite = this.favorites.includes(task.id);
            const shortTitle = task.title.length > 50 ? task.title.substring(0, 50) + '...' : task.title;
            const shortDesc = task.description.length > 80 ? task.description.substring(0, 80) + '...' : task.description;

            html += `
                <div class="list-group-item list-group-item-action py-2 recent-task-item"
                     style="cursor: pointer; border-left: 3px solid ${isFavorite ? '#ffc107' : '#0d6efd'};"
                     data-task='${encodeURIComponent(JSON.stringify(task))}'>
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-1">
                                <h6 class="mb-0 me-2" style="font-size: 0.9rem; font-weight: 600;">${shortTitle}</h6>
                                ${task.usage_count ? `<span class="badge bg-light text-dark badge-sm">${task.usage_count}x</span>` : ''}
                            </div>
                            <p class="mb-1 text-muted" style="font-size: 0.8rem;">${shortDesc}</p>
                            <div class="d-flex gap-1 align-items-center">
                                <span class="badge bg-secondary" style="font-size: 0.7rem;">${task.classification}</span>
                                <span class="badge bg-info" style="font-size: 0.7rem;">${task.category}</span>
                            </div>
                        </div>
                        <div class="text-end ms-2">
                            <div class="d-flex flex-column align-items-end">
                                <button class="btn btn-link btn-sm p-0 favorite-btn ${isFavorite ? 'text-warning' : 'text-muted'}"
                                        data-task-id="${task.id}" title="${isFavorite ? 'Remove from favorites' : 'Add to favorites'}">
                                    <i class="bi ${isFavorite ? 'bi-star-fill' : 'bi-star'}"></i>
                                </button>
                                <small class="text-muted">${task.est_time} min</small>
                                <i class="bi bi-arrow-right-circle text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div>';
        container.innerHTML = html;

        // Setup event listeners
        this.setupRecentTasksEventListeners();
    }

    setupRecentTasksEventListeners() {
        // Recent task click to fill form
        document.querySelectorAll('.recent-task-item').forEach(item => {
            item.addEventListener('click', (e) => {
                // Don't trigger if clicking on favorite button
                if (e.target.closest('.favorite-btn')) return;

                const taskData = JSON.parse(decodeURIComponent(item.dataset.task));
                this.applyTemplate({ name: taskData.title, data: taskData });
            });
        });

        // Favorite button clicks
        document.querySelectorAll('.favorite-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const taskId = parseInt(btn.dataset.taskId);
                this.toggleFavorite(taskId);
            });
        });
    }

    // Favorites management
    loadFavorites() {
        try {
            return JSON.parse(localStorage.getItem('taskFormFavorites') || '[]');
        } catch {
            return [];
        }
    }

    saveFavorites() {
        localStorage.setItem('taskFormFavorites', JSON.stringify(this.favorites));
    }

    toggleFavorite(taskId) {
        const index = this.favorites.indexOf(taskId);
        if (index === -1) {
            this.favorites.push(taskId);
        } else {
            this.favorites.splice(index, 1);
        }
        this.saveFavorites();
        this.loadRecentTasks(); // Refresh display
    }

    // Fallback for when API isn't available
    displayFallbackRecentTasks() {
        // Use the existing implementation as fallback
        const existingScript = document.querySelector('script:not([src])');
        if (existingScript && existingScript.textContent.includes('loadRecentTasks')) {
            // Execute the existing loadRecentTasks function
            if (window.loadRecentTasks) {
                window.loadRecentTasks();
            }
        }
    }

    // Advanced Keyboard shortcuts (Phase 2)
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K for command palette
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                this.openCommandPalette();
                return;
            }

            // Ctrl/Cmd + Shift + N for quick task creation modal
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'N') {
                e.preventDefault();
                this.openQuickTaskModal();
                return;
            }

            // Ctrl/Cmd + number keys for template shortcuts (now supports 1-9)
            if ((e.ctrlKey || e.metaKey) && e.key >= '1' && e.key <= '9') {
                e.preventDefault();
                const templateIndex = parseInt(e.key) - 1;

                // First try default templates
                if (templateIndex < this.templates.length) {
                    this.applyTemplate(this.templates[templateIndex]);
                    return;
                }

                // Then try custom templates
                const customIndex = templateIndex - this.templates.length;
                if (customIndex >= 0 && customIndex < this.customTemplates.length) {
                    this.applyCustomTemplate(this.customTemplates[customIndex]);
                    return;
                }
            }

            // Ctrl/Cmd + Enter to submit form
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                const form = document.querySelector('form[method="POST"]');
                if (form && this.validateForm()) {
                    form.submit();
                }
                return;
            }

            // Ctrl/Cmd + Shift + C for enhanced smart suggestions
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'C') {
                e.preventDefault();
                // Trigger enhanced smart suggestions refresh
                const titleField = document.querySelector('[name="title"]');
                if (titleField && titleField.value.length >= 2) {
                    this.setupPatternRecognition();
                }
                this.showNotification('Enhanced smart suggestions refreshed', 'info', 2000);
                return;
            }

            // Escape to close any open modals or suggestions
            if (e.key === 'Escape') {
                this.closeAllSuggestions();
                this.closeCommandPalette();
                return;
            }

            // Tab navigation enhancement
            if (e.key === 'Tab' && !e.shiftKey) {
                this.enhanceTabNavigation(e);
            }
        });

        // Setup keyboard shortcuts help
        this.setupKeyboardShortcutsHelp();
    }

    enhanceTabNavigation(e) {
        // Ensure logical tab order through form fields
        const formFields = document.querySelectorAll('input, select, textarea, button');
        const visibleFields = Array.from(formFields).filter(field =>
            field.offsetParent !== null && !field.disabled && field.tabIndex >= 0
        );

        const currentIndex = visibleFields.indexOf(document.activeElement);
        if (currentIndex >= 0 && currentIndex < visibleFields.length - 1) {
            // Standard tab behavior is fine
            return;
        }

        // If we're at the last field, focus the submit button
        if (currentIndex === visibleFields.length - 1) {
            const submitButton = document.querySelector('button[type="submit"]');
            if (submitButton) {
                e.preventDefault();
                submitButton.focus();
            }
        }
    }

    openCommandPalette() {
        // Create command palette modal if it doesn't exist
        if (!document.getElementById('commandPaletteModal')) {
            this.createCommandPaletteModal();
        }

        const modal = new bootstrap.Modal(document.getElementById('commandPaletteModal'));
        modal.show();

        // Focus on search input
        setTimeout(() => {
            const searchInput = document.getElementById('commandPaletteSearch');
            if (searchInput) {
                searchInput.focus();
                searchInput.value = '';
                this.updateCommandPaletteResults('');
            }
        }, 100);
    }

    closeCommandPalette() {
        const modal = document.getElementById('commandPaletteModal');
        if (modal) {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
        }
    }

    createCommandPaletteModal() {
        const modalHtml = `
            <div class="modal fade" id="commandPaletteModal" tabindex="-1" aria-labelledby="commandPaletteLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header border-0 pb-0">
                            <h5 class="modal-title" id="commandPaletteLabel">
                                <i class="bi bi-command"></i> Command Palette
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body pt-2">
                            <div class="mb-3">
                                <input type="text" class="form-control form-control-lg"
                                       id="commandPaletteSearch"
                                       placeholder="Type a command or search for actions..."
                                       autocomplete="off">
                            </div>
                            <div id="commandPaletteResults" class="list-group list-group-flush">
                                <!-- Results will be populated here -->
                            </div>
                        </div>
                        <div class="modal-footer border-0 pt-0">
                            <small class="text-muted">
                                Use <kbd>↑</kbd> <kbd>↓</kbd> to navigate, <kbd>Enter</kbd> to execute, <kbd>Esc</kbd> to close
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Setup command palette event handlers
        const searchInput = document.getElementById('commandPaletteSearch');
        const resultsContainer = document.getElementById('commandPaletteResults');
        this.commandPaletteSelectedIndex = 0; // Store as instance variable

        searchInput.addEventListener('input', (e) => {
            this.updateCommandPaletteResults(e.target.value);
        });

        searchInput.addEventListener('keydown', (e) => {
            const results = resultsContainer.querySelectorAll('.command-item');

            if (e.key === 'ArrowDown') {
                e.preventDefault();
                this.commandPaletteSelectedIndex = Math.min(this.commandPaletteSelectedIndex + 1, results.length - 1);
                this.updateCommandPaletteSelection(this.commandPaletteSelectedIndex);
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                this.commandPaletteSelectedIndex = Math.max(this.commandPaletteSelectedIndex - 1, 0);
                this.updateCommandPaletteSelection(this.commandPaletteSelectedIndex);
            } else if (e.key === 'Enter') {
                e.preventDefault();
                const selectedResult = results[this.commandPaletteSelectedIndex];
                if (selectedResult) {
                    selectedResult.click();
                }
            } else if (e.key === 'Escape') {
                this.closeCommandPalette();
            }
        });
    }

    updateCommandPaletteResults(query) {
        const resultsContainer = document.getElementById('commandPaletteResults');
        const commands = this.getAvailableCommands();

        const filteredCommands = commands.filter(cmd =>
            cmd.name.toLowerCase().includes(query.toLowerCase()) ||
            cmd.description.toLowerCase().includes(query.toLowerCase()) ||
            cmd.keywords.some(keyword => keyword.toLowerCase().includes(query.toLowerCase()))
        );

        let html = '';
        filteredCommands.forEach((cmd, index) => {
            html += `
                <div class="list-group-item list-group-item-action command-item ${index === 0 ? 'active' : ''}"
                     data-command="${cmd.id}">
                    <div class="d-flex align-items-center">
                        <i class="${cmd.icon} me-3"></i>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${cmd.name}</h6>
                            <small class="text-muted">${cmd.description}</small>
                        </div>
                        ${cmd.shortcut ? `<small class="text-muted ms-2"><kbd>${cmd.shortcut}</kbd></small>` : ''}
                    </div>
                </div>
            `;
        });

        if (html === '') {
            html = '<div class="text-center text-muted py-3">No commands found</div>';
        }

        resultsContainer.innerHTML = html;

        // Reset selectedIndex to 0 and update selection
        this.commandPaletteSelectedIndex = 0;
        this.updateCommandPaletteSelection(0);

        // Add click event listeners to command items
        resultsContainer.querySelectorAll('.command-item[data-command]').forEach(item => {
            item.addEventListener('click', () => {
                const commandId = item.dataset.command;
                this.executeCommand(commandId);
            });
        });
    }

    updateCommandPaletteSelection(index) {
        const results = document.querySelectorAll('.command-item');
        results.forEach((result, i) => {
            result.classList.toggle('active', i === index);
        });
    }

    getAvailableCommands() {
        const commands = [
            {
                id: 'apply_template_1',
                name: 'Apply Daily Standup Template',
                description: 'Apply the Daily Standup template to the current form',
                icon: 'bi-people',
                shortcut: 'Ctrl+1',
                keywords: ['template', 'standup', 'daily', 'meeting']
            },
            {
                id: 'apply_template_2',
                name: 'Apply Team Meeting Template',
                description: 'Apply the Team Meeting template to the current form',
                icon: 'bi-calendar-event',
                shortcut: 'Ctrl+2',
                keywords: ['template', 'meeting', 'team']
            },
            {
                id: 'apply_template_3',
                name: 'Apply Report Generation Template',
                description: 'Apply the Report Generation template to the current form',
                icon: 'bi-file-text',
                shortcut: 'Ctrl+3',
                keywords: ['template', 'report', 'generation']
            },
            {
                id: 'apply_template_4',
                name: 'Apply Planning Session Template',
                description: 'Apply the Planning Session template to the current form',
                icon: 'bi-diagram-3',
                shortcut: 'Ctrl+4',
                keywords: ['template', 'planning', 'session']
            },
            {
                id: 'apply_template_5',
                name: 'Apply Townhall Meeting Template',
                description: 'Apply the Townhall Meeting template to the current form',
                icon: 'bi-buildings',
                shortcut: 'Ctrl+5',
                keywords: ['template', 'townhall', 'meeting']
            },
            {
                id: 'open_bulk_entry',
                name: 'Open Bulk Entry',
                description: 'Open the bulk task entry interface',
                icon: 'bi-stack',
                shortcut: '',
                keywords: ['bulk', 'multiple', 'batch', 'entry']
            },
            {
                id: 'create_custom_template',
                name: 'Create Custom Template',
                description: 'Create a new custom template from current form',
                icon: 'bi-plus-square',
                shortcut: '',
                keywords: ['template', 'custom', 'create', 'save']
            },
            {
                id: 'manage_templates',
                name: 'Manage Templates',
                description: 'Open template management interface',
                icon: 'bi-gear',
                shortcut: '',
                keywords: ['template', 'manage', 'edit', 'delete']
            },
            {
                id: 'refresh_enhanced_suggestions',
                name: 'Refresh Enhanced Smart Suggestions',
                description: 'Reload smart suggestions based on current input and patterns',
                icon: 'bi-arrow-clockwise',
                shortcut: 'Ctrl+Shift+C',
                keywords: ['suggestions', 'refresh', 'smart', 'enhanced']
            },
            {
                id: 'clear_form',
                name: 'Clear Form',
                description: 'Clear all form fields and start fresh',
                icon: 'bi-eraser',
                shortcut: '',
                keywords: ['clear', 'reset', 'form', 'empty']
            },
            {
                id: 'submit_form',
                name: 'Submit Task',
                description: 'Submit the current task form',
                icon: 'bi-check-circle',
                shortcut: 'Ctrl+Enter',
                keywords: ['submit', 'save', 'create', 'task']
            },
            {
                id: 'show_shortcuts',
                name: 'Show Keyboard Shortcuts',
                description: 'Display all available keyboard shortcuts',
                icon: 'bi-keyboard',
                shortcut: '',
                keywords: ['keyboard', 'shortcuts', 'help', 'commands']
            }
        ];

        // Add custom templates to commands
        this.customTemplates.forEach((template, index) => {
            commands.push({
                id: `apply_custom_template_${template.id}`,
                name: `Apply ${template.name} Template`,
                description: template.description || `Apply the ${template.name} custom template`,
                icon: 'bi-bookmark',
                shortcut: index < 4 ? `Ctrl+${6 + index}` : '',
                keywords: ['template', 'custom', template.name.toLowerCase()]
            });
        });

        return commands;
    }

    executeCommand(commandId) {
        this.closeCommandPalette();

        switch (commandId) {
            case 'apply_template_1':
                this.applyTemplate(this.templates[0]);
                break;
            case 'apply_template_2':
                this.applyTemplate(this.templates[1]);
                break;
            case 'apply_template_3':
                this.applyTemplate(this.templates[2]);
                break;
            case 'apply_template_4':
                this.applyTemplate(this.templates[3]);
                break;
            case 'apply_template_5':
                this.applyTemplate(this.templates[4]);
                break;
            case 'open_bulk_entry':
                const bulkModal = new bootstrap.Modal(document.getElementById('bulkEntryModal'));
                bulkModal.show();
                break;
            case 'create_custom_template':
                const createModal = new bootstrap.Modal(document.getElementById('customTemplateModal'));
                createModal.show();
                this.populateCustomTemplateModal();
                break;
            case 'manage_templates':
                const manageModal = new bootstrap.Modal(document.getElementById('manageTemplatesModal'));
                manageModal.show();
                this.loadTemplatesForManagement();
                break;
            case 'refresh_enhanced_suggestions':
                // Trigger enhanced smart suggestions refresh
                const titleField = document.querySelector('[name="title"]');
                if (titleField && titleField.value.length >= 2) {
                    this.setupPatternRecognition();
                }
                this.showNotification('Enhanced smart suggestions refreshed', 'info', 2000);
                break;
            case 'clear_form':
                this.clearForm();
                break;
            case 'submit_form':
                const form = document.querySelector('form[method="POST"]');
                if (form && this.validateForm()) {
                    form.submit();
                }
                break;
            case 'show_shortcuts':
                this.showKeyboardShortcutsHelp();
                break;
            default:
                // Handle custom template commands
                if (commandId.startsWith('apply_custom_template_')) {
                    const templateId = commandId.replace('apply_custom_template_', '');
                    const template = this.customTemplates.find(t => t.id === templateId);
                    if (template) {
                        this.applyCustomTemplate(template);
                    }
                }
                break;
        }
    }

    openQuickTaskModal() {
        // Create quick task modal if it doesn't exist
        if (!document.getElementById('quickTaskModal')) {
            this.createQuickTaskModal();
        }

        const modal = new bootstrap.Modal(document.getElementById('quickTaskModal'));
        modal.show();

        // Focus on title input
        setTimeout(() => {
            const titleInput = document.getElementById('quickTaskTitle');
            if (titleInput) {
                titleInput.focus();
            }
        }, 100);
    }

    createQuickTaskModal() {
        const modalHtml = `
            <div class="modal fade" id="quickTaskModal" tabindex="-1" aria-labelledby="quickTaskLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="quickTaskLabel">
                                <i class="bi bi-lightning"></i> Quick Task Entry
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="quickTaskForm">
                                <div class="mb-3">
                                    <label for="quickTaskTitle" class="form-label">Title</label>
                                    <input type="text" class="form-control" id="quickTaskTitle"
                                           placeholder="Enter task title..." required>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="quickTaskClassification" class="form-label">Classification</label>
                                        <select class="form-select" id="quickTaskClassification">
                                            <option value="">Select classification...</option>
                                            <option value="Business Support Activities">Business Support Activities</option>
                                            <option value="Offline Processing">Offline Processing</option>
                                            <option value="Planning">Planning</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="quickTaskTime" class="form-label">Est. Time (min)</label>
                                        <input type="number" class="form-control" id="quickTaskTime"
                                               value="30" min="5" max="480">
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="submitQuickTaskBtn">
                                <i class="bi bi-plus-circle"></i> Create Task
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Setup quick task form handlers
        const titleInput = document.getElementById('quickTaskTitle');
        const classificationSelect = document.getElementById('quickTaskClassification');
        const submitBtn = document.getElementById('submitQuickTaskBtn');

        // Add submit button event listener
        submitBtn.addEventListener('click', () => {
            this.submitQuickTask();
        });

        // Auto-suggest classification and time based on title
        titleInput.addEventListener('input', async () => {
            const title = titleInput.value;
            if (title.length > 3) {
                // Get classification suggestion
                try {
                    const response = await fetch('/api/suggestions/classification', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ title })
                    });

                    if (response.ok) {
                        const data = await response.json();
                        if (data.success && data.prediction.confidence > 0.6) {
                            classificationSelect.value = data.prediction.classification;
                        }
                    }
                } catch (error) {
                    console.error('Error getting quick task suggestions:', error);
                }
            }
        });

        // Submit on Enter
        titleInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.submitQuickTask();
            }
        });
    }

    submitQuickTask() {
        const titleInput = document.getElementById('quickTaskTitle');
        const classificationSelect = document.getElementById('quickTaskClassification');
        const timeInput = document.getElementById('quickTaskTime');

        if (!titleInput.value.trim()) {
            titleInput.focus();
            return;
        }

        // Apply to main form
        const mainTitleField = document.querySelector('[name="title"]');
        const mainClassificationField = document.querySelector('[name="classification"]');
        const mainTimeField = document.querySelector('[name="est_time"]');
        const mainDateField = document.querySelector('[name="date"]');

        if (mainTitleField) mainTitleField.value = titleInput.value;
        if (mainClassificationField) {
            mainClassificationField.value = classificationSelect.value;
            mainClassificationField.dispatchEvent(new Event('change', { bubbles: true }));
        }
        if (mainTimeField) mainTimeField.value = timeInput.value;
        if (mainDateField && !mainDateField.value) {
            mainDateField.value = new Date().toISOString().split('T')[0];
        }

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('quickTaskModal'));
        modal.hide();

        // Show notification
        this.showNotification('Quick task details applied to form', 'success', 3000);

        // Focus on description field in main form
        setTimeout(() => {
            const descField = document.querySelector('[name="description"]');
            if (descField) {
                descField.focus();
            }
        }, 500);
    }

    clearForm() {
        const form = document.querySelector('form[method="POST"]');
        if (form) {
            // Clear all input fields except date (keep today's date)
            const inputs = form.querySelectorAll('input:not([name="date"]), textarea, select');
            inputs.forEach(input => {
                if (input.type === 'checkbox' || input.type === 'radio') {
                    input.checked = false;
                } else {
                    input.value = '';
                }
            });

            // Clear any suggestions
            this.closeAllSuggestions();

            // Focus on title field
            const titleField = document.querySelector('[name="title"]');
            if (titleField) {
                titleField.focus();
            }

            this.showNotification('Form cleared', 'info', 2000);
        }
    }

    validateForm() {
        const titleField = document.querySelector('[name="title"]');
        const classificationField = document.querySelector('[name="classification"]');

        if (!titleField || !titleField.value.trim()) {
            titleField?.focus();
            this.showNotification('Please enter a task title', 'warning', 3000);
            return false;
        }

        if (!classificationField || !classificationField.value) {
            classificationField?.focus();
            this.showNotification('Please select a classification', 'warning', 3000);
            return false;
        }

        return true;
    }

    closeAllSuggestions() {
        const suggestions = document.querySelectorAll('.classification-suggestion, .duration-suggestion');
        suggestions.forEach(suggestion => suggestion.remove());
    }

    setupKeyboardShortcutsHelp() {
        // Add keyboard shortcuts help button if it doesn't exist
        const helpButton = document.getElementById('keyboardHelpButton');
        if (!helpButton) {
            // Find a good place to add the help button
            const cardHeader = document.querySelector('.card-header');
            if (cardHeader) {
                const button = document.createElement('button');
                button.type = 'button';
                button.className = 'btn btn-outline-info btn-sm ms-2';
                button.id = 'keyboardHelpButton';
                button.innerHTML = '<i class="bi bi-keyboard"></i> Shortcuts';
                button.title = 'Show keyboard shortcuts';
                button.onclick = () => this.showKeyboardShortcutsHelp();
                cardHeader.appendChild(button);
            }
        }
    }

    showKeyboardShortcutsHelp() {
        // Create shortcuts help modal if it doesn't exist
        if (!document.getElementById('keyboardShortcutsModal')) {
            this.createKeyboardShortcutsModal();
        }

        const modal = new bootstrap.Modal(document.getElementById('keyboardShortcutsModal'));
        modal.show();
    }

    createKeyboardShortcutsModal() {
        const modalHtml = `
            <div class="modal fade" id="keyboardShortcutsModal" tabindex="-1" aria-labelledby="keyboardShortcutsLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="keyboardShortcutsLabel">
                                <i class="bi bi-keyboard"></i> Keyboard Shortcuts
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Templates</h6>
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr><td><kbd>Ctrl+1</kbd></td><td>Daily Standup template</td></tr>
                                            <tr><td><kbd>Ctrl+2</kbd></td><td>Team Meeting template</td></tr>
                                            <tr><td><kbd>Ctrl+3</kbd></td><td>Report Generation template</td></tr>
                                            <tr><td><kbd>Ctrl+4</kbd></td><td>Planning Session template</td></tr>
                                            <tr><td><kbd>Ctrl+5</kbd></td><td>Townhall Meeting template</td></tr>
                                            <tr><td><kbd>Ctrl+6-9</kbd></td><td>Custom templates (if available)</td></tr>
                                        </tbody>
                                    </table>

                                    <h6>Form Actions</h6>
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr><td><kbd>Ctrl+Enter</kbd></td><td>Submit form</td></tr>
                                            <tr><td><kbd>Tab</kbd></td><td>Navigate between fields</td></tr>
                                            <tr><td><kbd>Esc</kbd></td><td>Close suggestions/modals</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6>Power Features</h6>
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr><td><kbd>Ctrl+K</kbd></td><td>Open command palette</td></tr>
                                            <tr><td><kbd>Ctrl+Shift+N</kbd></td><td>Quick task creation</td></tr>
                                            <tr><td><kbd>Ctrl+Shift+C</kbd></td><td>Refresh enhanced smart suggestions</td></tr>
                                        </tbody>
                                    </table>

                                    <h6>Command Palette Navigation</h6>
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr><td><kbd>↑ ↓</kbd></td><td>Navigate commands</td></tr>
                                            <tr><td><kbd>Enter</kbd></td><td>Execute command</td></tr>
                                            <tr><td><kbd>Esc</kbd></td><td>Close palette</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    // Auto-fill form functionality
    setupFormAutoFill() {
        const classificationSelect = document.querySelector('[name="classification"]');
        if (classificationSelect) {
            classificationSelect.addEventListener('change', () => {
                this.updateCategoryFromClassification();
            });
        }

        // Set default date to today
        const dateField = document.querySelector('[name="date"]');
        if (dateField && !dateField.value) {
            dateField.value = new Date().toISOString().split('T')[0];
        }

        // Setup pattern recognition for smart suggestions
        this.setupPatternRecognition();
    }

    // Pattern Recognition & Learning functionality (Phase 2)
    setupPatternRecognition() {
        const titleField = document.querySelector('[name="title"]');
        const descriptionField = document.querySelector('[name="description"]');
        const classificationSelect = document.querySelector('[name="classification"]');
        const estTimeField = document.querySelector('[name="est_time"]');

        let suggestionTimeout;
        let titleSuggestionTimeout;

        // Real-time title suggestions
        const showTitleSuggestions = async () => {
            const title = titleField?.value || '';

            if (title.length < 2) {
                this.hideTitleSuggestions();
                return;
            }

            try {
                const response = await fetch('/api/suggestions/title', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ title, limit: 5 })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.suggestions.length > 0) {
                        this.displayTitleSuggestions(data.suggestions);
                    } else {
                        this.hideTitleSuggestions();
                    }
                }
            } catch (error) {
                console.error('Error getting title suggestions:', error);
            }
        };

        // Smart classification suggestion based on title and description
        const suggestClassification = async () => {
            const title = titleField?.value || '';
            const description = descriptionField?.value || '';

            if (title.length < 3) return; // Wait for meaningful input

            try {
                const response = await fetch('/api/suggestions/classification', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ title, description })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.prediction.confidence > 0.5) {
                        this.showClassificationSuggestion(data.prediction);
                    }
                }
            } catch (error) {
                console.error('Error getting classification suggestion:', error);
            }
        };

        // Smart duration estimation
        const suggestDuration = async () => {
            const title = titleField?.value || '';
            const classification = classificationSelect?.value || '';
            const description = descriptionField?.value || '';

            if (title.length < 3) return;

            try {
                const response = await fetch('/api/suggestions/duration', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ title, classification, description })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.estimation.confidence > 0.4) {
                        this.showDurationSuggestion(data.estimation);
                    }
                }
            } catch (error) {
                console.error('Error getting duration suggestion:', error);
            }
        };

        // Enhanced real-time contextual suggestions
        const updateSmartSuggestions = async () => {
            const title = titleField?.value || '';
            const classification = classificationSelect?.value || '';

            if (title.length >= 2) {
                try {
                    // Get both contextual and title-based suggestions
                    const [contextualResponse, titleResponse] = await Promise.all([
                        fetch('/api/suggestions/contextual'),
                        fetch('/api/suggestions/title', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ title, limit: 3 })
                        })
                    ]);

                    const contextualData = await contextualResponse.json();
                    const titleData = await titleResponse.json();

                    const combinedSuggestions = {
                        contextual: contextualData.success ? contextualData.suggestions : {},
                        title_based: titleData.success ? titleData.suggestions : []
                    };

                    // Debug logging
                    // Combine local and AI suggestions

                    this.displayEnhancedSmartSuggestions(combinedSuggestions);
                } catch (error) {
                    console.error('Error updating smart suggestions:', error);
                }
            }
        };

        // Event listeners for real-time suggestions
        if (titleField) {
            titleField.addEventListener('input', () => {
                // Title suggestions with shorter delay
                clearTimeout(titleSuggestionTimeout);
                titleSuggestionTimeout = setTimeout(showTitleSuggestions, 300);

                // Other suggestions with longer delay
                clearTimeout(suggestionTimeout);
                suggestionTimeout = setTimeout(() => {
                    suggestClassification();
                    suggestDuration();
                    updateSmartSuggestions();
                }, 1000);
            });

            // Hide title suggestions when field loses focus
            titleField.addEventListener('blur', () => {
                setTimeout(() => this.hideTitleSuggestions(), 200);
            });
        }

        if (descriptionField) {
            descriptionField.addEventListener('input', () => {
                clearTimeout(suggestionTimeout);
                suggestionTimeout = setTimeout(() => {
                    suggestClassification();
                    suggestDuration();
                }, 1500); // Longer delay for description
            });
        }

        if (classificationSelect) {
            classificationSelect.addEventListener('change', () => {
                suggestDuration(); // Update duration when classification changes
                updateSmartSuggestions(); // Update enhanced smart suggestions
            });
        }

        // Initialize enhanced smart suggestions on load
        updateSmartSuggestions();
    }

    // Phase 3: AI Enhancement Setup
    setupAIEnhancements() {
        // AI functionality is now handled by ai_analysis.js
        // AI enhancements are now delegated to AIAnalysisManager
    }

    setupAnalyticsTracking() {
        // Track form start when user first interacts
        const form = document.querySelector('#task-form, form');
        if (form) {
            let hasStarted = false;

            form.addEventListener('focusin', (e) => {
                if (!hasStarted) {
                    this.analyticsTracker.trackFormStart();
                    hasStarted = true;
                }

                // Track field interactions
                if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'SELECT') {
                    this.analyticsTracker.trackFieldInteraction(e.target.name || e.target.id, 'focus');
                }
            });

            form.addEventListener('focusout', (e) => {
                if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'SELECT') {
                    this.analyticsTracker.trackFieldInteraction(e.target.name || e.target.id, 'blur');
                }
            });

            form.addEventListener('change', (e) => {
                if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'SELECT') {
                    this.analyticsTracker.trackFieldInteraction(e.target.name || e.target.id, 'change', e.target.value);
                }
            });

            // Track form submission
            form.addEventListener('submit', () => {
                const fieldsUsed = Array.from(form.elements)
                    .filter(el => el.name && el.value.trim())
                    .map(el => el.name);

                this.analyticsTracker.trackFormCompletion(fieldsUsed);
            });
        }
    }

    createAIToggleButtonManually() {
        // Fallback method to create AI toggle button
        try {
            const cardHeader = document.querySelector('.card-header');
            if (cardHeader && !document.getElementById('aiToggleMainBtn')) {
                const toggleBtn = document.createElement('button');
                toggleBtn.type = 'button';
                toggleBtn.id = 'aiToggleMainBtn';
                toggleBtn.className = `btn btn-sm ${this.aiAnalysisEnabled ? 'btn-primary' : 'btn-outline-secondary'} ms-2`;
                toggleBtn.innerHTML = `<i class="bi bi-robot"></i> AI Analysis ${this.aiAnalysisEnabled ? 'ON' : 'OFF'}`;
                toggleBtn.title = `${this.aiAnalysisEnabled ? 'Disable' : 'Enable'} AI Analysis`;

                // Use arrow function to maintain 'this' context
                toggleBtn.addEventListener('click', () => {
                    try {
                        this.toggleAIAnalysis();
                        // Update button appearance
                        toggleBtn.className = `btn btn-sm ${this.aiAnalysisEnabled ? 'btn-primary' : 'btn-outline-secondary'} ms-2`;
                        toggleBtn.innerHTML = `<i class="bi bi-robot"></i> AI Analysis ${this.aiAnalysisEnabled ? 'ON' : 'OFF'}`;
                        toggleBtn.title = `${this.aiAnalysisEnabled ? 'Disable' : 'Enable'} AI Analysis`;
                    } catch (error) {
                        console.error('Error toggling AI analysis:', error);
                    }
                });

                cardHeader.appendChild(toggleBtn);
            }
        } catch (error) {
            console.error('Error creating AI toggle button manually:', error);
        }
    }

    createAIInsightsContainer() {
        // Find a good place to insert AI insights
        const taskDetailsSection = document.querySelector('.card-body');
        if (taskDetailsSection) {
            const insightsContainer = document.createElement('div');
            insightsContainer.id = 'ai-insights-container';
            insightsContainer.className = 'mb-3';

            // Insert after the Quick Add from Previous Tasks section
            const quickAddSection = document.querySelector('#quick-add-section');
            if (quickAddSection && quickAddSection.nextSibling) {
                quickAddSection.parentNode.insertBefore(insightsContainer, quickAddSection.nextSibling);
            } else {
                taskDetailsSection.insertBefore(insightsContainer, taskDetailsSection.firstChild);
            }
        }
    }

    setupRealTimeAIAnalysis() {
        const titleField = document.querySelector('[name="title"]');
        const descriptionField = document.querySelector('[name="description"]');

        let analysisTimeout;

        const performAIAnalysis = async () => {
            // Check if AI analysis is enabled
            if (!this.aiAnalysisEnabled) {
                const container = document.querySelector('#ai-insights-container');
                if (container) container.innerHTML = '';
                return;
            }

            const title = titleField?.value || '';
            const description = descriptionField?.value || '';

            if (title.length < 3) {
                // Clear insights if title is too short
                const container = document.querySelector('#ai-insights-container');
                if (container) container.innerHTML = '';
                return;
            }

            try {
                const analysis = await this.aiEngine.analyzeTask(title, description);
                if (analysis) {
                    const container = document.querySelector('#ai-insights-container');
                    if (container) {
                        this.displayAIInsights(analysis, container);

                        // Apply AI suggestions automatically if confidence is high
                        this.applyHighConfidenceAISuggestions(analysis);
                    }
                }
            } catch (error) {
                console.error('Error performing AI analysis:', error);
            }
        };

        // Store the function as a class method for external access
        this.performAIAnalysis = performAIAnalysis;

        // Setup event listeners for AI analysis
        if (titleField) {
            titleField.addEventListener('input', () => {
                clearTimeout(analysisTimeout);
                analysisTimeout = setTimeout(performAIAnalysis, 1500); // Longer delay for AI analysis
            });
        }

        if (descriptionField) {
            descriptionField.addEventListener('input', () => {
                clearTimeout(analysisTimeout);
                analysisTimeout = setTimeout(performAIAnalysis, 2000); // Even longer delay when description changes
            });
        }
    }

    setupNextTaskPredictions() {
        // Create next task predictions widget
        this.createNextTaskPredictionsWidget();
    }

    async createNextTaskPredictionsWidget() {
        try {
            const predictions = await this.aiEngine.getNextTaskPredictions(3);

            if (predictions && predictions.length > 0) {
                const widget = document.createElement('div');
                widget.className = 'card border-info mb-3';
                widget.innerHTML = `
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="bi bi-magic"></i> AI Task Predictions
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="card-text mb-3">
                            <small class="text-muted">Based on your patterns, you might want to work on:</small>
                        </p>
                        <div class="row">
                            ${predictions.map((prediction, index) => `
                                <div class="col-md-4 mb-2">
                                    <div class="border rounded p-2 h-100 prediction-card"
                                         data-prediction="${encodeURIComponent(JSON.stringify(prediction))}"
                                         style="cursor: pointer;">
                                        <div class="fw-bold small">${prediction.title}</div>
                                        <div class="text-muted small">${prediction.classification}</div>
                                        <div class="text-muted small">${prediction.est_time} min</div>
                                        <div class="text-info small">${prediction.reason}</div>
                                        <div class="text-end">
                                            <span class="badge bg-success">${Math.round(prediction.confidence * 100)}%</span>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;

                // Add click handlers for predictions
                widget.addEventListener('click', (e) => {
                    const predictionCard = e.target.closest('.prediction-card');
                    if (predictionCard) {
                        try {
                            const prediction = JSON.parse(decodeURIComponent(predictionCard.dataset.prediction));
                            this.applyPredictionToForm(prediction);

                            // Track analytics
                            this.analyticsTracker.trackEvent('ai_prediction_applied', {
                                prediction_type: prediction.type,
                                prediction_title: prediction.title,
                                confidence: prediction.confidence
                            });
                        } catch (error) {
                            console.error('Error parsing prediction data:', error);
                            // Fallback: extract visible text from the card
                            const title = predictionCard.querySelector('.fw-bold').textContent;
                            const classification = predictionCard.querySelector('.text-muted').textContent;
                            const fallbackPrediction = {
                                title: title,
                                classification: classification,
                                confidence: 0.5
                            };
                            this.applyPredictionToForm(fallbackPrediction);
                        }
                    }
                });

                // Insert into the page
                const container = document.querySelector('#ai-insights-container');
                if (container) {
                    container.appendChild(widget);
                }
            }
        } catch (error) {
            console.error('Error creating next task predictions widget:', error);
        }
    }

    applyHighConfidenceAISuggestions(analysis) {
        // Auto-apply suggestions with very high confidence
        const classificationSelect = document.querySelector('[name="classification"]');
        const estTimeField = document.querySelector('[name="est_time"]');

        // Apply classification if confidence > 80%
        if (analysis.classification && analysis.classification.confidence > 0.8 && classificationSelect) {
            if (!classificationSelect.value || classificationSelect.value === '') {
                classificationSelect.value = analysis.classification.classification;
                this.updateCategoryFromClassification();

                // Track analytics
                this.analyticsTracker.trackSuggestionShown('auto_classification', analysis.classification, analysis.classification.confidence);
                this.analyticsTracker.trackSuggestionInteraction('accepted', 'auto_classification', analysis.classification, 0);
            }
        }

        // Apply duration if confidence > 70% and field is empty
        if (analysis.duration && analysis.duration.confidence > 0.7 && estTimeField) {
            if (!estTimeField.value || estTimeField.value === '') {
                estTimeField.value = analysis.duration.duration;

                // Track analytics
                this.analyticsTracker.trackSuggestionShown('auto_duration', analysis.duration, analysis.duration.confidence);
                this.analyticsTracker.trackSuggestionInteraction('accepted', 'auto_duration', analysis.duration, 0);
            }
        }
    }

    applyPredictionToForm(prediction) {
        // Apply AI prediction to form fields
        const titleField = document.querySelector('[name="title"]');
        const classificationSelect = document.querySelector('[name="classification"]');
        const estTimeField = document.querySelector('[name="est_time"]');

        if (titleField) titleField.value = prediction.title;
        if (classificationSelect) classificationSelect.value = prediction.classification;
        if (estTimeField) estTimeField.value = prediction.est_time;

        this.updateCategoryFromClassification();

        // Trigger AI analysis for the new content
        const event = new Event('input', { bubbles: true });
        if (titleField) titleField.dispatchEvent(event);
    }

    displayTitleSuggestions(suggestions) {
        this.hideTitleSuggestions(); // Remove existing suggestions

        const titleField = document.querySelector('[name="title"]');
        if (!titleField || suggestions.length === 0) return;

        const dropdown = document.createElement('div');
        dropdown.className = 'title-suggestions-dropdown position-absolute bg-white border rounded shadow-sm';
        dropdown.style.cssText = `
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
        `;

        suggestions.forEach((suggestion, index) => {
            const item = document.createElement('div');
            item.className = 'suggestion-item p-2 border-bottom cursor-pointer';
            item.style.cssText = 'cursor: pointer;';

            item.innerHTML = `
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="fw-medium text-primary">${this.highlightMatch(suggestion.title, titleField.value)}</div>
                        <small class="text-muted">${suggestion.classification}</small>
                        ${suggestion.usage_count > 1 ? `<span class="badge bg-light text-dark ms-1">${suggestion.usage_count}x</span>` : ''}
                    </div>
                    <small class="text-muted">${suggestion.est_time}min</small>
                </div>
            `;

            item.addEventListener('click', () => {
                this.applyTitleSuggestion(suggestion);
            });

            item.addEventListener('mouseenter', () => {
                item.style.backgroundColor = '#f8f9fa';
            });

            item.addEventListener('mouseleave', () => {
                item.style.backgroundColor = '';
            });

            dropdown.appendChild(item);
        });

        const container = titleField.parentNode;
        container.style.position = 'relative';
        container.appendChild(dropdown);
    }

    hideTitleSuggestions() {
        const existing = document.querySelector('.title-suggestions-dropdown');
        if (existing) {
            existing.remove();
        }
    }

    highlightMatch(text, query) {
        if (!query) return text;
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<strong>$1</strong>');
    }

    applyTitleSuggestion(suggestion) {
        const titleField = document.querySelector('[name="title"]');
        const classificationField = document.querySelector('[name="classification"]');
        const descriptionField = document.querySelector('[name="description"]');
        const estTimeField = document.querySelector('[name="est_time"]');

        // Determine the title to use
        let titleToUse = suggestion.title;
        if (!titleToUse && suggestion.classification) {
            titleToUse = `${suggestion.classification} task`;
        }
        if (!titleToUse) {
            titleToUse = 'New task';
        }

        if (titleField) {
            titleField.value = titleToUse;
            titleField.dispatchEvent(new Event('change', { bubbles: true }));
        }

        // Only auto-fill other fields if they're empty
        if (classificationField && !classificationField.value && suggestion.classification) {
            classificationField.value = suggestion.classification;
            classificationField.dispatchEvent(new Event('change', { bubbles: true }));
        }

        if (descriptionField && !descriptionField.value && suggestion.description) {
            descriptionField.value = suggestion.description;
        }

        if (estTimeField && !estTimeField.value && suggestion.est_time) {
            estTimeField.value = suggestion.est_time;
        }

        this.hideTitleSuggestions();
        this.showNotification(`Applied suggestion: "${titleToUse}"`, 'success', 2000);
    }

    displayEnhancedSmartSuggestions(suggestions) {
        // Find or create enhanced smart suggestions container
        let container = document.querySelector('.enhanced-smart-suggestions');
        if (!container) {
            // Create container after the Quick Add card
            const quickAddCard = document.querySelector('#quickAddContainer').closest('.card');
            if (quickAddCard) {
                container = document.createElement('div');
                container.className = 'enhanced-smart-suggestions mt-3';
                quickAddCard.parentNode.insertBefore(container, quickAddCard.nextSibling);
            } else {
                return; // No place to add suggestions
            }
        }

        // Debug logging
        // Display enhanced smart suggestions

        let hasContent = false;
        let html = '';

        // Title-based suggestions
        if (suggestions.title_based && suggestions.title_based.length > 0) {
            hasContent = true;
            html += `
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-search"></i> Smart Suggestions</h6>
                        <small class="text-muted">Based on what you're typing</small>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <small class="text-primary fw-medium mb-2 d-block"><i class="bi bi-search"></i> Similar Tasks</small>
                            <div class="list-group list-group-flush">
            `;
            suggestions.title_based.slice(0, 3).forEach(task => {
                html += `
                    <div class="list-group-item list-group-item-action p-2 smart-suggestion-item"
                         style="cursor: pointer;"
                         data-suggestion='${encodeURIComponent(JSON.stringify(task))}'>
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="fw-medium">${task.title}</div>
                                <small class="text-muted">${task.classification}</small>
                            </div>
                            <div class="text-end">
                                <small class="text-muted">${task.est_time}min</small>
                                ${task.usage_count > 1 ? `<br><span class="badge bg-light text-dark">${task.usage_count}x</span>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div></div>';
        }

        // Contextual suggestions (time-based and pattern-based)
        if (suggestions.contextual) {
            if (!hasContent) {
                html += `
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-magic"></i> Smart Suggestions</h6>
                            <small class="text-muted">Based on your patterns</small>
                        </div>
                        <div class="card-body">
                `;
                hasContent = true;
            }

            if (suggestions.contextual.time_based && suggestions.contextual.time_based.length > 0) {
                html += `
                    <div class="mb-3">
                        <small class="text-info fw-medium mb-2 d-block"><i class="bi bi-clock"></i> Common at this time</small>
                        <div class="list-group list-group-flush">
                `;
                suggestions.contextual.time_based.slice(0, 2).forEach(task => {
                    html += `
                        <div class="list-group-item list-group-item-action p-2 smart-suggestion-item"
                             style="cursor: pointer;"
                             data-suggestion='${encodeURIComponent(JSON.stringify(task))}'>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="fw-medium">${task.title || task.classification || 'Untitled task'}</div>
                                <small class="text-muted">${task.est_time || 30}min</small>
                            </div>
                            <small class="text-muted">${task.description || ''}</small>
                        </div>
                    `;
                });
                html += '</div></div>';
            }

            if (suggestions.contextual.pattern_based && suggestions.contextual.pattern_based.length > 0) {
                html += `
                    <div class="mb-3">
                        <small class="text-success fw-medium mb-2 d-block"><i class="bi bi-graph-up"></i> Frequently Used</small>
                        <div class="list-group list-group-flush">
                `;
                suggestions.contextual.pattern_based.slice(0, 2).forEach(task => {
                    const usageText = task.usage_count ? ` (${task.usage_count}x used)` : '';
                    html += `
                        <div class="list-group-item list-group-item-action p-2 smart-suggestion-item"
                             style="cursor: pointer;"
                             data-suggestion='${encodeURIComponent(JSON.stringify(task))}'>
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="fw-medium">${task.title || 'Untitled task'}</div>
                                    <small class="text-muted">${task.classification || ''}${usageText}</small>
                                </div>
                                <small class="text-muted">${task.est_time || 30}min</small>
                            </div>
                        </div>
                    `;
                });
                html += '</div></div>';
            }

            if (suggestions.contextual.recent_similar && suggestions.contextual.recent_similar.length > 0) {
                html += `
                    <div class="mb-2">
                        <small class="text-warning fw-medium mb-2 d-block"><i class="bi bi-trending-up"></i> Trending This Week</small>
                        <div class="list-group list-group-flush">
                `;
                suggestions.contextual.recent_similar.slice(0, 2).forEach(task => {
                    const trendText = task.trend_count ? ` (${task.trend_count} times this week)` : '';
                    html += `
                        <div class="list-group-item list-group-item-action p-2 smart-suggestion-item"
                             style="cursor: pointer;"
                             data-suggestion='${encodeURIComponent(JSON.stringify(task))}'>
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="fw-medium">${task.title || 'Untitled task'}</div>
                                    <small class="text-muted">${task.classification || ''}${trendText}</small>
                                </div>
                                <small class="text-muted">${task.est_time || 30}min</small>
                            </div>
                        </div>
                    `;
                });
                html += '</div></div>';
            }
        }

        if (hasContent) {
            html += '</div></div>'; // Close card body and card
            container.innerHTML = html;

            // Add event listeners to suggestion items
            container.querySelectorAll('.smart-suggestion-item').forEach(item => {
                item.addEventListener('click', () => {
                    const suggestionData = JSON.parse(decodeURIComponent(item.dataset.suggestion));
                    this.applyTitleSuggestion(suggestionData);
                });
            });
        } else {
            container.innerHTML = ''; // Hide if no suggestions
        }
    }

    showClassificationSuggestion(prediction) {
        // Remove any existing suggestion
        const existingSuggestion = document.querySelector('.classification-suggestion');
        if (existingSuggestion) {
            existingSuggestion.remove();
        }

        const classificationSelect = document.querySelector('[name="classification"]');
        if (!classificationSelect || classificationSelect.value === prediction.classification) {
            return; // Already set or no field found
        }

        // Create suggestion element
        const suggestion = document.createElement('div');
        suggestion.className = 'classification-suggestion alert alert-info alert-dismissible fade show mt-2';
        suggestion.innerHTML = `
            <i class="bi bi-lightbulb"></i>
            <strong>Smart suggestion:</strong> Based on your title, this looks like a
            <strong>${prediction.classification}</strong> task
            <small>(${Math.round(prediction.confidence * 100)}% confidence)</small>
            <button type="button" class="btn btn-sm btn-outline-primary ms-2 apply-suggestion-btn"
                    data-field="classification" data-value="${prediction.classification}">
                Apply
            </button>
            <button type="button" class="btn-close" aria-label="Close"></button>
        `;

        classificationSelect.parentNode.appendChild(suggestion);

        // Add event listeners
        const applyBtn = suggestion.querySelector('.apply-suggestion-btn');
        applyBtn.addEventListener('click', () => {
            this.applySuggestion('classification', prediction.classification);
        });

        const closeBtn = suggestion.querySelector('.btn-close');
        closeBtn.addEventListener('click', () => {
            suggestion.remove();
        });

        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (suggestion.parentNode) {
                suggestion.remove();
            }
        }, 10000);
    }

    showDurationSuggestion(estimation) {
        // Remove any existing suggestion
        const existingSuggestion = document.querySelector('.duration-suggestion');
        if (existingSuggestion) {
            existingSuggestion.remove();
        }

        const estTimeField = document.querySelector('[name="est_time"]');
        if (!estTimeField || parseInt(estTimeField.value) === estimation.estimated_time) {
            return; // Already set or no field found
        }

        // Create suggestion element
        const suggestion = document.createElement('div');
        suggestion.className = 'duration-suggestion alert alert-info alert-dismissible fade show mt-2';
        suggestion.innerHTML = `
            <i class="bi bi-clock"></i>
            <strong>Duration suggestion:</strong> Based on similar tasks, this might take
            <strong>${estimation.estimated_time} minutes</strong>
            <small>(${estimation.based_on})</small>
            <button type="button" class="btn btn-sm btn-outline-primary ms-2 apply-suggestion-btn"
                    data-field="est_time" data-value="${estimation.estimated_time}">
                Apply
            </button>
            <button type="button" class="btn-close" aria-label="Close"></button>
        `;

        estTimeField.parentNode.appendChild(suggestion);

        // Add event listeners
        const applyBtn = suggestion.querySelector('.apply-suggestion-btn');
        applyBtn.addEventListener('click', () => {
            this.applySuggestion('est_time', estimation.estimated_time);
        });

        const closeBtn = suggestion.querySelector('.btn-close');
        closeBtn.addEventListener('click', () => {
            suggestion.remove();
        });

        // Auto-remove after 8 seconds
        setTimeout(() => {
            if (suggestion.parentNode) {
                suggestion.remove();
            }
        }, 8000);
    }

    applySuggestion(fieldName, value) {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field) {
            field.value = value;
            field.dispatchEvent(new Event('change', { bubbles: true }));

            // Remove the suggestion
            const suggestion = document.querySelector(`.${fieldName}-suggestion`);
            if (suggestion) {
                suggestion.remove();
            }

            // Show feedback
            this.showNotification(`Applied ${fieldName} suggestion: ${value}`, 'success', 3000);
        }
    }

    // Phase 2: Custom Template Management
    async loadCustomTemplates() {
        try {
            const response = await fetch('/api/templates');
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.customTemplates = data.templates;
                    this.updateTemplateButtons();
                }
            }
        } catch (error) {
            console.error('Error loading custom templates:', error);
        }
    }

    async createCustomTemplate(templateData) {
        try {
            const response = await fetch('/api/templates', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(templateData)
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.customTemplates.push(data.template);
                    this.updateTemplateButtons();
                    this.showNotification('Custom template created successfully!', 'success');
                    return true;
                }
            }
            return false;
        } catch (error) {
            console.error('Error creating custom template:', error);
            return false;
        }
    }

    async deleteCustomTemplate(templateId) {
        try {
            const response = await fetch(`/api/templates/${templateId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.customTemplates = this.customTemplates.filter(t => t.id !== templateId);
                    this.updateTemplateButtons();
                    this.showNotification('Template deleted successfully!', 'success');

                    // Refresh the manage templates modal if it's open
                    const manageModal = document.getElementById('manageTemplatesModal');
                    if (manageModal && manageModal.classList.contains('show')) {
                        this.loadTemplatesForManagement();
                    }

                    return true;
                }
            }
            return false;
        } catch (error) {
            console.error('Error deleting template:', error);
            return false;
        }
    }

    setupCustomTemplateModal() {
        // Set up form submission handler for custom template modal
        const saveButton = document.getElementById('saveCustomTemplate');
        if (saveButton) {
            saveButton.addEventListener('click', () => {
                this.handleCreateTemplate();
            });
        }

        // Set up the manage templates modal to load templates when opened
        const manageModal = document.getElementById('manageTemplatesModal');
        if (manageModal) {
            manageModal.addEventListener('shown.bs.modal', () => {
                this.loadTemplatesForManagement();
            });
        }

        // Set up delete confirmation modal
        const confirmDeleteButton = document.getElementById('confirmDeleteTemplate');
        if (confirmDeleteButton) {
            confirmDeleteButton.addEventListener('click', () => {
                if (this.templateToDelete) {
                    this.deleteCustomTemplate(this.templateToDelete);
                    this.templateToDelete = null;

                    // Close the delete modal
                    const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteTemplateModal'));
                    if (deleteModal) {
                        deleteModal.hide();
                    }
                }
            });
        }

        // Reset form when custom template modal is closed
        const customModal = document.getElementById('customTemplateModal');
        if (customModal) {
            customModal.addEventListener('hidden.bs.modal', () => {
                const form = document.getElementById('customTemplateForm');
                if (form) {
                    form.reset();
                }
                // Reset editing state
                this.editingTemplateId = null;
                document.getElementById('customTemplateModalLabel').innerHTML = '<i class="bi bi-bookmark-plus"></i> Create Custom Template';
                document.getElementById('saveCustomTemplate').innerHTML = '<i class="bi bi-check-lg"></i> Save Template';
            });
        }
    }

    async handleCreateTemplate() {
        const form = document.getElementById('customTemplateForm');
        if (!form) return;

        const formData = new FormData(form);

        const templateData = {
            name: formData.get('name'),
            title: formData.get('title'),
            classification: formData.get('classification'),
            description: formData.get('description'),
            est_time: parseInt(formData.get('est_time'))
        };

        // Validate required fields
        if (!templateData.name || !templateData.title || !templateData.classification || !templateData.description || !templateData.est_time) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        const success = await this.createCustomTemplate(templateData);
        if (success) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('customTemplateModal'));
            if (modal) {
                modal.hide();
            }
            form.reset(); // Clear the form
        } else {
            this.showNotification('Failed to create template', 'error');
        }
    }

    updateTemplateButtons() {
        // Find template container and update with both default and custom templates
        const templateContainer = document.getElementById('template-buttons-container');
        if (!templateContainer) return;

        // Clear existing content
        templateContainer.innerHTML = '';

        // Add default templates
        this.templates.forEach(template => {
            const button = this.createTemplateButton(template);
            templateContainer.appendChild(button);
        });

        // Add custom templates
        this.customTemplates.forEach(template => {
            const customTemplate = {
                id: `custom-${template.id}`,
                name: template.name,
                icon: 'bi-star',
                data: {
                    title: template.title,
                    classification: template.classification,
                    description: template.description,
                    est_time: template.est_time
                },
                isCustom: true,
                customId: template.id
            };

            const button = this.createTemplateButton(customTemplate);

            // Add delete option for custom templates
            const deleteBtn = document.createElement('button');
            deleteBtn.type = 'button';
            deleteBtn.className = 'btn btn-sm btn-outline-danger position-absolute top-0 end-0 translate-middle';
            deleteBtn.innerHTML = '<i class="bi bi-x"></i>';
            deleteBtn.onclick = (e) => {
                e.stopPropagation();
                this.deleteCustomTemplate(template.id);
            };
            deleteBtn.style.fontSize = '10px';
            deleteBtn.style.padding = '2px 4px';

            button.style.position = 'relative';
            button.appendChild(deleteBtn);
            templateContainer.appendChild(button);
        });
    }

    // Bulk Entry Methods (for Advanced Actions modal)
    addBulkTask(taskData = null) {
        const container = document.getElementById('bulk-tasks-container');
        if (!container) return;

        const taskIndex = this.bulkTasks.length;

        const defaultData = taskData || {
            title: '',
            classification: '',
            description: '',
            est_time: '',
            date: new Date().toISOString().split('T')[0]
        };

        this.bulkTasks.push(defaultData);

        const taskHTML = `
            <div class="bulk-task-item card mb-2" data-task-index="${taskIndex}">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            <input type="text" class="form-control form-control-sm" placeholder="Task Title"
                                   value="${defaultData.title}" onchange="taskFormManager.updateBulkTask(${taskIndex}, 'title', this.value)">
                        </div>
                        <div class="col-md-2">
                            <select class="form-control form-control-sm" onchange="taskFormManager.updateBulkTask(${taskIndex}, 'classification', this.value)">
                                <option value="">Classification</option>
                                <option value="Planning" ${defaultData.classification === 'Planning' ? 'selected' : ''}>Planning</option>
                                <option value="Offline Processing" ${defaultData.classification === 'Offline Processing' ? 'selected' : ''}>Offline Processing</option>
                                <option value="Execution" ${defaultData.classification === 'Execution' ? 'selected' : ''}>Execution</option>
                                <option value="Business Support Activities" ${defaultData.classification === 'Business Support Activities' ? 'selected' : ''}>Business Support Activities</option>
                                <option value="Operational Project Involvement" ${defaultData.classification === 'Operational Project Involvement' ? 'selected' : ''}>Operational Project Involvement</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="text" class="form-control form-control-sm" placeholder="Description"
                                   value="${defaultData.description}" onchange="taskFormManager.updateBulkTask(${taskIndex}, 'description', this.value)">
                        </div>
                        <div class="col-md-2">
                            <input type="number" class="form-control form-control-sm" placeholder="Minutes" min="1"
                                   value="${defaultData.est_time}" onchange="taskFormManager.updateBulkTask(${taskIndex}, 'est_time', this.value)">
                        </div>
                        <div class="col-md-2">
                            <input type="date" class="form-control form-control-sm"
                                   value="${defaultData.date}" onchange="taskFormManager.updateBulkTask(${taskIndex}, 'date', this.value)">
                        </div>
                        <div class="col-md-1">
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="taskFormManager.removeBulkTask(${taskIndex})">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', taskHTML);
    }

    updateBulkTask(index, field, value) {
        if (this.bulkTasks[index]) {
            this.bulkTasks[index][field] = value;
        }
    }

    removeBulkTask(index) {
        const taskElement = document.querySelector(`[data-task-index="${index}"]`);
        if (taskElement) {
            taskElement.remove();
            this.bulkTasks.splice(index, 1);
            this.updateBulkTaskIndices();
        }
    }

    updateBulkTaskIndices() {
        const taskElements = document.querySelectorAll('.bulk-task-item');
        taskElements.forEach((element, newIndex) => {
            element.setAttribute('data-task-index', newIndex);
            // Update all the onchange handlers
            const inputs = element.querySelectorAll('input, select');
            inputs.forEach(input => {
                const onchangeAttr = input.getAttribute('onchange');
                if (onchangeAttr) {
                    input.setAttribute('onchange', onchangeAttr.replace(/\d+/, newIndex));
                }
            });

            const deleteBtn = element.querySelector('.btn-outline-danger');
            if (deleteBtn) {
                deleteBtn.setAttribute('onclick', `taskFormManager.removeBulkTask(${newIndex})`);
            }
        });
    }

    async submitBulkTasks() {
        // Validate bulk tasks
        const validTasks = this.bulkTasks.filter(task =>
            task.title && task.classification && task.description && task.est_time
        );

        if (validTasks.length === 0) {
            this.showNotification('Please add at least one complete task', 'error');
            return;
        }

        try {
            const response = await fetch('/api/tasks/bulk', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ tasks: validTasks })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.showNotification(
                        `Successfully created ${data.created_count} tasks!${data.error_count > 0 ? ` (${data.error_count} errors)` : ''}`,
                        'success'
                    );

                    // Clear bulk tasks and close modal
                    this.bulkTasks = [];
                    const container = document.getElementById('bulk-tasks-container');
                    if (container) {
                        container.innerHTML = '';
                    }

                    const modal = bootstrap.Modal.getInstance(document.getElementById('bulkEntryModal'));
                    if (modal) {
                        modal.hide();
                    }

                    // Refresh the page to show new tasks
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    throw new Error(data.error || 'Failed to create tasks');
                }
            } else {
                throw new Error('Network error');
            }
        } catch (error) {
            console.error('Error creating bulk tasks:', error);
            this.showNotification('Failed to create tasks: ' + error.message, 'error');
        }
    }

    // Template Management Methods
    showManageTemplatesModal() {
        this.loadTemplatesForManagement();
    }

    async loadTemplatesForManagement() {
        const container = document.getElementById('templates-list-container');
        if (!container) return;

        try {
            const response = await fetch('/api/templates');
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.displayTemplatesForManagement(data.templates);
                } else {
                    container.innerHTML = '<p class="text-muted text-center">No custom templates found.</p>';
                }
            }
        } catch (error) {
            console.error('Error loading templates:', error);
            container.innerHTML = '<p class="text-danger text-center">Error loading templates.</p>';
        }
    }

    displayTemplatesForManagement(templates) {
        const container = document.getElementById('templates-list-container');
        if (!container) return;

        if (templates.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="bi bi-bookmark fs-1 text-muted"></i>
                    <p class="text-muted mt-2 mb-0">No custom templates yet.</p>
                    <p class="text-muted">Create your first template to get started!</p>
                </div>
            `;
            return;
        }

        let html = '<div class="list-group">';
        templates.forEach(template => {
            html += `
                <div class="list-group-item">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <h6 class="mb-0">${template.name}</h6>
                                <small class="text-muted">${template.est_time} min</small>
                            </div>
                            <p class="mb-1 text-muted">${template.title}</p>
                            <small class="text-muted">
                                <span class="badge bg-secondary me-1">${template.classification}</span>
                                ${template.description.length > 60 ? template.description.substring(0, 60) + '...' : template.description}
                            </small>
                        </div>
                        <div class="btn-group ms-3" role="group">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="taskFormManager.editTemplate('${template.id}')" title="Edit Template">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="taskFormManager.confirmDeleteTemplate('${template.id}', '${template.name}')" title="Delete Template">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';
        container.innerHTML = html;
    }

    editTemplate(templateId) {
        // Find the template
        const template = this.customTemplates.find(t => t.id === templateId);
        if (!template) {
            console.error('Template not found:', templateId);
            return;
        }

        // Store the template ID and data for updating
        this.editingTemplateId = templateId;
        this.editingTemplateData = template;

        // Close manage modal first
        const manageModal = bootstrap.Modal.getInstance(document.getElementById('manageTemplatesModal'));
        if (manageModal) {
            manageModal.hide();
        }

        // Wait for the manage modal to close, then open edit modal
        setTimeout(() => {
            // Change modal title and button text first
            document.getElementById('customTemplateModalLabel').innerHTML = '<i class="bi bi-pencil"></i> Edit Custom Template';
            document.getElementById('saveCustomTemplate').innerHTML = '<i class="bi bi-check-lg"></i> Update Template';

            // Open edit modal
            const editModal = new bootstrap.Modal(document.getElementById('customTemplateModal'));
            editModal.show();

            // The form will be populated by the modal's 'shown.bs.modal' event listener
        }, 300);
    }

    confirmDeleteTemplate(templateId, templateName) {
        // Set the template name in the modal
        document.getElementById('deleteTemplateName').textContent = templateName;

        // Store the template ID for deletion
        this.templateToDelete = templateId;

        // Show the delete confirmation modal
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteTemplateModal'));
        deleteModal.show();
    }

    async handleCreateTemplate() {
        const form = document.getElementById('customTemplateForm');
        if (!form) return;

        // Check form validity first
        if (!form.checkValidity()) {
            form.classList.add('was-validated');
            this.showNotification('Please fill in all required fields correctly', 'error');
            return;
        }

        const formData = new FormData(form);

        const templateData = {
            name: formData.get('name'),
            title: formData.get('title'),
            classification: formData.get('classification'),
            description: formData.get('description'),
            est_time: parseInt(formData.get('est_time'))
        };

        // Additional validation
        if (!templateData.name || !templateData.title || !templateData.classification || !templateData.description || !templateData.est_time) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        // Show loading state
        const saveBtn = document.getElementById('saveCustomTemplate');
        const originalText = saveBtn.innerHTML;
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Saving...';

        let success;
        try {
            if (this.editingTemplateId) {
                // Update existing template
                templateData.id = this.editingTemplateId;
                success = await this.updateCustomTemplate(templateData);
            } else {
                // Create new template
                success = await this.createCustomTemplate(templateData);
            }

            if (success) {
                const modal = bootstrap.Modal.getInstance(document.getElementById('customTemplateModal'));
                if (modal) {
                    modal.hide();
                }
                form.reset(); // Clear the form
                form.classList.remove('was-validated');

                // Reset editing state
                this.editingTemplateId = null;
                this.editingTemplateData = null;
                document.getElementById('customTemplateModalLabel').innerHTML = '<i class="bi bi-bookmark-plus"></i> Create Custom Template';
                document.getElementById('saveCustomTemplate').innerHTML = '<i class="bi bi-check-lg"></i> Save Template';

                // Reload templates to reflect changes
                await this.loadCustomTemplates();
            } else {
                this.showNotification(this.editingTemplateId ? 'Failed to update template' : 'Failed to create template', 'error');
            }
        } catch (error) {
            console.error('Error handling template:', error);
            this.showNotification('An error occurred while saving the template', 'error');
        } finally {
            // Reset button state
            saveBtn.disabled = false;
            saveBtn.innerHTML = originalText;
        }
    }

    async updateCustomTemplate(templateData) {
        try {
            const response = await fetch(`/api/templates/${templateData.id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(templateData)
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    // Update the template in our local array
                    const index = this.customTemplates.findIndex(t => t.id === templateData.id);
                    if (index !== -1) {
                        this.customTemplates[index] = { ...this.customTemplates[index], ...templateData };
                    }
                    this.updateTemplateButtons();
                    this.showNotification('Template updated successfully!', 'success');
                    return true;
                }
            }
            return false;
        } catch (error) {
            console.error('Error updating template:', error);
            return false;
        }
    }

    showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

/**
 * Phase 3: AI Enhancement Engine
 * Provides advanced AI-powered features for task analysis and suggestions
 */
class AIEnhancementEngine {
    constructor() {
        this.cache = new Map();
        this.requestQueue = [];
        this.isProcessing = false;
    }

    async analyzeTask(title, description = '') {
        // Comprehensive AI analysis of a task
        const cacheKey = `analyze_${title}_${description}`;

        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        try {
            const response = await fetch('/api/ai/analyze-task', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ title, description })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.cache.set(cacheKey, data.analysis);
                    return data.analysis;
                }
            }
        } catch (error) {
            console.error('Error analyzing task:', error);
        }

        return null;
    }

    async getNextTaskPredictions(limit = 5) {
        // Get AI predictions for next likely tasks
        try {
            const response = await fetch(`/api/ai/predict-next-tasks?limit=${limit}`);

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    return data.predictions;
                }
            }
        } catch (error) {
            console.error('Error getting next task predictions:', error);
        }

        return [];
    }

    async getWorkloadOptimization(targetDate = null) {
        // Get workload optimization recommendations
        const dateParam = targetDate ? `?date=${targetDate}` : '';

        try {
            const response = await fetch(`/api/ai/workload-optimization${dateParam}`);

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    return data.optimization;
                }
            }
        } catch (error) {
            console.error('Error getting workload optimization:', error);
        }

        return null;
    }

    async getEnhancedClassificationSuggestion(title, description = '') {
        // Get enhanced AI-powered classification suggestion
        try {
            const response = await fetch('/api/suggestions/enhanced-classification', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ title, description })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    return data.prediction;
                }
            }
        } catch (error) {
            console.error('Error getting enhanced classification:', error);
        }

        return null;
    }

    async getEnhancedDurationSuggestion(title, description = '', classification = '') {
        // Get enhanced AI-powered duration suggestion
        try {
            const response = await fetch('/api/suggestions/enhanced-duration', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ title, description, classification })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    return data.estimation;
                }
            }
        } catch (error) {
            console.error('Error getting enhanced duration:', error);
        }

        return null;
    }

    displayAIInsights(analysis, container) {
        // Display AI analysis insights in the UI
        if (!analysis || !container) return;

        const insightsHtml = `
            <div class="ai-insights-card card border-primary mb-3">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="bi bi-robot"></i> AI Task Analysis
                    </h6>
                    <button type="button" class="btn btn-sm btn-outline-light ai-toggle-btn"
                            title="Disable AI Analysis">
                        <i class="bi bi-toggle-on"></i>
                    </button>
                </div>
                <div class="card-body">
                    <!-- Priority Analysis -->
                    <div class="mb-3">
                        <h6 class="text-primary">Priority Assessment</h6>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-${this.getPriorityColor(analysis.priority.level)} me-2">
                                ${analysis.priority.level.toUpperCase()}
                            </span>
                            <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                <div class="progress-bar bg-${this.getPriorityColor(analysis.priority.level)}"
                                     style="width: ${Math.round(analysis.priority.confidence * 100)}%"></div>
                            </div>
                            <small class="text-muted">${Math.round(analysis.priority.confidence * 100)}%</small>
                        </div>
                    </div>

                    <!-- Complexity Analysis -->
                    <div class="mb-3">
                        <h6 class="text-primary">Complexity Assessment</h6>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-${this.getComplexityColor(analysis.complexity.level)} me-2">
                                ${analysis.complexity.level.toUpperCase()}
                            </span>
                            <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                <div class="progress-bar bg-${this.getComplexityColor(analysis.complexity.level)}"
                                     style="width: ${analysis.complexity.score * 100}%"></div>
                            </div>
                            <small class="text-muted">${Math.round(analysis.complexity.score * 100)}%</small>
                        </div>
                    </div>

                    <!-- Duration Prediction -->
                    <div class="mb-3">
                        <h6 class="text-primary">Duration Estimate</h6>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-info me-2">${analysis.duration.duration} min</span>
                            <small class="text-muted">
                                Range: ${analysis.duration.range.min}-${analysis.duration.range.max} min
                                (${Math.round(analysis.duration.confidence * 100)}% confidence)
                            </small>
                        </div>
                    </div>

                    <!-- AI Insights -->
                    ${analysis.insights && analysis.insights.length > 0 ? `
                        <div class="mb-3">
                            <h6 class="text-primary">AI Insights</h6>
                            <ul class="list-unstyled mb-0">
                                ${analysis.insights.map(insight => `
                                    <li class="mb-1">
                                        <i class="bi bi-lightbulb text-warning me-1"></i>
                                        <small>${insight}</small>
                                    </li>
                                `).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    <!-- Similar Tasks -->
                    ${analysis.similar_tasks && analysis.similar_tasks.length > 0 ? `
                        <div class="mb-0">
                            <h6 class="text-primary">Similar Tasks Found</h6>
                            <div class="row">
                                ${analysis.similar_tasks.slice(0, 3).map(similar => `
                                    <div class="col-md-4 mb-2">
                                        <div class="border rounded p-2">
                                            <small class="fw-bold">${similar.title}</small><br>
                                            <small class="text-muted">
                                                ${similar.classification} • ${similar.est_time}min
                                                <br>Similarity: ${Math.round(similar.similarity * 100)}%
                                            </small>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        container.innerHTML = insightsHtml;

        // Add toggle button event listener
        const toggleBtn = container.querySelector('.ai-toggle-btn');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => {
                this.toggleAIAnalysis();
            });
        }
    }

    getPriorityColor(level) {
        const colors = {
            'high': 'danger',
            'medium': 'warning',
            'low': 'info',
            'normal': 'secondary'
        };
        return colors[level] || 'secondary';
    }

    getComplexityColor(level) {
        const colors = {
            'high': 'danger',
            'medium': 'warning',
            'low': 'success'
        };
        return colors[level] || 'secondary';
    }

    // AI Analysis Toggle Management
    loadAIAnalysisState() {
        // Default is disabled (false)
        try {
            const saved = localStorage.getItem('aiAnalysisEnabled');
            this.aiAnalysisEnabled = saved === 'true';
        } catch (error) {
            console.warn('Could not load AI analysis state:', error);
            this.aiAnalysisEnabled = false;
        }
    }

    saveAIAnalysisState() {
        localStorage.setItem('aiAnalysisEnabled', this.aiAnalysisEnabled.toString());
    }

    toggleAIAnalysis() {
        this.aiAnalysisEnabled = !this.aiAnalysisEnabled;
        this.saveAIAnalysisState();

        // Show/hide AI analysis cards based on toggle state
        const aiCards = document.querySelectorAll('.ai-widget-card, .ai-insight-card, #ai-insights-container, .ai-analysis-card');
        aiCards.forEach(card => {
            if (this.aiAnalysisEnabled) {
                card.style.display = '';
            } else {
                card.style.display = 'none';
            }
        });

        if (this.aiAnalysisEnabled) {
            // Re-run AI analysis if enabled
            const titleField = document.querySelector('[name="title"]');
            const descriptionField = document.querySelector('[name="description"]');

            if (titleField && titleField.value.length >= 3) {
                this.performAIAnalysis();
            }

            this.showNotification('AI Task Analysis enabled', 'success', 3000);
        } else {
            // Clear AI insights if disabled
            const container = document.querySelector('#ai-insights-container');
            if (container) {
                container.innerHTML = '';
            }

            this.showNotification('AI Task Analysis disabled', 'info', 3000);
        }
    }

    addAIToggleButton() {
        // Add an AI toggle button to the form header if it doesn't exist
        try {
            const cardHeader = document.querySelector('.card-header');
            if (cardHeader && !document.getElementById('aiToggleMainBtn')) {
                const toggleBtn = document.createElement('button');
                toggleBtn.type = 'button';
                toggleBtn.id = 'aiToggleMainBtn';
                toggleBtn.className = `btn btn-sm ${this.aiAnalysisEnabled ? 'btn-primary' : 'btn-outline-secondary'} ms-2`;
                toggleBtn.innerHTML = `<i class="bi bi-robot"></i> AI Analysis ${this.aiAnalysisEnabled ? 'ON' : 'OFF'}`;
                toggleBtn.title = `${this.aiAnalysisEnabled ? 'Disable' : 'Enable'} AI Analysis`;

                toggleBtn.addEventListener('click', () => {
                    this.toggleAIAnalysis();
                    // Update button appearance
                    toggleBtn.className = `btn btn-sm ${this.aiAnalysisEnabled ? 'btn-primary' : 'btn-outline-secondary'} ms-2`;
                    toggleBtn.innerHTML = `<i class="bi bi-robot"></i> AI Analysis ${this.aiAnalysisEnabled ? 'ON' : 'OFF'}`;
                    toggleBtn.title = `${this.aiAnalysisEnabled ? 'Disable' : 'Enable'} AI Analysis`;
                });

                cardHeader.appendChild(toggleBtn);
            }
        } catch (error) {
            console.error('Error adding AI toggle button:', error);
        }
    }
}

/**
 * Phase 3: Analytics Tracker
 * Tracks user interactions for continuous improvement
 */
class AnalyticsTracker {
    constructor() {
        this.sessionStart = Date.now();
        this.eventQueue = [];
        this.formStartTime = null;
        this.fieldInteractions = new Map();
    }

    trackEvent(eventType, data = {}) {
        // Track a user interaction event
        const event = {
            event_type: eventType,
            data: {
                ...data,
                timestamp: new Date().toISOString(),
                session_duration: Date.now() - this.sessionStart
            },
            user_id: 'default_user' // Can be enhanced with actual user identification
        };

        this.eventQueue.push(event);
        this.flushEvents();
    }

    async flushEvents() {
        // Send events to the server
        if (this.eventQueue.length === 0) return;

        const eventsToSend = [...this.eventQueue];
        this.eventQueue = [];

        try {
            for (const event of eventsToSend) {
                await fetch('/api/analytics/track-event', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(event)
                });
            }
        } catch (error) {
            console.error('Error sending analytics events:', error);
            // Re-queue events on failure
            this.eventQueue.unshift(...eventsToSend);
        }
    }

    trackFormStart() {
        // Track when user starts filling the form
        this.formStartTime = Date.now();
        this.trackEvent('form_started', {
            page_url: window.location.href
        });
    }

    trackFormCompletion(fieldsUsed, errorsEncountered = 0) {
        // Track form completion
        if (!this.formStartTime) return;

        const completionTime = (Date.now() - this.formStartTime) / 1000; // seconds

        this.trackEvent('form_completed', {
            completion_time: completionTime,
            fields_used: fieldsUsed,
            errors_encountered: errorsEncountered,
            total_fields: fieldsUsed.length
        });
    }

    trackSuggestionShown(suggestionType, suggestionData, confidence = 0) {
        // Track when a suggestion is shown
        this.trackEvent('suggestion_shown', {
            suggestion_type: suggestionType,
            confidence: confidence,
            suggestion_data: suggestionData
        });
    }

    async trackSuggestionInteraction(interactionType, suggestionType, suggestionData, responseTime = 0) {
        // Track user interaction with suggestions
        try {
            await fetch('/api/suggestions/track-interaction', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    type: interactionType, // 'accepted', 'rejected', 'ignored'
                    suggestion_type: suggestionType,
                    suggestion_data: suggestionData,
                    response_time: responseTime
                })
            });
        } catch (error) {
            console.error('Error tracking suggestion interaction:', error);
        }
    }

    trackPerformanceMetric(metricName, value, context = {}) {
        // Track performance metrics
        this.trackEvent('performance_metric', {
            metric_name: metricName,
            value: value,
            context: context
        });
    }

    trackFieldInteraction(fieldName, action, value = null) {
        // Track field-level interactions
        const fieldKey = fieldName;

        if (!this.fieldInteractions.has(fieldKey)) {
            this.fieldInteractions.set(fieldKey, {
                focus_count: 0,
                change_count: 0,
                first_focus: Date.now(),
                total_focus_time: 0,
                last_focus_start: null
            });
        }

        const field = this.fieldInteractions.get(fieldKey);
        const now = Date.now();

        switch (action) {
            case 'focus':
                field.focus_count++;
                field.last_focus_start = now;
                break;
            case 'blur':
                if (field.last_focus_start) {
                    field.total_focus_time += now - field.last_focus_start;
                    field.last_focus_start = null;
                }
                break;
            case 'change':
                field.change_count++;
                break;
        }

        this.fieldInteractions.set(fieldKey, field);
    }

    getFieldInteractionSummary() {
        // Get summary of field interactions
        const summary = {};

        for (const [fieldName, data] of this.fieldInteractions) {
            summary[fieldName] = {
                ...data,
                avg_focus_time: data.focus_count > 0 ? data.total_focus_time / data.focus_count : 0
            };
        }

        return summary;
    }
}

// Global function for backward compatibility
function fillTaskForm(taskDataEncoded) {
    try {
        const taskData = JSON.parse(decodeURIComponent(taskDataEncoded));
        if (window.taskFormManager) {
            window.taskFormManager.applyTemplate({ name: taskData.title, data: taskData });
        }
    } catch (error) {
        console.error('Error filling task form:', error);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.taskFormManager = new TaskFormManager();
    // Create global alias for compatibility
    window.taskForm = window.taskFormManager;
});
