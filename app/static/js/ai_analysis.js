/**
 * AI Analysis Module
 * Handles all AI-related functionality including toggle, card visibility, and analysis display
 */

class AIAnalysisManager {
    constructor() {
        this.aiAnalysisEnabled = false;
        this.loadAIAnalysisState();
        this.init();
    }

    init() {
        // Initialize AI analysis features
        this.createAIInsightsContainer();
        this.createAIToggleButton();

        this.setupRealTimeAIAnalysis();
        this.setupNextTaskPredictions();

        // Set initial card visibility after widgets are created
        this.setInitialAICardVisibility();
    }

    loadAIAnalysisState() {
        try {
            const saved = localStorage.getItem('aiAnalysisEnabled');
            this.aiAnalysisEnabled = saved === 'true';
        } catch (error) {
            console.warn('Could not load AI analysis state:', error);
            this.aiAnalysisEnabled = false;
        }
    }

    saveAIAnalysisState() {
        try {
            localStorage.setItem('aiAnalysisEnabled', this.aiAnalysisEnabled.toString());
        } catch (error) {
            console.warn('Could not save AI analysis state:', error);
        }
    }

    createAIToggleButton() {
        // Create AI toggle button in the form header
        setTimeout(() => {
            try {
                const cardHeader = document.querySelector('.card-header');
                if (cardHeader && !document.getElementById('aiToggleMainBtn')) {
                    const toggleBtn = document.createElement('button');
                    toggleBtn.type = 'button';
                    toggleBtn.id = 'aiToggleMainBtn';
                    toggleBtn.className = `btn btn-sm ${this.aiAnalysisEnabled ? 'btn-primary' : 'btn-outline-secondary'} ms-2`;
                    toggleBtn.innerHTML = `<i class="bi bi-robot"></i> AI Analysis ${this.aiAnalysisEnabled ? 'ON' : 'OFF'}`;
                    toggleBtn.title = `${this.aiAnalysisEnabled ? 'Disable' : 'Enable'} AI Analysis`;

                    // Use arrow function to maintain proper 'this' binding
                    toggleBtn.addEventListener('click', () => {
                        this.toggleAIAnalysis();
                    });

                    cardHeader.appendChild(toggleBtn);
                }
            } catch (error) {
                console.error('Error creating AI toggle button:', error);
            }
        }, 300); // Slight delay to ensure DOM is ready
    }

    toggleAIAnalysis() {
        try {
            this.aiAnalysisEnabled = !this.aiAnalysisEnabled;
            this.saveAIAnalysisState();

            // Update button appearance
            const toggleBtn = document.getElementById('aiToggleMainBtn');
            if (toggleBtn) {
                toggleBtn.className = `btn btn-sm ${this.aiAnalysisEnabled ? 'btn-primary' : 'btn-outline-secondary'} ms-2`;
                toggleBtn.innerHTML = `<i class="bi bi-robot"></i> AI Analysis ${this.aiAnalysisEnabled ? 'ON' : 'OFF'}`;
                toggleBtn.title = `${this.aiAnalysisEnabled ? 'Disable' : 'Enable'} AI Analysis`;
            }

            // Show/hide AI analysis cards based on toggle state
            this.updateAICardVisibility();

            if (this.aiAnalysisEnabled) {
                // Re-run AI analysis if enabled
                const titleField = document.querySelector('[name="title"]');
                if (titleField && titleField.value.length >= 3) {
                    this.performAIAnalysis();
                }
                this.showNotification('AI Task Analysis enabled', 'success', 3000);
            } else {
                // Clear AI insights if disabled
                this.clearAIInsights();
                this.showNotification('AI Task Analysis disabled', 'info', 3000);
            }
        } catch (error) {
            console.error('Error toggling AI analysis:', error);
        }
    }

    updateAICardVisibility() {
        // Show/hide AI analysis cards based on toggle state
        const aiCards = document.querySelectorAll('.ai-widget-card, .ai-insight-card, #ai-insights-container, .ai-analysis-card');
        aiCards.forEach(card => {
            if (this.aiAnalysisEnabled) {
                card.style.display = '';
            } else {
                card.style.display = 'none';
            }
        });
    }

    setInitialAICardVisibility() {
        // Apply current visibility state to any existing AI cards
        this.updateAICardVisibility();
    }

    clearAIInsights() {
        const container = document.querySelector('#ai-insights-container');
        if (container) {
            container.innerHTML = '';
        }
    }

    createAIInsightsContainer() {
        // Find a good place to insert AI insights
        const taskDetailsSection = document.querySelector('.card-body');
        if (taskDetailsSection) {
            const insightsContainer = document.createElement('div');
            insightsContainer.id = 'ai-insights-container';
            insightsContainer.className = 'mb-3 ai-analysis-card';

            // Insert after the Quick Add from Previous Tasks section
            const quickAddSection = document.querySelector('#quick-add-section');
            if (quickAddSection && quickAddSection.nextSibling) {
                quickAddSection.parentNode.insertBefore(insightsContainer, quickAddSection.nextSibling);
            } else {
                taskDetailsSection.insertBefore(insightsContainer, taskDetailsSection.firstChild);
            }
        }
    }

    setupRealTimeAIAnalysis() {
        const titleField = document.querySelector('[name="title"]');
        const descriptionField = document.querySelector('[name="description"]');

        let analysisTimeout;

        const performAnalysis = () => {
            // Check if AI analysis is enabled
            if (!this.aiAnalysisEnabled) {
                this.clearAIInsights();
                return;
            }

            const title = titleField?.value || '';
            const description = descriptionField?.value || '';

            if (title.length < 3) {
                // Clear insights if title is too short for meaningful analysis
                this.clearAIInsights();
                return;
            }

            this.performAIAnalysis();
        };

        // Setup event listeners for AI analysis
        if (titleField) {
            titleField.addEventListener('input', () => {
                clearTimeout(analysisTimeout);
                analysisTimeout = setTimeout(performAnalysis, 800); // Reduced delay
            });

            // Also trigger on blur to catch paste events
            titleField.addEventListener('blur', () => {
                clearTimeout(analysisTimeout);
                setTimeout(performAnalysis, 100);
            });
        }

        if (descriptionField) {
            descriptionField.addEventListener('input', () => {
                clearTimeout(analysisTimeout);
                analysisTimeout = setTimeout(performAnalysis, 1000); // Reduced delay
            });

            // Also trigger on blur
            descriptionField.addEventListener('blur', () => {
                clearTimeout(analysisTimeout);
                setTimeout(performAnalysis, 100);
            });
        }
    }

    async performAIAnalysis() {
        if (!this.aiAnalysisEnabled) return;

        const titleField = document.querySelector('[name="title"]');
        const descriptionField = document.querySelector('[name="description"]');

        const title = titleField?.value || '';
        const description = descriptionField?.value || '';

        if (title.length < 3) return; // Require minimal input

        try {
            // Make real API call to AI analysis endpoint
            const response = await fetch('/api/ai/analyze-task', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    title: title,
                    description: description
                })
            });

            if (response.ok) {
                const data = await response.json();
                console.log('AI Analysis Response:', data); // Debug log
                if (data.success && data.analysis) {
                    this.displayAIAnalysis(data.analysis);
                } else {
                    console.warn('AI analysis returned no data:', data);
                    this.clearAIInsights();
                }
            } else {
                console.warn('AI analysis request failed:', response.status);
                this.clearAIInsights();
            }
        } catch (error) {
            console.error('Error performing AI analysis:', error);
            this.clearAIInsights();
        }
    }

    displayAIAnalysis(analysis) {
        const container = document.querySelector('#ai-insights-container');
        if (!container) return;

        console.log('Displaying AI Analysis:', analysis); // Debug log

        // Handle case where analysis has very low confidence
        if (analysis.priority.confidence < 0.15) {
            console.log('Low confidence analysis, showing help message'); // Debug log
            container.innerHTML = `
                <div class="card border-secondary ai-insight-card">
                    <div class="card-header bg-light text-muted d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="bi bi-robot"></i> AI Task Analysis
                        </h6>
                        <small>Need more details</small>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            Add more details for better AI analysis and predictions.
                        </p>
                    </div>
                </div>
            `;
            return;
        }

        const priorityConfidence = Math.round(analysis.priority.confidence * 100);
        const classificationConfidence = analysis.classification ? Math.round(analysis.classification.confidence * 100) : 0;
        const durationConfidence = analysis.duration ? Math.round(analysis.duration.confidence * 100) : 0;

        container.innerHTML = `
            <div class="card border-primary ai-insight-card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="bi bi-robot"></i> AI Task Analysis
                    </h6>
                    <small class="opacity-75">${priorityConfidence}% confidence</small>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="text-primary">Priority Assessment</h6>
                            <div class="d-flex align-items-center mb-2">
                                <span class="badge bg-${this.getPriorityColor(analysis.priority.level)} me-2">
                                    ${analysis.priority.level.toUpperCase()}
                                </span>
                                <div class="flex-grow-1">
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-${this.getPriorityColor(analysis.priority.level)}"
                                             style="width: ${priorityConfidence}%"></div>
                                    </div>
                                </div>
                                <small class="text-muted ms-2">${priorityConfidence}%</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">Duration Estimate</h6>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-clock text-muted me-2"></i>
                                <span class="fw-bold">${analysis.duration ? analysis.duration.duration : 30} minutes</span>
                                <small class="text-muted ms-2">(${durationConfidence}% confidence)</small>
                            </div>
                        </div>
                    </div>

                    ${analysis.insights && analysis.insights.length > 0 ? `
                        <div class="mb-3">
                            <h6 class="text-primary">AI Insights</h6>
                            <ul class="list-unstyled mb-0">
                                ${analysis.insights.map(insight => `
                                    <li class="mb-1">
                                        <i class="bi bi-lightbulb text-warning me-2"></i>
                                        <small>${insight}</small>
                                    </li>
                                `).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${analysis.classification && analysis.classification.classification && classificationConfidence > 40 ? `
                        <div class="alert alert-info py-2 mb-0">
                            <small>
                                <i class="bi bi-info-circle me-1"></i>
                                Suggested classification: <strong>${analysis.classification.classification}</strong>
                                (${classificationConfidence}% confidence)
                            </small>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    getPriorityColor(level) {
        const colors = {
            'low': 'success',
            'normal': 'info',
            'medium': 'warning',
            'high': 'danger',
            'unknown': 'secondary'
        };
        return colors[level] || 'secondary';
    }

    setupNextTaskPredictions() {
        // Create next task predictions widget
        setTimeout(() => {
            this.createNextTaskPredictionsWidget();
        }, 500);
    }

    async createNextTaskPredictionsWidget() {
        try {
            // Fetch real predictions from the API
            const response = await fetch('/api/ai/predict-next-tasks?limit=3');
            let predictions = [];

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.predictions) {
                    predictions = data.predictions;
                }
            }

            // Only show widget if we have meaningful predictions
            if (predictions && predictions.length > 0) {
                const widget = document.createElement('div');
                widget.className = 'card border-info mb-3 ai-widget-card';
                widget.innerHTML = `
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="bi bi-magic"></i> AI Task Predictions
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="card-text mb-3">
                            <small class="text-muted">Based on your patterns, you might want to work on:</small>
                        </p>
                        <div class="row">
                            ${predictions.map((prediction, index) => `
                                <div class="col-md-4 mb-2">
                                    <div class="border rounded p-2 h-100 prediction-card"
                                         data-prediction="${encodeURIComponent(JSON.stringify(prediction))}"
                                         style="cursor: pointer;">
                                        <div class="fw-bold small">${prediction.title}</div>
                                        <div class="text-muted small">${prediction.classification}</div>
                                        <div class="text-muted small">${prediction.est_time} min</div>
                                        <div class="text-info small">${prediction.reason}</div>
                                        <div class="text-end">
                                            <span class="badge bg-success">${Math.round(prediction.confidence * 100)}%</span>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;

                // Add click handlers for predictions
                widget.addEventListener('click', (e) => {
                    const predictionCard = e.target.closest('.prediction-card');
                    if (predictionCard) {
                        try {
                            const prediction = JSON.parse(decodeURIComponent(predictionCard.dataset.prediction));
                            this.applyPredictionToForm(prediction);
                        } catch (error) {
                            console.error('Error applying prediction:', error);
                        }
                    }
                });

                // Insert the widget after AI insights container
                const insightsContainer = document.querySelector('#ai-insights-container');
                if (insightsContainer && insightsContainer.parentNode) {
                    insightsContainer.parentNode.insertBefore(widget, insightsContainer.nextSibling);

                    // Apply current visibility state immediately after creation
                    if (!this.aiAnalysisEnabled) {
                        widget.style.display = 'none';
                    }
                }
            }
        } catch (error) {
            console.error('Error creating next task predictions widget:', error);
        }
    }

    applyPredictionToForm(prediction) {
        // Apply AI prediction to form fields
        const titleField = document.querySelector('[name="title"]');
        const classificationSelect = document.querySelector('[name="classification"]');
        const estTimeField = document.querySelector('[name="est_time"]');

        if (titleField) titleField.value = prediction.title;
        if (classificationSelect) classificationSelect.value = prediction.classification;
        if (estTimeField) estTimeField.value = prediction.est_time;

        // Update category based on classification
        if (classificationSelect) {
            classificationSelect.dispatchEvent(new Event('change', { bubbles: true }));
        }

        // Trigger AI analysis for the new content
        const event = new Event('input', { bubbles: true });
        if (titleField) titleField.dispatchEvent(event);

        this.showNotification(`Applied prediction: "${prediction.title}"`, 'success', 3000);
    }

    showNotification(message, type = 'info', duration = 5000) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px; max-width: 400px;';

        const iconMap = {
            'success': 'bi-check-circle',
            'error': 'bi-exclamation-triangle',
            'warning': 'bi-exclamation-triangle',
            'info': 'bi-info-circle'
        };

        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="${iconMap[type] || iconMap.info} me-2"></i>
                <div class="flex-grow-1">${message}</div>
                <button type="button" class="btn-close" aria-label="Close"></button>
            </div>
        `;

        // Add click handler for close button
        const closeBtn = notification.querySelector('.btn-close');
        closeBtn.addEventListener('click', () => {
            notification.remove();
        });

        // Add to DOM
        document.body.appendChild(notification);

        // Auto-remove after duration
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    }
}

// Initialize AI Analysis Manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Small delay to ensure other scripts are loaded
    setTimeout(() => {
        window.aiAnalysisManager = new AIAnalysisManager();
    }, 500);
});
