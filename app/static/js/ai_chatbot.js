/**
 * AI Chatbot Interface for AdhocLog
 * Provides conversational task management and AI assistance
 */

class AIChatbot {
    constructor() {
        this.chatContainer = null;
        this.messagesContainer = null;
        this.inputField = null;
        this.sendButton = null;
        this.isOpen = false;
        this.isTyping = false;
        this.conversationHistory = [];

        this.init();
    }

    init() {
        this.createChatInterface();
        this.bindEvents();
        this.loadConversationSuggestions();
    }

    createChatInterface() {
        // Create main chat container
        this.chatContainer = document.createElement('div');
        this.chatContainer.className = 'ai-chatbot-container';
        this.chatContainer.innerHTML = `
            <div class="chatbot-header">
                <div class="chatbot-title">
                    <i class="bi bi-robot"></i>
                    <span>AdBot</span>
                </div>
                <div class="chatbot-controls">
                    <button class="btn btn-sm btn-outline-light chatbot-minimize" title="Minimize">
                        <i class="bi bi-dash"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-light chatbot-close" title="Close">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            </div>
            <div class="chatbot-messages" id="chatbot-messages">
                <div class="welcome-message">
                    <div class="message assistant-message">
                        <div class="message-content">
                            <i class="bi bi-robot me-2"></i>
                            <div class="message-text">Hello! I'm AdBot, your AI task assistant. I can help you create tasks, manage your schedule, and optimize your productivity. What would you like to do?</div>
                        </div>
                    </div>
                </div>
                <div class="conversation-suggestions" id="conversation-suggestions">
                    <!-- Suggestions will be loaded here -->
                </div>
            </div>
            <div class="chatbot-input">
                <div class="input-group">
                    <textarea class="form-control" id="chatbot-input" rows="1"
                              placeholder="Ask about tasks or get help..."
                              style="resize: none; overflow-y: hidden; min-height: 38px; max-height: 120px;"></textarea>
                    <button class="btn btn-primary" id="chatbot-send" disabled>
                        <i class="bi bi-send"></i>
                    </button>
                </div>
                <div class="chatbot-typing" id="chatbot-typing" style="display: none;">
                    <div class="typing-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <small class="text-muted">AI is typing...</small>
                </div>
            </div>
        `;

        // Add to page
        document.body.appendChild(this.chatContainer);

        // Get references
        this.messagesContainer = document.getElementById('chatbot-messages');
        this.inputField = document.getElementById('chatbot-input');
        this.sendButton = document.getElementById('chatbot-send');

        // Create floating button
        this.createFloatingButton();
    }

    createFloatingButton() {
        const floatingButton = document.createElement('div');
        floatingButton.className = 'chatbot-floating-button';
        floatingButton.innerHTML = `
            <button class="btn btn-primary btn-lg rounded-circle shadow" title="Open AdBot">
                <i class="bi bi-robot"></i>
            </button>
        `;
        floatingButton.addEventListener('click', () => this.toggleChat());
        document.body.appendChild(floatingButton);
    }

    bindEvents() {
        // Input field events
        this.inputField.addEventListener('input', () => {
            this.sendButton.disabled = !this.inputField.value.trim();
            this.autoResizeTextarea();
        });

        this.inputField.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Send button
        this.sendButton.addEventListener('click', () => this.sendMessage());

        // Header controls
        this.chatContainer.querySelector('.chatbot-minimize').addEventListener('click', () => {
            this.toggleChat();
        });

        this.chatContainer.querySelector('.chatbot-close').addEventListener('click', () => {
            this.closeChat();
        });

        // Click outside to close
        document.addEventListener('click', (e) => {
            if (this.isOpen && !this.chatContainer.contains(e.target) &&
                !e.target.closest('.chatbot-floating-button')) {
                // Don't auto-close for now - could be annoying during use
            }
        });
    }

    async loadConversationSuggestions() {
        try {
            const response = await fetch('/api/chatbot/suggestions');
            const data = await response.json();

            if (data.success && data.suggestions) {
                this.displayConversationSuggestions(data.suggestions);
            }
        } catch (error) {
            console.error('Error loading conversation suggestions:', error);
        }
    }

    displayConversationSuggestions(suggestions) {
        const suggestionsContainer = document.getElementById('conversation-suggestions');
        if (!suggestionsContainer) return;

        const suggestionsHtml = `
            <div class="suggestions-wrapper">
                <div class="suggestions-title">
                    <small class="text-muted">Try asking:</small>
                </div>
                <div class="suggestions-list">
                    ${suggestions.map(suggestion => `
                        <button class="btn btn-outline-secondary btn-sm suggestion-btn"
                                data-suggestion="${suggestion}">
                            ${suggestion}
                        </button>
                    `).join('')}
                </div>
            </div>
        `;

        suggestionsContainer.innerHTML = suggestionsHtml;

        // Bind suggestion clicks
        suggestionsContainer.querySelectorAll('.suggestion-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const suggestion = btn.getAttribute('data-suggestion');
                this.inputField.value = suggestion;
                this.sendButton.disabled = false;
                this.hideSuggestions();
            });
        });
    }

    hideSuggestions() {
        const suggestionsContainer = document.getElementById('conversation-suggestions');
        if (suggestionsContainer) {
            suggestionsContainer.style.display = 'none';
        }
    }

    toggleChat() {
        if (this.isOpen) {
            this.closeChat();
        } else {
            this.openChat();
        }
    }

    openChat() {
        this.chatContainer.classList.add('open');
        this.isOpen = true;
        this.inputField.focus();

        // Hide floating button
        const floatingButton = document.querySelector('.chatbot-floating-button');
        if (floatingButton) {
            floatingButton.style.display = 'none';
        }
    }

    closeChat() {
        this.chatContainer.classList.remove('open');
        this.isOpen = false;

        // Show floating button
        const floatingButton = document.querySelector('.chatbot-floating-button');
        if (floatingButton) {
            floatingButton.style.display = 'block';
        }
    }

    async sendMessage() {
        const message = this.inputField.value.trim();
        if (!message) return;

        // Add user message to chat
        this.addMessage(message, 'user');

        // Clear input
        this.inputField.value = '';
        this.sendButton.disabled = true;

        // Reset textarea height
        this.inputField.style.height = '38px';

        // Hide suggestions
        this.hideSuggestions();

        // Show typing indicator
        this.showTyping();

        try {
            // Send to backend
            const response = await fetch('/api/chatbot/message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: message })
            });

            const data = await response.json();

            this.hideTyping();

            if (data.success && data.response) {
                this.addMessage(data.response.content, 'assistant', data.response.message_type, data.response.metadata);
            } else {
                this.addMessage('Sorry, I encountered an error processing your request. Please try again.', 'assistant');
            }

        } catch (error) {
            this.hideTyping();
            console.error('Error sending message:', error);
            this.addMessage('Sorry, I\'m having trouble connecting right now. Please try again later.', 'assistant');
        }
    }

    addMessage(content, sender, messageType = 'text', metadata = {}) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        let iconClass = sender === 'user' ? 'bi-person-circle' : 'bi-robot';
        let messageClass = '';

        // Special styling for different message types
        switch (messageType) {
            case 'task_created':
                messageClass = 'message-success';
                iconClass = 'bi-check-circle';
                break;
            case 'task_list':
                messageClass = 'message-info';
                iconClass = 'bi-list-task';
                break;
            case 'suggestion':
                messageClass = 'message-suggestion';
                iconClass = 'bi-lightbulb';
                break;
            case 'analytics':
                messageClass = 'message-analytics';
                iconClass = 'bi-graph-up';
                break;
            case 'out_of_scope':
                messageClass = 'message-out-of-scope';
                iconClass = 'bi-exclamation-triangle';
                break;
            case 'time_summary':
                messageClass = 'message-time-summary';
                iconClass = 'bi-clock-history';
                break;
            case 'structured_fallback':
                messageClass = 'message-structured-fallback';
                iconClass = 'bi-question-circle';
                break;
        }

        messageDiv.innerHTML = `
            <div class="message-content ${messageClass}">
                <i class="${iconClass} me-2"></i>
                <div class="message-text">${this.formatMessage(content, messageType, metadata)}</div>
            </div>
            <div class="message-timestamp">
                <small class="text-muted">${new Date().toLocaleTimeString()}</small>
            </div>
        `;

        // Insert before typing indicator or at end
        const typingIndicator = document.getElementById('chatbot-typing');
        if (typingIndicator && typingIndicator.parentNode === this.messagesContainer) {
            this.messagesContainer.insertBefore(messageDiv, typingIndicator);
        } else {
            this.messagesContainer.appendChild(messageDiv);
        }

        // Add entrance animation
        messageDiv.style.opacity = '0';
        messageDiv.style.transform = sender === 'user' ? 'translateX(20px)' : 'translateX(-20px)';

        // Trigger animation
        setTimeout(() => {
            messageDiv.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
            messageDiv.style.opacity = '1';
            messageDiv.style.transform = 'translateX(0)';
        }, 50);

        // Scroll to bottom with smooth animation
        this.scrollToBottom();

        // Store in conversation history
        this.conversationHistory.push({
            content,
            sender,
            messageType,
            metadata,
            timestamp: new Date()
        });
    }

    formatMessage(content, messageType, metadata) {
        // Convert newlines to HTML breaks
        let formattedContent = content.replace(/\n/g, '<br>');

        // Handle structured fallback with interactive suggestions
        if (messageType === 'structured_fallback' && metadata.suggestions) {
            formattedContent += '<div class="suggestion-buttons">';
            metadata.suggestions.forEach((suggestion, index) => {
                const icons = ['💡', '🎯', '📝', '⚡', '🔧', '📊'];
                const icon = icons[index % icons.length];
                formattedContent += `
                    <button class="btn btn-outline-primary suggestion-btn"
                            onclick="aiChatbot.sendSuggestion('${suggestion.replace(/'/g, "\\'")}')">
                        <span class="me-1">${icon}</span>${suggestion}
                    </button>
                `;
            });
            formattedContent += '</div>';
        }

        // Handle out-of-scope messages with IT alternatives
        if (messageType === 'out_of_scope') {
            const itSuggestions = [
                { text: "Create a task for system maintenance", icon: "🔧" },
                { text: "Show me my tasks for this week", icon: "📅" },
                { text: "How much time did I spend on planning tasks?", icon: "⏱️" },
                { text: "Help me prioritize my project tasks", icon: "🎯" }
            ];

            formattedContent += '<div class="suggestion-buttons">';
            itSuggestions.forEach(suggestion => {
                formattedContent += `
                    <button class="btn btn-outline-success suggestion-btn"
                            onclick="aiChatbot.sendSuggestion('${suggestion.text.replace(/'/g, "\\'")}')">
                        <span class="me-1">${suggestion.icon}</span>${suggestion.text}
                    </button>
                `;
            });
            formattedContent += '</div>';
        }

        // Add action buttons for certain message types
        if (messageType === 'task_created' && metadata.task) {
            formattedContent += `
                <div class="message-actions mt-2">
                    <button class="btn btn-xs btn-outline-primary me-1"
                            onclick="window.location.href='/tasks'">
                        <i class="bi bi-list-task"></i>View All Tasks
                    </button>
                    <button class="btn btn-xs btn-outline-secondary"
                            onclick="window.location.href='/tasks/edit/${metadata.task.id}'">
                        <i class="bi bi-pencil"></i>Edit Task
                    </button>
                </div>
            `;
        } else if (messageType === 'task_list' && metadata.tasks) {
            formattedContent += `
                <div class="message-actions mt-2">
                    <button class="btn btn-xs btn-outline-primary"
                            onclick="window.location.href='/tasks'">
                        <i class="bi bi-kanban"></i>View in Task Manager
                    </button>
                </div>
            `;
        } else if (messageType === 'time_summary') {
            formattedContent += `
                <div class="message-actions mt-2">
                    <button class="btn btn-xs btn-outline-info me-1"
                            onclick="window.location.href='/statistics'">
                        <i class="bi bi-graph-up"></i>View Full Analytics
                    </button>
                    <button class="btn btn-xs btn-outline-secondary"
                            onclick="window.location.href='/export_csv'">
                        <i class="bi bi-download"></i>Export Data
                    </button>
                </div>
            `;
        }

        return formattedContent;
    }

    // Method to handle suggestion button clicks
    sendSuggestion(suggestion) {
        this.inputField.value = suggestion;
        this.sendMessage();
    }

    showTyping() {
        this.isTyping = true;
        const typingIndicator = document.getElementById('chatbot-typing');
        if (typingIndicator) {
            typingIndicator.style.display = 'block';
        }
        this.scrollToBottom();
    }

    hideTyping() {
        this.isTyping = false;
        const typingIndicator = document.getElementById('chatbot-typing');
        if (typingIndicator) {
            typingIndicator.style.display = 'none';
        }
    }

    scrollToBottom() {
        // Small delay to ensure content is rendered
        setTimeout(() => {
            this.messagesContainer.scrollTo({
                top: this.messagesContainer.scrollHeight,
                behavior: 'smooth'
            });
        }, 100);
    }

    autoResizeTextarea() {
        // Reset height to auto to get the correct scrollHeight
        this.inputField.style.height = 'auto';

        // Calculate the new height based on content
        const newHeight = Math.min(this.inputField.scrollHeight, 120); // Max height of 120px

        // Set the new height
        this.inputField.style.height = newHeight + 'px';

        // Ensure minimum height
        if (newHeight < 38) {
            this.inputField.style.height = '38px';
        }
    }

    // Public methods for integration

    sendMessageProgrammatically(message) {
        this.inputField.value = message;
        this.sendButton.disabled = false;
        this.sendMessage();
    }

    openChatWithMessage(message) {
        this.openChat();
        this.sendMessageProgrammatically(message);
    }

    resetConversation() {
        fetch('/api/chatbot/reset', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Clear chat interface
                    const messages = this.messagesContainer.querySelectorAll('.message:not(.welcome-message .message)');
                    messages.forEach(msg => msg.remove());

                    // Show suggestions again
                    const suggestionsContainer = document.getElementById('conversation-suggestions');
                    if (suggestionsContainer) {
                        suggestionsContainer.style.display = 'block';
                    }

                    // Clear conversation history
                    this.conversationHistory = [];

                    this.addMessage('Conversation reset. How can I help you?', 'assistant');
                }
            })
            .catch(error => {
                console.error('Error resetting conversation:', error);
            });
    }
}

// Initialize chatbot when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if we're not on a page that might conflict
    if (!document.querySelector('.ai-chatbot-container')) {
        window.aiChatbot = new AIChatbot();
    }
});

// Utility functions for integration with existing pages
window.AIChatbotUtils = {
    openWithTaskCreation: () => {
        if (window.aiChatbot) {
            window.aiChatbot.openChatWithMessage('Help me create a task');
        }
    },

    openWithScheduleRequest: () => {
        if (window.aiChatbot) {
            window.aiChatbot.openChatWithMessage('Show me today\'s tasks');
        }
    },

    openWithSuggestions: () => {
        if (window.aiChatbot) {
            window.aiChatbot.openChatWithMessage('What should I work on next?');
        }
    },

    resetChat: () => {
        if (window.aiChatbot) {
            window.aiChatbot.resetConversation();
        }
    }
};
