"""
Advanced Analytics System for Task Management
Phase 3: User Behavior Tracking and Optimization

This module provides comprehensive analytics capabilities including:
- User behavior tracking
- Suggestion effectiveness monitoring
- Performance optimization
- A/B testing framework
- Continuous improvement algorithms

All data is stored locally for privacy compliance.
"""

import json
import os
import time
import getpass
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict, Counter
from dataclasses import dataclass, asdict
try:
    from .config import Config
except ImportError:
    from config import Config


@dataclass
class UserEvent:
    """Data class for user interaction events"""
    event_type: str
    timestamp: datetime
    data: Dict[str, Any]
    session_id: str
    user_id: str


@dataclass
class SuggestionMetrics:
    """Metrics for suggestion effectiveness"""
    suggestion_type: str
    total_shown: int
    total_accepted: int
    total_rejected: int
    total_ignored: int
    avg_confidence: float
    avg_response_time: float
    acceptance_rate: float


class AnalyticsEngine:
    """Core analytics engine for user behavior tracking"""

    def __init__(self, data_dir: Optional[str] = None):
        self.config = Config()
        # Use SharePoint-aware data directory if available
        self.data_dir = data_dir or self.config.EFFECTIVE_DATA_DIR
        self.username = getpass.getuser()
        self.analytics_file = os.path.join(self.data_dir, f'analytics_{self.username}.json')
        self.events_file = os.path.join(self.data_dir, f'user_events_{self.username}.json')
        self.session_id = self._generate_session_id()
        self._ensure_files_exist()

    def _generate_session_id(self) -> str:
        """Generate unique session identifier"""
        return f"session_{int(time.time())}"

    def _ensure_files_exist(self):
        """Ensure analytics files exist and migrate from legacy shared files if needed"""
        # Create user-specific directory if using SharePoint structure
        user_data_dir = os.path.dirname(self.analytics_file)
        if not os.path.exists(user_data_dir):
            os.makedirs(user_data_dir, exist_ok=True)

        # Handle migration from legacy shared files
        self._migrate_legacy_analytics_files()

        if not os.path.exists(self.analytics_file):
            self._save_analytics_data({
                'suggestions': {},
                'user_behavior': {},
                'performance': {},
                'ab_tests': {},
                'last_updated': datetime.now().isoformat()
            })

        if not os.path.exists(self.events_file):
            self._save_events_data([])

    def _migrate_legacy_analytics_files(self):
        """Migrate from legacy shared analytics files to user-specific files"""
        legacy_analytics_file = os.path.join(self.data_dir, 'analytics.json')
        legacy_events_file = os.path.join(self.data_dir, 'user_events.json')

        # Only migrate if user files don't exist but legacy files do
        if not os.path.exists(self.analytics_file) and os.path.exists(legacy_analytics_file):
            try:
                with open(legacy_analytics_file, 'r', encoding='utf-8') as f:
                    legacy_data = json.load(f)
                # Save to user-specific file
                self._save_analytics_data(legacy_data)
                print(f"Migrated analytics data to user-specific file: {self.analytics_file}")
            except Exception as e:
                print(f"Warning: Could not migrate legacy analytics file: {e}")

        if not os.path.exists(self.events_file) and os.path.exists(legacy_events_file):
            try:
                with open(legacy_events_file, 'r', encoding='utf-8') as f:
                    legacy_events = json.load(f)
                # Filter events for this user if user_id is available
                user_events = []
                for event in legacy_events:
                    if event.get('user_id') == self.username or event.get('user_id') == 'default_user':
                        user_events.append(event)
                self._save_events_data(user_events)
                print(f"Migrated user events to user-specific file: {self.events_file}")
            except Exception as e:
                print(f"Warning: Could not migrate legacy events file: {e}")

    def _load_analytics_data(self) -> Dict:
        """Load analytics data from file"""
        try:
            with open(self.analytics_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return {
                'suggestions': {},
                'user_behavior': {},
                'performance': {},
                'ab_tests': {},
                'last_updated': datetime.now().isoformat()
            }

    def _save_analytics_data(self, data: Dict):
        """Save analytics data to file"""
        data['last_updated'] = datetime.now().isoformat()
        with open(self.analytics_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False, default=str)

    def _load_events_data(self) -> List[Dict]:
        """Load user events from file"""
        try:
            with open(self.events_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return []

    def _save_events_data(self, events: List[Dict]):
        """Save user events to file"""
        # Keep only last 1000 events to prevent file growth
        if len(events) > 1000:
            events = events[-1000:]

        with open(self.events_file, 'w', encoding='utf-8') as f:
            json.dump(events, f, indent=2, ensure_ascii=False, default=str)

    def track_event(self, event_type: str, data: Dict[str, Any], user_id: str = 'default_user'):
        """Track a user interaction event"""
        event = UserEvent(
            event_type=event_type,
            timestamp=datetime.now(),
            data=data,
            session_id=self.session_id,
            user_id=user_id
        )

        # Load existing events and add new one
        events = self._load_events_data()
        events.append(asdict(event))
        self._save_events_data(events)

    def track_suggestion_shown(self, suggestion_type: str, suggestion_data: Dict, confidence: float = 0.0):
        """Track when a suggestion is shown to the user"""
        self.track_event('suggestion_shown', {
            'suggestion_type': suggestion_type,
            'confidence': confidence,
            'suggestion_data': suggestion_data
        })

    def track_suggestion_accepted(self, suggestion_type: str, suggestion_data: Dict, response_time: float = 0.0):
        """Track when a user accepts a suggestion"""
        self.track_event('suggestion_accepted', {
            'suggestion_type': suggestion_type,
            'response_time': response_time,
            'suggestion_data': suggestion_data
        })

    def track_suggestion_rejected(self, suggestion_type: str, suggestion_data: Dict, response_time: float = 0.0):
        """Track when a user rejects a suggestion"""
        self.track_event('suggestion_rejected', {
            'suggestion_type': suggestion_type,
            'response_time': response_time,
            'suggestion_data': suggestion_data
        })

    def track_form_completion(self, completion_time: float, fields_used: List[str], errors_encountered: int = 0):
        """Track form completion metrics"""
        self.track_event('form_completed', {
            'completion_time': completion_time,
            'fields_used': fields_used,
            'errors_encountered': errors_encountered,
            'total_fields': len(fields_used)
        })

    def track_performance_metric(self, metric_name: str, value: float, context: Optional[Dict] = None):
        """Track performance metrics"""
        self.track_event('performance_metric', {
            'metric_name': metric_name,
            'value': value,
            'context': context or {}
        })

    def get_suggestion_metrics(self, suggestion_type: Optional[str] = None, days: int = 30) -> Dict[str, SuggestionMetrics]:
        """Get metrics for suggestion effectiveness"""
        cutoff_date = datetime.now() - timedelta(days=days)
        events = self._load_events_data()

        # Filter events by date and type
        relevant_events = []
        for event in events:
            try:
                event_time = datetime.fromisoformat(event['timestamp'])
                if event_time >= cutoff_date:
                    if event['event_type'].startswith('suggestion_'):
                        if suggestion_type is None or event['data'].get('suggestion_type') == suggestion_type:
                            relevant_events.append(event)
            except:
                continue

        # Group events by suggestion type
        def default_stats():
            return {
                'shown': 0,
                'accepted': 0,
                'rejected': 0,
                'ignored': 0,
                'confidences': [],
                'response_times': []
            }

        suggestion_stats = defaultdict(default_stats)

        for event in relevant_events:
            s_type = event['data'].get('suggestion_type', 'unknown')

            if event['event_type'] == 'suggestion_shown':
                suggestion_stats[s_type]['shown'] += 1
                if 'confidence' in event['data']:
                    suggestion_stats[s_type]['confidences'].append(event['data']['confidence'])

            elif event['event_type'] == 'suggestion_accepted':
                suggestion_stats[s_type]['accepted'] += 1
                if 'response_time' in event['data']:
                    suggestion_stats[s_type]['response_times'].append(event['data']['response_time'])

            elif event['event_type'] == 'suggestion_rejected':
                suggestion_stats[s_type]['rejected'] += 1
                if 'response_time' in event['data']:
                    suggestion_stats[s_type]['response_times'].append(event['data']['response_time'])

        # Calculate metrics
        metrics = {}
        for s_type, stats in suggestion_stats.items():
            total_interactions = stats['accepted'] + stats['rejected']
            stats['ignored'] = max(0, stats['shown'] - total_interactions)

            acceptance_rate = stats['accepted'] / stats['shown'] if stats['shown'] > 0 else 0
            avg_confidence = sum(stats['confidences']) / len(stats['confidences']) if stats['confidences'] else 0
            avg_response_time = sum(stats['response_times']) / len(stats['response_times']) if stats['response_times'] else 0

            metrics[s_type] = SuggestionMetrics(
                suggestion_type=s_type,
                total_shown=stats['shown'],
                total_accepted=stats['accepted'],
                total_rejected=stats['rejected'],
                total_ignored=stats['ignored'],
                avg_confidence=avg_confidence,
                avg_response_time=avg_response_time,
                acceptance_rate=acceptance_rate
            )

        return metrics

    def get_user_behavior_patterns(self, days: int = 30) -> Dict:
        """Analyze user behavior patterns"""
        cutoff_date = datetime.now() - timedelta(days=days)
        events = self._load_events_data()

        # Filter recent events
        recent_events = []
        for event in events:
            try:
                event_time = datetime.fromisoformat(event['timestamp'])
                if event_time >= cutoff_date:
                    recent_events.append(event)
            except:
                continue

        patterns = {
            'activity_by_hour': defaultdict(int),
            'activity_by_day': defaultdict(int),
            'most_used_features': defaultdict(int),
            'completion_times': [],
            'error_rates': [],
            'session_durations': defaultdict(list)
        }

        session_starts = {}
        session_ends = {}

        for event in recent_events:
            try:
                event_time = datetime.fromisoformat(event['timestamp'])
                hour = event_time.hour
                day = event_time.strftime('%A')
                session_id = event.get('session_id', 'unknown')

                patterns['activity_by_hour'][hour] += 1
                patterns['activity_by_day'][day] += 1
                patterns['most_used_features'][event['event_type']] += 1

                # Track session durations
                if session_id not in session_starts:
                    session_starts[session_id] = event_time
                session_ends[session_id] = event_time

                # Track form completion metrics
                if event['event_type'] == 'form_completed':
                    data = event.get('data', {})
                    if 'completion_time' in data:
                        patterns['completion_times'].append(data['completion_time'])
                    if 'errors_encountered' in data:
                        patterns['error_rates'].append(data['errors_encountered'])

            except:
                continue

        # Calculate session durations
        for session_id in session_starts:
            if session_id in session_ends:
                duration = (session_ends[session_id] - session_starts[session_id]).total_seconds()
                patterns['session_durations'][session_id].append(duration)

        # Calculate summary statistics
        avg_completion_time = sum(patterns['completion_times']) / len(patterns['completion_times']) if patterns['completion_times'] else 0
        avg_errors = sum(patterns['error_rates']) / len(patterns['error_rates']) if patterns['error_rates'] else 0
        avg_session_duration = 0

        all_durations = [d for durations in patterns['session_durations'].values() for d in durations]
        if all_durations:
            avg_session_duration = sum(all_durations) / len(all_durations)

        return {
            'activity_by_hour': dict(patterns['activity_by_hour']),
            'activity_by_day': dict(patterns['activity_by_day']),
            'most_used_features': dict(Counter(patterns['most_used_features']).most_common(10)),
            'avg_completion_time': avg_completion_time,
            'avg_errors_per_form': avg_errors,
            'avg_session_duration': avg_session_duration,
            'total_sessions': len(session_starts),
            'total_events': len(recent_events)
        }

    def get_performance_metrics(self, days: int = 30) -> Dict:
        """Get performance metrics and trends"""
        cutoff_date = datetime.now() - timedelta(days=days)
        events = self._load_events_data()

        performance_events = []
        for event in events:
            try:
                event_time = datetime.fromisoformat(event['timestamp'])
                if event_time >= cutoff_date and event['event_type'] == 'performance_metric':
                    performance_events.append(event)
            except:
                continue

        metrics = defaultdict(list)
        for event in performance_events:
            metric_name = event['data'].get('metric_name')
            value = event['data'].get('value')
            if metric_name and value is not None:
                metrics[metric_name].append(value)

        # Calculate summary statistics
        summary = {}
        for metric_name, values in metrics.items():
            if values:
                summary[metric_name] = {
                    'avg': sum(values) / len(values),
                    'min': min(values),
                    'max': max(values),
                    'count': len(values),
                    'recent_trend': self._calculate_trend(values)
                }

        return summary

    def _calculate_trend(self, values: List[float]) -> str:
        """Calculate trend direction for a metric"""
        if len(values) < 2:
            return 'stable'

        # Simple trend calculation: compare first and second half
        mid = len(values) // 2
        first_half_avg = sum(values[:mid]) / mid if mid > 0 else 0
        second_half_avg = sum(values[mid:]) / (len(values) - mid) if len(values) - mid > 0 else 0

        change_threshold = 0.1 * first_half_avg if first_half_avg != 0 else 0.1

        if second_half_avg > first_half_avg + change_threshold:
            return 'improving'
        elif second_half_avg < first_half_avg - change_threshold:
            return 'declining'
        else:
            return 'stable'


class OptimizationEngine:
    """Engine for continuous improvement and optimization"""

    def __init__(self, analytics_engine: AnalyticsEngine):
        self.analytics = analytics_engine

    def get_optimization_recommendations(self) -> List[Dict]:
        """Generate optimization recommendations based on analytics"""
        recommendations = []

        # Get metrics
        suggestion_metrics = self.analytics.get_suggestion_metrics()
        behavior_patterns = self.analytics.get_user_behavior_patterns()
        performance_metrics = self.analytics.get_performance_metrics()

        # Analyze suggestion effectiveness
        for suggestion_type, metrics in suggestion_metrics.items():
            if metrics.acceptance_rate < 0.3 and metrics.total_shown > 10:
                recommendations.append({
                    'type': 'suggestion_improvement',
                    'priority': 'high',
                    'title': f'Low acceptance rate for {suggestion_type} suggestions',
                    'description': f'Only {metrics.acceptance_rate:.1%} of {suggestion_type} suggestions are accepted',
                    'suggestion': 'Review suggestion algorithm accuracy and relevance',
                    'metric': 'acceptance_rate',
                    'current_value': metrics.acceptance_rate,
                    'target_value': 0.5
                })

            if metrics.avg_confidence < 0.5 and metrics.total_shown > 5:
                recommendations.append({
                    'type': 'confidence_improvement',
                    'priority': 'medium',
                    'title': f'Low confidence in {suggestion_type} predictions',
                    'description': f'Average confidence is only {metrics.avg_confidence:.1%}',
                    'suggestion': 'Improve training data or algorithm sophistication',
                    'metric': 'confidence',
                    'current_value': metrics.avg_confidence,
                    'target_value': 0.7
                })

        # Analyze user behavior
        if behavior_patterns['avg_completion_time'] > 300:  # More than 5 minutes
            recommendations.append({
                'type': 'usability_improvement',
                'priority': 'high',
                'title': 'Long form completion times',
                'description': f'Average completion time is {behavior_patterns["avg_completion_time"]:.0f} seconds',
                'suggestion': 'Consider simplifying the form or adding more automation',
                'metric': 'completion_time',
                'current_value': behavior_patterns['avg_completion_time'],
                'target_value': 180
            })

        if behavior_patterns['avg_errors_per_form'] > 1:
            recommendations.append({
                'type': 'error_reduction',
                'priority': 'high',
                'title': 'High error rate in forms',
                'description': f'Users encounter {behavior_patterns["avg_errors_per_form"]:.1f} errors per form on average',
                'suggestion': 'Improve form validation and user guidance',
                'metric': 'error_rate',
                'current_value': behavior_patterns['avg_errors_per_form'],
                'target_value': 0.5
            })

        # Analyze performance trends
        for metric_name, data in performance_metrics.items():
            if data['recent_trend'] == 'declining':
                recommendations.append({
                    'type': 'performance_optimization',
                    'priority': 'medium',
                    'title': f'Declining {metric_name} performance',
                    'description': f'{metric_name} has been trending downward',
                    'suggestion': f'Investigate and optimize {metric_name}',
                    'metric': metric_name,
                    'current_value': data['avg'],
                    'target_value': data['max']
                })

        # Sort by priority
        priority_order = {'high': 3, 'medium': 2, 'low': 1}
        recommendations.sort(key=lambda x: priority_order.get(x['priority'], 0), reverse=True)

        return recommendations

    def get_ab_test_suggestions(self) -> List[Dict]:
        """Suggest A/B tests based on analytics"""
        suggestions = []

        behavior_patterns = self.analytics.get_user_behavior_patterns()
        suggestion_metrics = self.analytics.get_suggestion_metrics()

        # Suggest tests based on low-performing areas
        if behavior_patterns['avg_completion_time'] > 240:
            suggestions.append({
                'test_name': 'form_simplification',
                'hypothesis': 'Removing optional fields will reduce completion time',
                'metric': 'completion_time',
                'variants': ['current_form', 'simplified_form'],
                'duration_days': 14,
                'priority': 'high'
            })

        for suggestion_type, metrics in suggestion_metrics.items():
            if metrics.acceptance_rate < 0.4:
                suggestions.append({
                    'test_name': f'{suggestion_type}_algorithm_comparison',
                    'hypothesis': f'Alternative algorithm will improve {suggestion_type} acceptance',
                    'metric': 'acceptance_rate',
                    'variants': ['current_algorithm', 'enhanced_algorithm'],
                    'duration_days': 21,
                    'priority': 'medium'
                })

        return suggestions


class ReportGenerator:
    """Generate comprehensive analytics reports"""

    def __init__(self, analytics_engine: AnalyticsEngine, optimization_engine: OptimizationEngine):
        self.analytics = analytics_engine
        self.optimization = optimization_engine

    def generate_summary_report(self, days: int = 30) -> Dict:
        """Generate a comprehensive summary report"""
        suggestion_metrics = self.analytics.get_suggestion_metrics(days=days)
        behavior_patterns = self.analytics.get_user_behavior_patterns(days=days)
        performance_metrics = self.analytics.get_performance_metrics(days=days)
        recommendations = self.optimization.get_optimization_recommendations()
        ab_test_suggestions = self.optimization.get_ab_test_suggestions()

        # Calculate overall health score
        health_score = self._calculate_health_score(suggestion_metrics, behavior_patterns, performance_metrics)

        return {
            'report_date': datetime.now().isoformat(),
            'period_days': days,
            'health_score': health_score,
            'summary': {
                'total_suggestions_shown': sum(m.total_shown for m in suggestion_metrics.values()),
                'overall_acceptance_rate': self._calculate_overall_acceptance_rate(suggestion_metrics),
                'avg_completion_time': behavior_patterns['avg_completion_time'],
                'total_sessions': behavior_patterns['total_sessions'],
                'total_events': behavior_patterns['total_events']
            },
            'suggestion_metrics': {k: asdict(v) for k, v in suggestion_metrics.items()},
            'behavior_patterns': behavior_patterns,
            'performance_metrics': performance_metrics,
            'recommendations': recommendations,
            'ab_test_suggestions': ab_test_suggestions
        }

    def _calculate_health_score(self, suggestion_metrics: Dict, behavior_patterns: Dict, performance_metrics: Dict) -> float:
        """Calculate overall system health score (0-100)"""
        score = 100.0

        # Penalize low acceptance rates
        overall_acceptance = self._calculate_overall_acceptance_rate(suggestion_metrics)
        if overall_acceptance < 0.5:
            score -= (0.5 - overall_acceptance) * 40

        # Penalize long completion times
        if behavior_patterns['avg_completion_time'] > 300:
            score -= min((behavior_patterns['avg_completion_time'] - 300) / 10, 20)

        # Penalize high error rates
        if behavior_patterns['avg_errors_per_form'] > 1:
            score -= min(behavior_patterns['avg_errors_per_form'] * 10, 15)

        # Check for declining performance trends
        declining_metrics = sum(1 for data in performance_metrics.values()
                              if data.get('recent_trend') == 'declining')
        score -= declining_metrics * 5

        return max(0.0, min(score, 100.0))

    def _calculate_overall_acceptance_rate(self, suggestion_metrics: Dict) -> float:
        """Calculate overall acceptance rate across all suggestion types"""
        total_shown = sum(m.total_shown for m in suggestion_metrics.values())
        total_accepted = sum(m.total_accepted for m in suggestion_metrics.values())

        return total_accepted / total_shown if total_shown > 0 else 0.0
