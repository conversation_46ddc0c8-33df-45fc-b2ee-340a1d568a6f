import json
import os
import getpass
from datetime import datetime
from typing import List, Dict, Optional, Any
from .config import Config

class DataManager:
    def __init__(self):
        self.config = Config()
        self.username = self._get_current_user()

        # Check for SharePoint mode and user-specific directory structure
        self._setup_data_paths()
        self._ensure_data_dir()

        # Migrate legacy data if needed
        self._migrate_legacy_data()

    def _get_current_user(self) -> str:
        """Auto-detect current system user"""
        try:
            return getpass.getuser()
        except Exception:
            return 'unknown_user'

    def _setup_data_paths(self):
        """Setup data file paths based on environment (SharePoint vs local)"""
        # Check for SharePoint mode environment variables
        sharepoint_mode = os.environ.get('ADHOCLOG_SHAREPOINT_MODE', '0') == '1'
        user_data_dir = os.environ.get('ADHOCLOG_USER_DATA_DIR', '')

        if sharepoint_mode and user_data_dir and os.path.exists(user_data_dir):
            # Use new user-specific directory structure
            self.data_file = os.path.join(user_data_dir, 'tasks.json')
            self.archive_file = os.path.join(user_data_dir, 'archived_tasks.json')
            self.templates_file = os.path.join(user_data_dir, 'templates.json')
            self.user_data_dir = user_data_dir
            self.sharepoint_mode = True
            print(f"🌐 SharePoint mode: Using user directory {user_data_dir}")
        else:
            # Use legacy file structure
            self.data_file = os.path.join(self.config.DATA_DIR, f'tasks_{self.username}.json')
            self.archive_file = os.path.join(self.config.DATA_DIR, f'archived_tasks_{self.username}.json')
            self.templates_file = os.path.join(self.config.DATA_DIR, f'templates_{self.username}.json')
            self.user_data_dir = self.config.DATA_DIR
            self.sharepoint_mode = False

    def _ensure_data_dir(self):
        """Ensure data directory exists"""
        if not os.path.exists(self.user_data_dir):
            try:
                os.makedirs(self.user_data_dir, exist_ok=True)
            except PermissionError:
                print(f"⚠️ Cannot create data directory: {self.user_data_dir}")
                print("📞 Contact IT support for write access permissions")
                # Fall back to current directory
                self.user_data_dir = '.'
                self._setup_fallback_paths()

    def _setup_fallback_paths(self):
        """Setup fallback paths when data directory creation fails"""
        if self.sharepoint_mode:
            # Try to use a local temp directory
            import tempfile
            temp_dir = os.path.join(tempfile.gettempdir(), f'adhoclog_{self.username}')
            try:
                os.makedirs(temp_dir, exist_ok=True)
                self.user_data_dir = temp_dir
                self.data_file = os.path.join(temp_dir, 'tasks.json')
                self.archive_file = os.path.join(temp_dir, 'archived_tasks.json')
                self.templates_file = os.path.join(temp_dir, 'templates.json')
                print(f"📁 Using temporary directory: {temp_dir}")
            except:
                # Ultimate fallback to current directory
                self.user_data_dir = '.'
                self.data_file = f'tasks_{self.username}.json'
                self.archive_file = f'archived_tasks_{self.username}.json'
                self.templates_file = f'templates_{self.username}.json'
                print("⚠️ Using current directory for data storage")

    def _migrate_legacy_data(self):
        """Migrate existing legacy data to new structure if needed"""
        if not self.sharepoint_mode:
            return  # No migration needed for legacy mode

        # Check for existing legacy files and migrate them
        legacy_files = [
            (os.path.join(self.config.DATA_DIR, f'tasks_{self.username}.json'), self.data_file),
            (os.path.join(self.config.DATA_DIR, f'archived_tasks_{self.username}.json'), self.archive_file),
            (os.path.join(self.config.DATA_DIR, f'templates_{self.username}.json'), self.templates_file)
        ]

        migrated_files = []
        for legacy_path, new_path in legacy_files:
            if os.path.exists(legacy_path) and not os.path.exists(new_path):
                try:
                    # Create backup first
                    backup_path = f"{legacy_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    import shutil
                    shutil.copy2(legacy_path, backup_path)

                    # Copy to new location
                    shutil.copy2(legacy_path, new_path)
                    migrated_files.append(os.path.basename(legacy_path))
                    print(f"📦 Migrated: {os.path.basename(legacy_path)} -> {os.path.basename(new_path)}")
                except Exception as e:
                    print(f"⚠️ Migration failed for {legacy_path}: {e}")

        if migrated_files:
            print(f"✅ Data migration completed for {len(migrated_files)} file(s)")

    def _get_templates_file(self) -> str:
        """Get the path to user's custom templates file"""
        return self.templates_file

    def _load_tasks(self) -> List[Dict]:
        """Load tasks from JSON file"""
        if not os.path.exists(self.data_file):
            return []

        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return []

    def _save_tasks(self, tasks: List[Dict]):
        """Save tasks to JSON file"""
        with open(self.data_file, 'w', encoding='utf-8') as f:
            json.dump(tasks, f, indent=2, ensure_ascii=False)

    def _load_archived_tasks(self) -> List[Dict]:
        """Load archived tasks from JSON file"""
        if not os.path.exists(self.archive_file):
            return []

        try:
            with open(self.archive_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return []

    def _save_archived_tasks(self, archived_tasks: List[Dict]):
        """Save archived tasks to JSON file"""
        with open(self.archive_file, 'w', encoding='utf-8') as f:
            json.dump(archived_tasks, f, indent=2, ensure_ascii=False)

    def _get_next_id(self, tasks: List[Dict]) -> int:
        """Get next available ID"""
        if not tasks:
            return 1
        return max(task.get('id', 0) for task in tasks) + 1

    def get_all_tasks(self) -> List[Dict]:
        """Get all tasks"""
        return self._load_tasks()

    def get_task_by_id(self, task_id: int) -> Optional[Dict]:
        """Get a specific task by ID"""
        tasks = self._load_tasks()
        for task in tasks:
            if task.get('id') == task_id:
                return task
        return None

    def add_task(self, task_data: Dict) -> Dict:
        """Add a new task"""
        tasks = self._load_tasks()

        # Set auto-generated fields
        task_data['id'] = self._get_next_id(tasks)
        task_data['team_member'] = self.username

        # Set default date if not provided
        if not task_data.get('date'):
            task_data['date'] = datetime.now().strftime('%Y-%m-%d')

        # Auto-map category from classification
        classification = task_data.get('classification', '')
        task_data['category'] = self.config.CLASSIFICATION_MAPPING.get(classification, 'Other')

        tasks.append(task_data)
        self._save_tasks(tasks)
        return task_data

    def update_task(self, task_id: int, task_data: Dict) -> Optional[Dict]:
        """Update an existing task"""
        tasks = self._load_tasks()

        for i, task in enumerate(tasks):
            if task.get('id') == task_id:
                # Preserve ID and team_member
                task_data['id'] = task_id
                task_data['team_member'] = task.get('team_member', self.username)

                # Auto-map category from classification
                classification = task_data.get('classification', '')
                task_data['category'] = self.config.CLASSIFICATION_MAPPING.get(classification, 'Other')

                tasks[i] = task_data
                self._save_tasks(tasks)
                return task_data

        return None

    def delete_task(self, task_id: int) -> bool:
        """Archive a task (soft delete)"""
        tasks = self._load_tasks()
        archived_tasks = self._load_archived_tasks()

        # Find the task to archive
        task_to_archive = None
        for task in tasks:
            if task.get('id') == task_id:
                task_to_archive = task
                break

        if task_to_archive:
            # Add timestamp when archived
            task_to_archive['archived_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # Remove from active tasks
            tasks = [task for task in tasks if task.get('id') != task_id]

            # Add to archived tasks
            archived_tasks.append(task_to_archive)

            # Save both files
            self._save_tasks(tasks)
            self._save_archived_tasks(archived_tasks)
            return True

        return False

    def get_archived_tasks(self) -> List[Dict]:
        """Get all archived tasks"""
        archived_tasks = self._load_archived_tasks()
        # Sort by archived date (newest first)
        return sorted(archived_tasks, key=lambda x: x.get('archived_date', ''), reverse=True)

    def restore_task(self, task_id: int) -> bool:
        """Restore a task from archive"""
        tasks = self._load_tasks()
        archived_tasks = self._load_archived_tasks()

        # Find the task to restore
        task_to_restore = None
        for task in archived_tasks:
            if task.get('id') == task_id:
                task_to_restore = task
                break

        if task_to_restore:
            # Remove archived_date field
            if 'archived_date' in task_to_restore:
                del task_to_restore['archived_date']

            # Remove from archived tasks
            archived_tasks = [task for task in archived_tasks if task.get('id') != task_id]

            # Add back to active tasks
            tasks.append(task_to_restore)

            # Save both files
            self._save_tasks(tasks)
            self._save_archived_tasks(archived_tasks)
            return True

        return False

    def permanently_delete_task(self, task_id: int) -> bool:
        """Permanently delete a task from archive"""
        archived_tasks = self._load_archived_tasks()
        original_length = len(archived_tasks)

        archived_tasks = [task for task in archived_tasks if task.get('id') != task_id]

        if len(archived_tasks) < original_length:
            self._save_archived_tasks(archived_tasks)
            return True
        return False

    def bulk_archive_tasks(self, task_ids: List[int]) -> Dict[str, Any]:
        """Archive multiple tasks"""
        if not task_ids:
            return {'success': False, 'message': 'No tasks specified', 'archived_count': 0}

        tasks = self._load_tasks()
        archived_tasks = self._load_archived_tasks()

        archived_count = 0
        archived_titles = []

        # Find tasks to archive
        tasks_to_archive = []
        for task in tasks[:]:  # Create a copy to iterate safely
            if task.get('id') in task_ids:
                # Add archived_date field
                task['archived_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                tasks_to_archive.append(task)
                archived_titles.append(task.get('title', 'Unknown'))
                tasks.remove(task)
                archived_count += 1

        # Add to archived tasks
        archived_tasks.extend(tasks_to_archive)

        # Save both files
        if archived_count > 0:
            self._save_tasks(tasks)
            self._save_archived_tasks(archived_tasks)

        return {
            'success': archived_count > 0,
            'archived_count': archived_count,
            'archived_titles': archived_titles,
            'message': f'Successfully archived {archived_count} task(s)' if archived_count > 0 else 'No tasks were archived'
        }

    def bulk_restore_tasks(self, task_ids: List[int]) -> Dict[str, Any]:
        """Restore multiple tasks from archive"""
        if not task_ids:
            return {'success': False, 'message': 'No tasks specified', 'restored_count': 0}

        tasks = self._load_tasks()
        archived_tasks = self._load_archived_tasks()

        restored_count = 0
        restored_titles = []

        # Find tasks to restore
        tasks_to_restore = []
        for task in archived_tasks[:]:  # Create a copy to iterate safely
            if task.get('id') in task_ids:
                # Remove archived_date field
                if 'archived_date' in task:
                    del task['archived_date']
                tasks_to_restore.append(task)
                restored_titles.append(task.get('title', 'Unknown'))
                archived_tasks.remove(task)
                restored_count += 1

        # Add back to active tasks
        tasks.extend(tasks_to_restore)

        # Save both files
        if restored_count > 0:
            self._save_tasks(tasks)
            self._save_archived_tasks(archived_tasks)

        return {
            'success': restored_count > 0,
            'restored_count': restored_count,
            'restored_titles': restored_titles,
            'message': f'Successfully restored {restored_count} task(s)' if restored_count > 0 else 'No tasks were restored'
        }

    def bulk_permanently_delete_tasks(self, task_ids: List[int]) -> Dict[str, Any]:
        """Permanently delete multiple tasks from archive"""
        if not task_ids:
            return {'success': False, 'message': 'No tasks specified', 'deleted_count': 0}

        archived_tasks = self._load_archived_tasks()

        deleted_count = 0
        deleted_titles = []

        # Find tasks to delete
        for task in archived_tasks[:]:  # Create a copy to iterate safely
            if task.get('id') in task_ids:
                deleted_titles.append(task.get('title', 'Unknown'))
                archived_tasks.remove(task)
                deleted_count += 1

        # Save archived tasks file
        if deleted_count > 0:
            self._save_archived_tasks(archived_tasks)

        return {
            'success': deleted_count > 0,
            'deleted_count': deleted_count,
            'deleted_titles': deleted_titles,
            'message': f'Successfully deleted {deleted_count} task(s)' if deleted_count > 0 else 'No tasks were deleted'
        }

    def filter_tasks(self, filters: Dict, page: int = 1, per_page: int = 10) -> Dict:
        """Filter tasks based on criteria with pagination"""
        tasks = self._load_tasks()
        filtered_tasks = tasks

        # Filter by date range
        if filters.get('start_date'):
            filtered_tasks = [t for t in filtered_tasks if t.get('date', '') >= filters['start_date']]

        if filters.get('end_date'):
            filtered_tasks = [t for t in filtered_tasks if t.get('date', '') <= filters['end_date']]

        # Filter by classification
        if filters.get('classification'):
            filtered_tasks = [t for t in filtered_tasks if t.get('classification') == filters['classification']]

        # Filter by search term (title or description)
        if filters.get('search'):
            search_term = filters['search'].lower()
            filtered_tasks = [
                t for t in filtered_tasks
                if search_term in t.get('title', '').lower() or search_term in t.get('description', '').lower()
            ]

        # Sort by date (newest first), then by ID (newest first)
        sorted_tasks = sorted(filtered_tasks, key=lambda x: (x.get('date', ''), x.get('id', 0)), reverse=True)

        # Calculate pagination
        total_tasks = len(sorted_tasks)
        total_pages = (total_tasks + per_page - 1) // per_page  # Ceiling division
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page

        paginated_tasks = sorted_tasks[start_idx:end_idx]

        return {
            'tasks': paginated_tasks,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_tasks,
                'total_pages': total_pages,
                'has_prev': page > 1,
                'has_next': page < total_pages,
                'prev_page': page - 1 if page > 1 else None,
                'next_page': page + 1 if page < total_pages else None
            }
        }

    def get_archived_tasks_paginated(self, page: int = 1, per_page: int = 10) -> Dict:
        """Get archived tasks with pagination"""
        archived_tasks = self._load_archived_tasks()
        # Sort by archived date (newest first)
        sorted_tasks = sorted(archived_tasks, key=lambda x: x.get('archived_date', ''), reverse=True)

        # Calculate pagination
        total_tasks = len(sorted_tasks)
        total_pages = (total_tasks + per_page - 1) // per_page  # Ceiling division
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page

        paginated_tasks = sorted_tasks[start_idx:end_idx]

        return {
            'tasks': paginated_tasks,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_tasks,
                'total_pages': total_pages,
                'has_prev': page > 1,
                'has_next': page < total_pages,
                'prev_page': page - 1 if page > 1 else None,
                'next_page': page + 1 if page < total_pages else None
            }
        }

    # Custom Template Management (Phase 2)
    def _load_custom_templates(self) -> List[Dict]:
        """Load custom templates from JSON file"""
        if not os.path.exists(self.templates_file):
            return []

        try:
            with open(self.templates_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return []

    def _save_custom_templates(self, templates: List[Dict]):
        """Save custom templates to JSON file"""
        with open(self.templates_file, 'w', encoding='utf-8') as f:
            json.dump(templates, f, indent=2, ensure_ascii=False)

    def get_custom_templates(self) -> List[Dict]:
        """Get all custom templates for the current user"""
        return self._load_custom_templates()

    def create_custom_template(self, template_data: Dict) -> str:
        """Create a new custom template"""
        templates = self._load_custom_templates()

        # Generate unique ID
        template_id = str(len(templates) + 1)
        while any(t.get('id') == template_id for t in templates):
            template_id = str(int(template_id) + 1)

        template_data['id'] = template_id
        templates.append(template_data)
        self._save_custom_templates(templates)
        return template_id

    def update_custom_template(self, template_id: str, update_data: Dict) -> bool:
        """Update an existing custom template"""
        templates = self._load_custom_templates()

        for template in templates:
            if template.get('id') == template_id:
                template.update(update_data)
                template['updated_at'] = datetime.now().isoformat()
                self._save_custom_templates(templates)
                return True

        return False

    def delete_custom_template(self, template_id: str) -> bool:
        """Delete a custom template"""
        templates = self._load_custom_templates()
        original_count = len(templates)

        templates = [t for t in templates if t.get('id') != template_id]

        if len(templates) < original_count:
            self._save_custom_templates(templates)
            return True

        return False

    def increment_template_usage(self, template_id: str) -> bool:
        """Increment usage count for a template"""
        templates = self._load_custom_templates()

        for template in templates:
            if template.get('id') == template_id:
                template['usage_count'] = template.get('usage_count', 0) + 1
                template['last_used'] = datetime.now().isoformat()
                self._save_custom_templates(templates)
                return True

        return False

    def get_current_user(self) -> str:
        """Get the current username"""
        return self.username

    def get_category_for_classification(self, classification: str) -> str:
        """Get the mapped category for a classification"""
        return self.config.CLASSIFICATION_MAPPING.get(classification, classification)

    # Pattern Recognition & Learning (Phase 2)
    def analyze_user_patterns(self) -> Dict:
        """Analyze user task creation patterns for intelligent suggestions"""
        tasks = self._load_tasks()
        archived_tasks = self._load_archived_tasks()
        all_tasks = tasks + archived_tasks

        if not all_tasks:
            return {}

        patterns = {
            'classification_frequency': {},
            'title_keywords': {},
            'time_patterns': {},
            'duration_patterns': {},
            'recent_classifications': [],
            'common_descriptions': {}
        }

        # Analyze classification frequency
        for task in all_tasks:
            classification = task.get('classification', '')
            patterns['classification_frequency'][classification] = patterns['classification_frequency'].get(classification, 0) + 1

            # Track recent classifications (last 20 tasks)
            if len(patterns['recent_classifications']) < 20:
                patterns['recent_classifications'].append(classification)

        # Analyze title keywords
        for task in all_tasks:
            title = task.get('title', '').lower()
            words = title.split()
            for word in words:
                if len(word) > 3:  # Only meaningful words
                    patterns['title_keywords'][word] = patterns['title_keywords'].get(word, 0) + 1

        # Analyze time patterns (hour of day when tasks are created)
        for task in all_tasks:
            created_at = task.get('created_at', '')
            if created_at:
                try:
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    hour = dt.hour
                    patterns['time_patterns'][hour] = patterns['time_patterns'].get(hour, 0) + 1
                except:
                    pass

        # Analyze duration patterns by classification
        for task in all_tasks:
            classification = task.get('classification', '')
            est_time = task.get('est_time', 0)
            if classification and est_time:
                if classification not in patterns['duration_patterns']:
                    patterns['duration_patterns'][classification] = []
                patterns['duration_patterns'][classification].append(est_time)

        # Calculate average durations
        for classification in patterns['duration_patterns']:
            durations = patterns['duration_patterns'][classification]
            if durations:
                patterns['duration_patterns'][classification] = {
                    'average': sum(durations) / len(durations),
                    'min': min(durations),
                    'max': max(durations),
                    'count': len(durations)
                }

        # Analyze common description patterns
        for task in all_tasks:
            description = task.get('description', '').lower()
            if description and len(description) > 10:
                # Extract key phrases (simplified approach)
                phrases = description.split('.')
                for phrase in phrases:
                    phrase = phrase.strip()
                    if len(phrase) > 5:
                        patterns['common_descriptions'][phrase] = patterns['common_descriptions'].get(phrase, 0) + 1

        return patterns

    def predict_classification(self, title: str, description: str = '') -> Dict:
        """Predict classification based on title and description using user patterns"""
        patterns = self.analyze_user_patterns()

        if not patterns.get('title_keywords'):
            return {'classification': None, 'confidence': 0}

        title_lower = title.lower()
        description_lower = description.lower()
        text_to_analyze = f"{title_lower} {description_lower}".strip()

        classification_scores = {}

        # Score based on keyword frequency
        for word in text_to_analyze.split():
            if len(word) > 3 and word in patterns['title_keywords']:
                # Find which classifications commonly use this word
                for task in self._load_tasks() + self._load_archived_tasks():
                    if word in task.get('title', '').lower():
                        classification = task.get('classification', '')
                        classification_scores[classification] = classification_scores.get(classification, 0) + 1

        # Boost score based on recent usage
        for classification in patterns.get('recent_classifications', [])[:10]:  # Recent 10
            classification_scores[classification] = classification_scores.get(classification, 0) + 2

        if not classification_scores:
            # Fallback to most frequent classification
            freq = patterns.get('classification_frequency', {})
            most_common = max(freq.items(), key=lambda x: x[1]) if freq else (None, 0)
            return {'classification': most_common[0], 'confidence': 0.3}

        # Get the highest scoring classification
        best_classification = max(classification_scores.items(), key=lambda x: x[1])
        total_score = sum(classification_scores.values())
        confidence = best_classification[1] / total_score if total_score > 0 else 0

        return {
            'classification': best_classification[0],
            'confidence': min(confidence, 0.9),  # Cap confidence at 90%
            'alternatives': sorted(classification_scores.items(), key=lambda x: x[1], reverse=True)[:3]
        }

    def estimate_duration(self, title: str, classification: str = '', description: str = '') -> Dict:
        """Estimate task duration based on user patterns with improved title matching"""
        patterns = self.analyze_user_patterns()

        # Look for exact or similar title matches first
        all_tasks = self._load_tasks() + self._load_archived_tasks()
        exact_matches = []
        similar_matches = []

        title_lower = title.lower()

        for task in all_tasks:
            task_title_lower = task.get('title', '').lower()

            # Exact title match
            if task_title_lower == title_lower:
                exact_matches.append(task)
            # Similarity match (contains key words or high overlap)
            elif self._calculate_title_similarity(title_lower, task_title_lower) > 0.6:
                similar_matches.append(task)

        # Use exact matches first
        if exact_matches:
            durations = [task.get('est_time', 30) for task in exact_matches if task.get('est_time')]
            if durations:
                avg_duration = sum(durations) / len(durations)
                return {
                    'estimated_time': int(round(avg_duration / 5) * 5),  # Round to nearest 5
                    'confidence': 0.9,
                    'based_on': f"{len(exact_matches)} identical task(s)"
                }

        # Use similar matches next
        if similar_matches:
            durations = [task.get('est_time', 30) for task in similar_matches if task.get('est_time')]
            if durations:
                avg_duration = sum(durations) / len(durations)
                return {
                    'estimated_time': int(round(avg_duration / 5) * 5),
                    'confidence': 0.8,
                    'based_on': f"{len(similar_matches)} similar task(s)"
                }

        # Fall back to classification-based average
        duration_info = patterns.get('duration_patterns', {}).get(classification, {})
        estimated_time = duration_info.get('average', 30) if duration_info else 30

        # Adjust based on title keywords
        keyword_adjustments = {
            'meeting': 1.2,
            'standup': 0.5,
            'daily standup': 0.5,
            'review': 1.5,
            'planning': 2.0,
            'research': 2.5,
            'quick': 0.3,
            'brief': 0.4,
            'urgent': 0.8,
            'detailed': 1.8,
            'analysis': 2.0,
            'report': 1.5,
            'documentation': 1.8,
            'presentation': 1.3,
            'training': 2.2,
            'troubleshoot': 1.6,
            'debug': 1.4,
            'fix': 0.8,
            'update': 0.7,
            'create': 1.5,
            'develop': 2.5,
            'implement': 2.0
        }

        adjustment_factor = 1.0
        for keyword, factor in keyword_adjustments.items():
            if keyword in title_lower:
                adjustment_factor *= factor

        estimated_time *= adjustment_factor

        # Round to nearest 5 minutes
        estimated_time = round(estimated_time / 5) * 5
        estimated_time = max(5, min(480, estimated_time))  # Between 5 minutes and 8 hours

        return {
            'estimated_time': int(estimated_time),
            'confidence': 0.7 if duration_info else 0.4,
            'based_on': f"{duration_info.get('count', 0)} {classification} tasks" if duration_info else "keyword analysis"
        }

    def get_title_suggestions(self, partial_title: str, limit: int = 5) -> Dict:
        """Get suggestions based on similar titles and descriptions"""
        if len(partial_title) < 2:
            return {'suggestions': []}

        all_tasks = self._load_tasks() + self._load_archived_tasks()
        partial_lower = partial_title.lower()

        # Find matching tasks
        title_matches = []
        description_matches = []

        for task in all_tasks:
            title = task.get('title', '')
            description = task.get('description', '')

            # Title matches
            if partial_lower in title.lower():
                similarity = self._calculate_title_similarity(partial_lower, title.lower())
                title_matches.append({
                    'task': task,
                    'similarity': similarity,
                    'match_type': 'title'
                })

            # Description matches (for context)
            elif partial_lower in description.lower():
                similarity = len(partial_lower) / len(description) if description else 0
                description_matches.append({
                    'task': task,
                    'similarity': similarity,
                    'match_type': 'description'
                })

        # Sort by similarity and combine
        title_matches.sort(key=lambda x: x['similarity'], reverse=True)
        description_matches.sort(key=lambda x: x['similarity'], reverse=True)

        # Take top matches
        suggestions = []
        seen_titles = set()

        # Add title matches first
        for match in title_matches[:limit]:
            task = match['task']
            title = task.get('title', '')
            if title not in seen_titles:
                suggestions.append({
                    'title': title,
                    'classification': task.get('classification', ''),
                    'description': task.get('description', ''),
                    'est_time': task.get('est_time', 30),
                    'match_type': match['match_type'],
                    'similarity': match['similarity'],
                    'usage_count': sum(1 for t in all_tasks if t.get('title', '').lower() == title.lower())
                })
                seen_titles.add(title)

        # Add description matches if we need more
        for match in description_matches[:limit - len(suggestions)]:
            task = match['task']
            title = task.get('title', '')
            if title not in seen_titles:
                suggestions.append({
                    'title': title,
                    'classification': task.get('classification', ''),
                    'description': task.get('description', ''),
                    'est_time': task.get('est_time', 30),
                    'match_type': match['match_type'],
                    'similarity': match['similarity'],
                    'usage_count': sum(1 for t in all_tasks if t.get('title', '').lower() == title.lower())
                })
                seen_titles.add(title)

        return {
            'suggestions': suggestions[:limit],
            'total_matches': len(title_matches) + len(description_matches)
        }

    def _calculate_title_similarity(self, title1: str, title2: str) -> float:
        """Calculate similarity between two titles using word overlap"""
        words1 = set(title1.split())
        words2 = set(title2.split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union) if union else 0.0

    def get_contextual_suggestions(self, current_time: Optional[datetime] = None) -> Dict:
        """Get contextual task suggestions based on time and patterns"""
        if current_time is None:
            current_time = datetime.now()

        patterns = self.analyze_user_patterns()
        current_hour = current_time.hour
        current_weekday = current_time.weekday()  # 0 = Monday

        suggestions = {
            'time_based': [],
            'pattern_based': [],
            'recent_similar': []
        }

        # Time-based suggestions
        time_patterns = patterns.get('time_patterns', {})
        if current_hour in time_patterns:
            # Suggest common task types for this time
            tasks_at_this_time = []
            for task in self._load_tasks() + self._load_archived_tasks():
                created_at = task.get('created_at', '')
                if created_at:
                    try:
                        dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                        if dt.hour == current_hour:
                            tasks_at_this_time.append(task)
                    except:
                        pass

            # Group by classification
            time_classifications = {}
            for task in tasks_at_this_time[-10:]:  # Last 10 tasks at this time
                classification = task.get('classification', '')
                if classification:
                    time_classifications[classification] = time_classifications.get(classification, 0) + 1

            for classification, count in sorted(time_classifications.items(), key=lambda x: x[1], reverse=True)[:3]:
                # Get average time for this classification at this hour
                classification_tasks = [t for t in tasks_at_this_time if t.get('classification') == classification]
                avg_time = 30
                if classification_tasks:
                    times = [t.get('est_time', 30) for t in classification_tasks if t.get('est_time')]
                    if times:
                        avg_time = sum(times) // len(times)

                suggestions['time_based'].append({
                    'title': f"{classification} task",
                    'classification': classification,
                    'est_time': avg_time,
                    'description': f"You often work on {classification} tasks at {current_hour}:00",
                    'type': 'time_based',
                    'confidence': min(count / 10.0, 0.8)
                })

        # Pattern-based suggestions (frequently used task titles and patterns)
        freq = patterns.get('classification_frequency', {})
        all_tasks = self._load_tasks() + self._load_archived_tasks()

        # Find frequently used task titles (used more than once)
        title_frequency = {}
        for task in all_tasks:
            title = task.get('title', '').strip()
            if title and len(title) > 5:  # Only meaningful titles
                title_frequency[title] = title_frequency.get(title, 0) + 1

        # Get frequently used titles (used 2+ times)
        frequent_titles = [(title, count) for title, count in title_frequency.items() if count >= 2]
        frequent_titles.sort(key=lambda x: x[1], reverse=True)

        for title, count in frequent_titles[:3]:  # Top 3 frequently used titles
            # Find the most recent task with this title to get details
            matching_tasks = [t for t in all_tasks if t.get('title', '').strip() == title]
            if matching_tasks:
                latest_task = max(matching_tasks, key=lambda x: x.get('id', 0))
                avg_time = sum(t.get('est_time', 30) for t in matching_tasks if t.get('est_time')) // len(matching_tasks)

                suggestions['pattern_based'].append({
                    'title': title,
                    'classification': latest_task.get('classification', ''),
                    'est_time': avg_time,
                    'description': latest_task.get('description', ''),
                    'type': 'frequent_title',
                    'usage_count': count,
                    'confidence': min(count / max(title_frequency.values()), 0.95)
                })

        # Recently trending task patterns (tasks from last 7 days with similar patterns)
        from datetime import timedelta
        week_ago = (current_time - timedelta(days=7)).strftime('%Y-%m-%d')
        recent_tasks = [t for t in all_tasks if t.get('date', '') >= week_ago]

        if recent_tasks:
            # Find common patterns in recent tasks
            recent_title_patterns = {}
            for task in recent_tasks:
                title = task.get('title', '').lower()
                # Extract meaningful keywords (3+ chars)
                keywords = [word for word in title.split() if len(word) >= 3]
                for keyword in keywords:
                    if keyword not in recent_title_patterns:
                        recent_title_patterns[keyword] = []
                    recent_title_patterns[keyword].append(task)

            # Find keywords that appear in multiple recent tasks
            trending_patterns = [(keyword, tasks) for keyword, tasks in recent_title_patterns.items()
                               if len(tasks) >= 2]
            trending_patterns.sort(key=lambda x: len(x[1]), reverse=True)

            for keyword, tasks in trending_patterns[:2]:  # Top 2 trending patterns
                # Get the most recent task with this pattern
                latest_task = max(tasks, key=lambda x: x.get('date', ''))
                avg_time = sum(t.get('est_time', 30) for t in tasks if t.get('est_time')) // len(tasks)

                suggestions['recent_similar'].append({
                    'title': latest_task.get('title', ''),
                    'classification': latest_task.get('classification', ''),
                    'description': latest_task.get('description', ''),
                    'est_time': avg_time,
                    'type': 'trending',
                    'keyword': keyword,
                    'trend_count': len(tasks),
                    'confidence': min(len(tasks) / len(recent_tasks), 0.8)
                })

        return suggestions
