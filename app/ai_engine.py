"""
AI Engine for Enhanced Task Management
Phase 3: AI-Powered Suggestions and Advanced Analytics

This module provides sophisticated AI capabilities for task management including:
- Enhanced Natural Language Processing
- Predictive Task Suggestions
- Priority Detection
- Task Relationship Analysis
- Workload Optimization

All processing is done locally without external API calls for privacy and performance.
"""

import re
import json
import math
from collections import Counter, defaultdict
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Set
from dataclasses import dataclass


@dataclass
class TaskInsight:
    """Data class for task analysis insights"""
    priority_score: float
    urgency_level: str
    complexity_score: float
    similar_tasks: List[Dict]
    predicted_duration: int
    confidence: float


class EnhancedNLPEngine:
    """Enhanced Natural Language Processing engine for task analysis"""

    def __init__(self):
        # Priority detection keywords with weights
        self.priority_keywords = {
            'urgent': 1.0, 'asap': 1.0, 'critical': 0.9, 'emergency': 1.0,
            'important': 0.7, 'high': 0.6, 'priority': 0.6, 'deadline': 0.8,
            'rush': 0.8, 'immediate': 0.9, 'soon': 0.5, 'quick': 0.4,
            'fast': 0.4, 'now': 0.7, 'today': 0.6, 'tomorrow': 0.4,
            'crash': 0.9, 'down': 0.7, 'broken': 0.8, 'issue': 0.3,
            'problem': 0.4, 'bug': 0.5, 'fix': 0.5, 'needed': 0.3
        }

        # Complexity indicators
        self.complexity_keywords = {
            'complex': 0.8, 'complicated': 0.7, 'difficult': 0.6, 'challenging': 0.6,
            'extensive': 0.7, 'detailed': 0.5, 'comprehensive': 0.6, 'thorough': 0.5,
            'research': 0.6, 'analysis': 0.6, 'review': 0.4, 'simple': -0.4,
            'quick': -0.3, 'easy': -0.4, 'basic': -0.3, 'straightforward': -0.4
        }

        # Action verbs and their typical durations (in minutes)
        self.action_patterns = {
            'create': 45, 'build': 60, 'develop': 90, 'design': 60, 'implement': 120,
            'review': 30, 'analyze': 45, 'research': 60, 'update': 30, 'fix': 45,
            'test': 30, 'debug': 60, 'optimize': 45, 'refactor': 75, 'document': 30,
            'plan': 30, 'schedule': 15, 'organize': 20, 'prepare': 25, 'setup': 20,
            'configure': 30, 'install': 20, 'deploy': 45, 'migrate': 60, 'backup': 15,
            'submit': 20, 'send': 10, 'write': 30, 'generate': 45, 'process': 60,
            'monitor': 30, 'maintain': 45, 'coordinate': 25, 'execute': 60,
            'workshop': 240, 'meeting': 30, 'standup': 15, 'sync': 20
        }

        # Time expressions and their minute values
        self.time_expressions = {
            r'\b(\d+)\s*(?:hour|hr|h)\b': lambda m: int(m.group(1)) * 60,
            r'\b(\d+)\s*(?:minute|min|m)\b': lambda m: int(m.group(1)),
            r'\b(\d+)\s*(?:day|days)\b': lambda m: int(m.group(1)) * 480,  # 8 hours
            r'\bhalf\s*(?:hour|hr)\b': lambda m: 30,
            r'\bquarter\s*(?:hour|hr)\b': lambda m: 15,
            r'\b(\d+)-hour\b': lambda m: int(m.group(1)) * 60,  # Handle "2-hour" format
            r'\b(\d+)-min\b': lambda m: int(m.group(1)),  # Handle "15-min" format
        }

        # Stop words to ignore in similarity calculations
        self.stop_words = {
            'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
            'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
            'to', 'was', 'with', 'will', 'would', 'could', 'should', 'might'
        }

    def analyze_task_text(self, title: str, description: str = '') -> TaskInsight:
        """Comprehensive analysis of task text"""
        text = f"{title} {description}".lower()

        # Validate input quality - require meaningful input for accurate analysis
        if len(title.strip()) < 3:
            return TaskInsight(
                priority_score=0.0,
                urgency_level='unknown',
                complexity_score=0.0,
                similar_tasks=[],
                predicted_duration=30,  # Default safe estimate
                confidence=0.05  # Very low confidence for minimal input
            )

        # Calculate priority score
        priority_score = self._calculate_priority_score(text)
        urgency_level = self._determine_urgency_level(priority_score)

        # Calculate complexity score
        complexity_score = self._calculate_complexity_score(text)

        # Extract time estimates from text
        predicted_duration = self._extract_time_estimate(text, title)

        # Calculate confidence based on text length and keyword matches
        confidence = self._calculate_confidence(text, title)

        return TaskInsight(
            priority_score=priority_score,
            urgency_level=urgency_level,
            complexity_score=complexity_score,
            similar_tasks=[],  # Will be populated by caller
            predicted_duration=predicted_duration,
            confidence=confidence
        )

    def _calculate_priority_score(self, text: str) -> float:
        """Calculate priority score based on keywords and patterns"""
        score = 0.1  # Small baseline to avoid zero scores
        words = text.split()

        for word in words:
            if word in self.priority_keywords:
                score += self.priority_keywords[word]

        # Check for time-sensitive patterns
        if re.search(r'\bdue\s+(today|tomorrow|this\s+week)\b', text):
            score += 0.8
        if re.search(r'\bdeadline\b', text):
            score += 0.7
        if re.search(r'\b(before|by)\s+\d+', text):
            score += 0.6

        # Default priority boost for common work patterns
        if any(word in ['ce', 'platform', 'project', 'update', 'review'] for word in words):
            score += 0.2  # Small boost for common work tasks

        # Meetings and standups are typically medium priority
        if any(word in ['standup', 'stand-up', 'meeting', 'sync'] for word in words):
            score += 0.4

        # Normalize score (0-1 range)
        return min(score / 2.0, 1.0)

    def _determine_urgency_level(self, priority_score: float) -> str:
        """Determine urgency level from priority score"""
        if priority_score >= 0.8:
            return 'high'
        elif priority_score >= 0.5:
            return 'medium'
        elif priority_score >= 0.2:
            return 'low'
        elif priority_score > 0:
            return 'normal'
        else:
            return 'unknown'

    def _calculate_complexity_score(self, text: str) -> float:
        """Calculate complexity score based on indicators"""
        score = 0.5  # Baseline neutral score
        words = text.split()

        for word in words:
            if word in self.complexity_keywords:
                score += self.complexity_keywords[word] * 0.2

        # Text length indicator (longer = potentially more complex)
        if len(text) > 100:
            score += 0.2
        elif len(text) > 50:
            score += 0.1

        # Simple task indicators
        simple_indicators = ['quick', 'simple', 'brief', 'short', 'update', 'send', 'email']
        if any(indicator in text for indicator in simple_indicators):
            score -= 0.3

        # Complex task indicators
        complex_indicators = ['comprehensive', 'architecture', 'migration', 'analysis', 'implementation', 'integration']
        if any(indicator in text for indicator in complex_indicators):
            score += 0.3

        # Technical terms indicator
        if re.search(r'\b(api|database|integration|algorithm|optimization|architecture|migration)\b', text):
            score += 0.2

        return max(0.0, min(score, 1.0))

    def _extract_time_estimate(self, text: str, title: str) -> int:
        """Extract time estimate from text or predict based on action verbs"""
        # First, try to find explicit time mentions
        for pattern, extractor in self.time_expressions.items():
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return extractor(match)

        # Look for deadline/urgency patterns that suggest longer tasks
        if re.search(r'\bby\s+\d+\s*(pm|am)\b|\bdeadline\b|\bsubmit\b|\breport\b', text, re.IGNORECASE):
            if 'report' in text.lower():
                return 60  # Reports typically take longer
            return 45  # Deadline tasks typically need more time

        # If no explicit time, predict based on action verbs
        title_words = title.lower().split()
        for word in title_words:
            if word in self.action_patterns:
                return self.action_patterns[word]

        # Look for task type indicators
        if any(word in text.lower() for word in ['report', 'analysis', 'comprehensive']):
            return 60
        if any(word in text.lower() for word in ['quick', 'brief', 'update', 'email']):
            return 15

        # Default based on text length and complexity
        if len(text) > 100:
            return 60  # 1 hour for complex tasks
        elif len(text) > 50:
            return 45  # 45 minutes for medium tasks
        else:
            return 30  # 30 minutes for simple tasks

    def _calculate_confidence(self, text: str, title: str) -> float:
        """Calculate confidence score for predictions"""
        confidence = 0.2  # Lower baseline for more conservative scoring

        # Require meaningful text length for higher confidence
        title_words = len(title.strip().split())
        text_length = len(text)

        # More conservative confidence increases
        if title_words >= 2:
            confidence += 0.1
        if title_words >= 3:
            confidence += 0.1
        if title_words >= 5:
            confidence += 0.1
        if text_length > 50:
            confidence += 0.15
        if text_length > 100:
            confidence += 0.1

        # Presence of keywords increases confidence, but conservatively
        word_count = len(text.split())
        keyword_matches = sum(1 for word in text.split()
                            if word in self.priority_keywords or word in self.action_patterns)

        if word_count > 0:
            keyword_ratio = keyword_matches / word_count
            confidence += keyword_ratio * 0.1  # Reduced impact

        # Check for complete sentences/descriptions
        if '.' in text or len(text.split()) > 8:
            confidence += 0.1

        # More conservative cap for typical inputs
        return min(confidence, 0.8)

    def calculate_semantic_similarity(self, text1: str, text2: str) -> float:
        """Calculate semantic similarity between two texts using enhanced algorithms"""
        # Normalize texts
        text1 = self._normalize_text(text1)
        text2 = self._normalize_text(text2)

        # Get word sets excluding stop words
        words1 = set(word for word in text1.split() if word not in self.stop_words and len(word) > 2)
        words2 = set(word for word in text2.split() if word not in self.stop_words and len(word) > 2)

        if not words1 or not words2:
            return 0.0

        # Jaccard similarity
        intersection = len(words1 & words2)
        union = len(words1 | words2)
        jaccard = intersection / union if union > 0 else 0.0

        # N-gram similarity for better matching
        bigrams1 = self._get_ngrams(text1, 2)
        bigrams2 = self._get_ngrams(text2, 2)

        if bigrams1 and bigrams2:
            bigram_intersection = len(bigrams1 & bigrams2)
            bigram_union = len(bigrams1 | bigrams2)
            bigram_similarity = bigram_intersection / bigram_union if bigram_union > 0 else 0.0
        else:
            bigram_similarity = 0.0

        # Weighted combination
        return 0.7 * jaccard + 0.3 * bigram_similarity

    def _normalize_text(self, text: str) -> str:
        """Normalize text for similarity comparison"""
        # Convert to lowercase and remove special characters
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        return text

    def _get_ngrams(self, text: str, n: int) -> Set[str]:
        """Get n-grams from text"""
        words = text.split()
        if len(words) < n:
            return set()
        return set(' '.join(words[i:i+n]) for i in range(len(words) - n + 1))


class PredictiveEngine:
    """Predictive analytics engine for task management"""

    def __init__(self, nlp_engine: EnhancedNLPEngine):
        self.nlp_engine = nlp_engine

    def predict_next_tasks(self, user_tasks: List[Dict], current_time: Optional[datetime] = None) -> List[Dict]:
        """Predict next likely tasks based on user patterns"""
        if current_time is None:
            current_time = datetime.now()

        # Need sufficient task history for meaningful predictions
        if len(user_tasks) < 5:
            return self._get_default_predictions()

        # Analyze task sequences
        task_sequences = self._analyze_task_sequences(user_tasks)

        # Analyze time patterns
        time_patterns = self._analyze_time_patterns(user_tasks, current_time)

        # Combine predictions
        predictions = []

        # Sequence-based predictions (only high-confidence ones)
        for sequence in task_sequences[:2]:  # Reduced from 3
            if sequence['confidence'] > 0.6 and sequence['frequency'] >= 2:
                predictions.append({
                    'title': sequence['next_task'],
                    'classification': sequence['classification'],
                    'est_time': sequence['avg_duration'],
                    'confidence': sequence['confidence'],
                    'reason': f"Often follows '{sequence['previous_task'][:30]}{'...' if len(sequence['previous_task']) > 30 else ''}'",
                    'type': 'sequence'
                })

        # Time-based predictions (only strong patterns)
        for pattern in time_patterns[:2]:
            if pattern['confidence'] > 0.5 and pattern['frequency'] >= 3:
                predictions.append({
                    'title': pattern['task_title'],
                    'classification': pattern['classification'],
                    'est_time': pattern['avg_duration'],
                    'confidence': pattern['confidence'],
                    'reason': f"Common at {pattern['time_period']}",
                    'type': 'temporal'
                })

        # If we don't have enough predictions, fill with default suggestions
        if len(predictions) < 2:
            default_predictions = self._get_default_predictions()
            for pred in default_predictions:
                if len(predictions) < 3:
                    predictions.append(pred)

        return predictions[:3]  # Limit to top 3

    def _get_default_predictions(self) -> List[Dict]:
        """Get default predictions when insufficient data is available"""
        current_time = datetime.now()
        hour = current_time.hour
        weekday = current_time.weekday()

        # Time-aware default suggestions
        if 8 <= hour <= 10 and weekday < 5:  # Weekday morning
            return [
                {
                    'title': 'Daily standup preparation',
                    'classification': 'Planning',
                    'est_time': 15,
                    'confidence': 0.6,
                    'reason': 'Common morning activity',
                    'type': 'default'
                },
                {
                    'title': 'Review daily priorities',
                    'classification': 'Planning',
                    'est_time': 20,
                    'confidence': 0.5,
                    'reason': 'Typical start-of-day task',
                    'type': 'default'
                }
            ]
        elif 13 <= hour <= 15 and weekday < 5:  # Weekday afternoon
            return [
                {
                    'title': 'Team coordination check',
                    'classification': 'Business Support Activities',
                    'est_time': 25,
                    'confidence': 0.55,
                    'reason': 'Common afternoon activity',
                    'type': 'default'
                }
            ]
        else:
            return [
                {
                    'title': 'Task planning and organization',
                    'classification': 'Planning',
                    'est_time': 30,
                    'confidence': 0.4,
                    'reason': 'General productivity task',
                    'type': 'default'
                }
            ]

    def _analyze_task_sequences(self, tasks: List[Dict]) -> List[Dict]:
        """Analyze sequential patterns in tasks"""
        # Sort tasks by date and time
        sorted_tasks = sorted(tasks, key=lambda x: (x.get('date', ''), x.get('created_at', '')))

        sequences = defaultdict(list)

        # Look for tasks created within 24 hours of each other
        for i in range(len(sorted_tasks) - 1):
            current_task = sorted_tasks[i]
            next_task = sorted_tasks[i + 1]

            # Check if tasks are in sequence (within 24 hours)
            try:
                current_date = datetime.fromisoformat(current_task.get('date', ''))
                next_date = datetime.fromisoformat(next_task.get('date', ''))

                if (next_date - current_date).days <= 1:
                    key = current_task.get('title', '').strip()
                    sequences[key].append({
                        'next_task': next_task.get('title', ''),
                        'classification': next_task.get('classification', ''),
                        'duration': next_task.get('est_time', 30)
                    })
            except:
                continue

        # Analyze sequences and calculate confidence
        sequence_patterns = []
        for prev_task, next_tasks in sequences.items():
            if len(next_tasks) >= 2:  # Need at least 2 occurrences
                # Find most common next task
                next_task_counter = Counter(task['next_task'] for task in next_tasks)
                most_common = next_task_counter.most_common(1)[0]

                # Calculate average duration and confidence
                matching_tasks = [task for task in next_tasks if task['next_task'] == most_common[0]]
                avg_duration = sum(task['duration'] for task in matching_tasks) // len(matching_tasks)
                confidence = most_common[1] / len(next_tasks)

                sequence_patterns.append({
                    'previous_task': prev_task,
                    'next_task': most_common[0],
                    'classification': matching_tasks[0]['classification'],
                    'avg_duration': avg_duration,
                    'confidence': confidence,
                    'frequency': most_common[1]
                })

        # Sort by confidence and frequency
        sequence_patterns.sort(key=lambda x: (x['confidence'], x['frequency']), reverse=True)
        return sequence_patterns

    def _analyze_time_patterns(self, tasks: List[Dict], current_time: datetime) -> List[Dict]:
        """Analyze time-based patterns in task creation"""
        current_hour = current_time.hour
        current_weekday = current_time.weekday()

        # Group tasks by time periods
        time_groups = defaultdict(list)

        for task in tasks:
            try:
                created_at = task.get('created_at', '')
                if created_at:
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))

                    # Group by hour ranges and weekday
                    hour_range = f"{dt.hour:02d}:00-{dt.hour:02d}:59"
                    weekday = dt.strftime('%A')

                    # Check if similar to current time
                    if abs(dt.hour - current_hour) <= 1 and dt.weekday() == current_weekday:
                        time_groups[hour_range].append(task)
            except:
                continue

        # Analyze patterns for current time period
        patterns = []
        for time_period, period_tasks in time_groups.items():
            if len(period_tasks) >= 2:  # Need multiple occurrences
                # Find common task types
                task_counter = Counter(task.get('title', '') for task in period_tasks)
                classification_counter = Counter(task.get('classification', '') for task in period_tasks)

                for title, count in task_counter.most_common(3):
                    if count >= 2:  # At least 2 occurrences
                        matching_tasks = [task for task in period_tasks if task.get('title') == title]
                        avg_duration = sum(task.get('est_time', 30) for task in matching_tasks) // len(matching_tasks)

                        patterns.append({
                            'task_title': title,
                            'classification': matching_tasks[0].get('classification', ''),
                            'avg_duration': avg_duration,
                            'confidence': count / len(period_tasks),
                            'time_period': time_period,
                            'frequency': count
                        })

        patterns.sort(key=lambda x: (x['confidence'], x['frequency']), reverse=True)
        return patterns

    def optimize_workload(self, tasks: List[Dict], target_date: Optional[str] = None) -> Dict:
        """Analyze workload and provide optimization suggestions"""
        if target_date is None:
            target_date = datetime.now().strftime('%Y-%m-%d')

        # Filter tasks for target date
        date_tasks = [task for task in tasks if task.get('date') == target_date]

        # Calculate workload metrics
        total_time = sum(task.get('est_time', 0) for task in date_tasks)
        task_count = len(date_tasks)

        # Analyze priority distribution
        high_priority_count = 0
        medium_priority_count = 0
        low_priority_count = 0

        for task in date_tasks:
            insight = self.nlp_engine.analyze_task_text(
                task.get('title', ''),
                task.get('description', '')
            )
            if insight.urgency_level == 'high':
                high_priority_count += 1
            elif insight.urgency_level == 'medium':
                medium_priority_count += 1
            else:
                low_priority_count += 1

        # Generate recommendations
        recommendations = []

        if total_time > 480:  # More than 8 hours
            recommendations.append({
                'type': 'overload',
                'message': f"Workload of {total_time//60}h {total_time%60}m exceeds recommended daily capacity",
                'suggestion': "Consider rescheduling some lower priority tasks to another day",
                'priority': 'high'
            })

        if high_priority_count > 3:
            recommendations.append({
                'type': 'priority_overload',
                'message': f"{high_priority_count} high-priority tasks scheduled",
                'suggestion': "Review priorities to ensure realistic daily goals",
                'priority': 'medium'
            })

        if task_count > 8:
            recommendations.append({
                'type': 'task_overload',
                'message': f"{task_count} tasks may cause context switching overhead",
                'suggestion': "Consider grouping similar tasks together",
                'priority': 'low'
            })

        return {
            'total_time': total_time,
            'task_count': task_count,
            'priority_distribution': {
                'high': high_priority_count,
                'medium': medium_priority_count,
                'low': low_priority_count
            },
            'recommendations': recommendations,
            'optimal_capacity': total_time <= 480 and task_count <= 8,
            'efficiency_score': min(100, max(0, 100 - (total_time - 480) / 10))
        }


class AITaskEngine:
    """Main AI engine combining NLP and predictive capabilities"""

    def __init__(self):
        self.nlp_engine = EnhancedNLPEngine()
        self.predictive_engine = PredictiveEngine(self.nlp_engine)

    def analyze_task(self, title: str, description: str = '', user_tasks: Optional[List[Dict]] = None) -> Dict:
        """Comprehensive task analysis using AI"""
        if user_tasks is None:
            user_tasks = []

        # Get basic insights
        insight = self.nlp_engine.analyze_task_text(title, description)

        # Find similar tasks
        similar_tasks = self._find_similar_tasks(title, description, user_tasks)
        insight.similar_tasks = similar_tasks

        # Enhanced classification prediction
        classification_prediction = self._predict_classification_ai(title, description, user_tasks)

        # Enhanced duration estimation
        duration_prediction = self._predict_duration_ai(title, description, user_tasks, similar_tasks)

        return {
            'priority': {
                'score': insight.priority_score,
                'level': insight.urgency_level,
                'confidence': insight.confidence
            },
            'complexity': {
                'score': insight.complexity_score,
                'level': 'high' if insight.complexity_score > 0.7 else 'medium' if insight.complexity_score > 0.4 else 'low'
            },
            'classification': classification_prediction,
            'duration': duration_prediction,
            'similar_tasks': similar_tasks[:5],  # Top 5 similar tasks
            'insights': self._generate_insights(insight, similar_tasks, title)
        }

    def _find_similar_tasks(self, title: str, description: str, user_tasks: List[Dict]) -> List[Dict]:
        """Find similar tasks using enhanced similarity algorithms"""
        target_text = f"{title} {description}"
        similarities = []

        for task in user_tasks:
            task_text = f"{task.get('title', '')} {task.get('description', '')}"
            similarity = self.nlp_engine.calculate_semantic_similarity(target_text, task_text)

            if similarity > 0.3:  # Threshold for similarity
                similarities.append({
                    'task': task,
                    'similarity': similarity,
                    'title': task.get('title', ''),
                    'classification': task.get('classification', ''),
                    'est_time': task.get('est_time', 0),
                    'date': task.get('date', '')
                })

        # Sort by similarity
        similarities.sort(key=lambda x: x['similarity'], reverse=True)
        return [item for item in similarities]

    def _predict_classification_ai(self, title: str, description: str, user_tasks: List[Dict]) -> Dict:
        """Enhanced classification prediction using AI"""
        text = f"{title} {description}".lower()

        # Don't provide confident classification for very short inputs
        if len(title.strip()) < 3:
            return {
                'classification': None,
                'confidence': 0.0,
                'alternatives': [],
                'reasoning': 'Input too short for reliable classification'
            }

        # Get similar tasks for classification hints
        similar_tasks = self._find_similar_tasks(title, description, user_tasks)

        classification_scores = defaultdict(float)

        # Weight based on similar tasks (more conservative)
        for similar in similar_tasks[:5]:  # Reduced from 10
            classification = similar['task'].get('classification', '')
            if classification and similar['similarity'] > 0.5:  # Higher similarity threshold
                classification_scores[classification] += similar['similarity'] * 1.5  # Reduced weight

        # Enhanced keyword-based scoring
        classification_keywords = {
            'Planning': {
                'strong': ['plan', 'planning', 'strategy', 'roadmap', 'design', 'architecture', 'ce', 'platform', 'preparation'],
                'moderate': ['prepare', 'organize', 'schedule', 'coordinate', 'structure', 'special', 'session']
            },
            'Offline Processing': {
                'strong': ['process', 'analyze', 'calculation', 'batch', 'report', 'data', 'review', 'analytics'],
                'moderate': ['examine', 'study', 'evaluate', 'assess', 'generate', 'insights']
            },
            'Execution': {
                'strong': ['implement', 'execute', 'build', 'create', 'develop', 'deploy', 'fix', 'bug'],
                'moderate': ['run', 'perform', 'complete', 'finish', 'deliver', 'launch', 'release']
            },
            'Business Support Activities': {
                'strong': ['meeting', 'standup', 'discussion', 'coordination', 'communication', 'sync', 'scrum'],
                'moderate': ['support', 'help', 'assist', 'collaborate', 'team', 'client', 'stakeholder']
            },
            'Operational Project Involvement': {
                'strong': ['project', 'operational', 'maintain', 'monitor', 'oversight', 'track'],
                'moderate': ['update', 'manage', 'supervise', 'control', 'performance', 'metrics']
            }
        }

        words = text.split()
        for classification, keyword_groups in classification_keywords.items():
            for word in words:
                # Check strong keywords
                for strong_keyword in keyword_groups['strong']:
                    if strong_keyword in word or word in strong_keyword:
                        classification_scores[classification] += 2.0

                # Check moderate keywords
                for moderate_keyword in keyword_groups['moderate']:
                    if moderate_keyword in word or word in moderate_keyword:
                        classification_scores[classification] += 1.0

        # Special patterns for better recognition
        if any(word in ['daily', 'standup', 'stand-up', 'stand_up', 'scrum'] for word in words):
            classification_scores['Business Support Activities'] += 3.0

        if any(word in ['review', 'report', 'weekly', 'monthly'] for word in words):
            # Context-aware classification for reviews
            if any(business_word in words for business_word in ['team', 'performance', 'meeting']):
                classification_scores['Business Support Activities'] += 2.5
            else:
                classification_scores['Offline Processing'] += 2.0

        # Additional pattern matching for common task types
        if any(word in ['meeting', 'sync', 'discussion', 'call'] for word in words):
            classification_scores['Business Support Activities'] += 2.0

        if any(word in ['documentation', 'document'] for word in words):
            classification_scores['Offline Processing'] += 2.0        # Priority-based classification hints (more conservative)
        insight = self.nlp_engine.analyze_task_text(title, description)
        if insight.confidence > 0.6:  # Only adjust if we have decent confidence
            if insight.urgency_level == 'high':
                classification_scores['Execution'] += 0.5  # Reduced from 1.0
            elif insight.complexity_score > 0.7:
                classification_scores['Planning'] += 0.5  # Reduced from 1.0

        if not classification_scores:
            return {'classification': None, 'confidence': 0.0, 'alternatives': [], 'reasoning': 'No clear classification patterns found'}

        # Get best prediction with more conservative confidence calculation
        best_classification = max(classification_scores.items(), key=lambda x: x[1])
        total_score = sum(classification_scores.values())

        # More conservative confidence calculation
        raw_confidence = best_classification[1] / total_score if total_score > 0 else 0

        # Apply additional confidence penalties for edge cases
        if len(words) < 2:
            raw_confidence *= 0.8  # Less penalty for short but meaningful titles
        if best_classification[1] < 1.5:  # Lower threshold for absolute score
            raw_confidence *= 0.8

        confidence = min(raw_confidence, 0.85)  # Cap confidence

        # Get alternatives
        alternatives = sorted(classification_scores.items(), key=lambda x: x[1], reverse=True)[:3]

        # Build reasoning
        reasoning_parts = []
        if similar_tasks:
            reasoning_parts.append(f"similar past tasks")
        if any(score > 2.0 for score in classification_scores.values()):
            reasoning_parts.append("strong keyword matches")
        else:
            reasoning_parts.append("weak keyword patterns")

        reasoning = f"Based on {' and '.join(reasoning_parts) if reasoning_parts else 'limited available information'}"

        return {
            'classification': best_classification[0],
            'confidence': confidence,
            'alternatives': alternatives,
            'reasoning': reasoning
        }

    def _predict_duration_ai(self, title: str, description: str, user_tasks: List[Dict], similar_tasks: List[Dict]) -> Dict:
        """Enhanced duration prediction using AI"""
        # Get base prediction from NLP
        insight = self.nlp_engine.analyze_task_text(title, description)
        base_duration = insight.predicted_duration

        # Adjust based on similar tasks
        if similar_tasks:
            similar_durations = [task['est_time'] for task in similar_tasks[:5] if task['est_time'] > 0]
            if similar_durations:
                avg_similar = sum(similar_durations) / len(similar_durations)
                # Weighted average between base and similar tasks
                adjusted_duration = int(0.6 * avg_similar + 0.4 * base_duration)
            else:
                adjusted_duration = base_duration
        else:
            adjusted_duration = base_duration

        # Adjust based on complexity
        if insight.complexity_score > 0.7:
            adjusted_duration = int(adjusted_duration * 1.3)
        elif insight.complexity_score < 0.3:
            adjusted_duration = int(adjusted_duration * 0.8)

        # Adjust based on priority (urgent tasks often take longer due to pressure)
        if insight.urgency_level == 'high':
            adjusted_duration = int(adjusted_duration * 1.1)

        confidence = 0.6 + (0.3 if similar_tasks else 0) + (0.1 if len(description) > 20 else 0)

        return {
            'duration': max(15, adjusted_duration),  # Minimum 15 minutes
            'confidence': min(confidence, 0.95),
            'reasoning': f"Based on text analysis" + (" and similar tasks" if similar_tasks else ""),
            'range': {
                'min': max(15, int(adjusted_duration * 0.7)),
                'max': int(adjusted_duration * 1.3)
            }
        }

    def _generate_insights(self, insight: TaskInsight, similar_tasks: List[Dict], input_title: str) -> List[str]:
        """Generate human-readable insights about the task"""
        insights = []
        # Always show 'Add more details' for very short/ambiguous input (title < 5 chars or < 2 words)
        if len(input_title.strip()) < 5 or len(input_title.strip().split()) < 2 or insight.confidence < 0.2:
            return ["Add more details for better analysis"]

        # Priority insights
        if insight.priority_score > 0.7 and insight.confidence > 0.4:
            insights.append("This appears to be a high-priority task requiring immediate attention")
        elif insight.priority_score > 0.4 and insight.confidence > 0.3:
            insights.append("This task has elevated priority and should be scheduled soon")

        # Complexity insights (always show for analysis/report tasks)
        if (insight.complexity_score > 0.7 and insight.confidence > 0.4) or (
            any(kw in insight.similar_tasks[0]['title'].lower() if insight.similar_tasks else '' for kw in ['analysis', 'review', 'report', 'data']) or
            any(kw in insight.similar_tasks[0]['classification'].lower() if insight.similar_tasks else '' for kw in ['offline processing'])
        ):
            insights.append("This is a complex task that may require additional planning")
        elif insight.complexity_score < 0.3 and insight.confidence > 0.3:
            insights.append("This appears to be a straightforward task")

        # Similar tasks
        if len(similar_tasks) >= 3:
            insights.append(f"You've worked on {len(similar_tasks)} similar tasks before")
        elif len(similar_tasks) >= 1:
            insights.append("Found similar tasks in your history")

        # Duration insights
        if insight.predicted_duration > 120 and insight.confidence > 0.4:
            insights.append("Consider breaking this large task into smaller components")
        elif insight.predicted_duration < 20 and insight.confidence > 0.3:
            insights.append("This appears to be a quick task")

        # If we still don't have insights, provide generic helpful advice
        if not insights:
            insights.append("Add more details for better AI analysis")

        return insights

    def get_next_task_predictions(self, user_tasks: List[Dict], limit: int = 5) -> List[Dict]:
        """Get predictions for next likely tasks"""
        return self.predictive_engine.predict_next_tasks(user_tasks)[:limit]

    def get_workload_optimization(self, user_tasks: List[Dict], target_date: Optional[str] = None) -> Dict:
        """Get workload optimization recommendations"""
        return self.predictive_engine.optimize_workload(user_tasks, target_date)
