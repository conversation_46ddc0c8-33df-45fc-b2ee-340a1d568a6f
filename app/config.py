import os

# Optional dotenv loading
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # dotenv not available, use environment variables directly
    pass

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'

    # SharePoint-aware data directory configuration
    def __init__(self):
        self._setup_data_directory()

    def _setup_data_directory(self):
        """Setup data directory based on environment"""
        # Check for user override via environment variable
        user_data_dir = os.environ.get('ADHOCLOG_USER_DATA_DIR', '')
        sharepoint_mode = os.environ.get('ADHOCLOG_SHAREPOINT_MODE', '0') == '1'

        if sharepoint_mode and user_data_dir:
            # SharePoint mode: use user-specific directory
            self.DATA_DIR = os.path.dirname(user_data_dir)  # Parent of user directory
            self.USER_DATA_DIR = user_data_dir
            self.SHAREPOINT_MODE = True
        else:
            # Local mode: use standard data directory
            self.DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data')
            self.USER_DATA_DIR = self.DATA_DIR
            self.SHAREPOINT_MODE = False

    @property
    def IS_SHAREPOINT_DEPLOYMENT(self) -> bool:
        """Check if running in SharePoint deployment mode"""
        return getattr(self, 'SHAREPOINT_MODE', False)

    @property
    def EFFECTIVE_DATA_DIR(self) -> str:
        """Get the effective data directory for current user"""
        return getattr(self, 'USER_DATA_DIR', self.DATA_DIR)

    # Classification to Category mapping
    CLASSIFICATION_MAPPING = {
        'Planning': 'Adhoc',
        'Offline Processing': 'Adhoc',
        'Execution': 'Adhoc',
        'Business Support Activities': 'Business Support Activities',
        'Operational Project Involvement': 'Adhoc'
    }

    # Available classifications for dropdown
    CLASSIFICATIONS = list(CLASSIFICATION_MAPPING.keys())

    # Corporate environment settings
    CORPORATE_PROXY_SUPPORT = True
    CACHE_ISOLATION_ENABLED = os.environ.get('PYTHONDONTWRITEBYTECODE', '0') == '1'

    # Performance settings for SharePoint
    SHAREPOINT_SYNC_DELAY_TOLERANCE = 5  # seconds
    MAX_CONCURRENT_USERS = 10
