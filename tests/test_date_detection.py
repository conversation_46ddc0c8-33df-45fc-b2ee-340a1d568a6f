#!/usr/bin/env python3
"""
Simple test to debug date detection
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.chatbot_engine import ChatbotEngine
from app.data_manager import DataManager
from app.ai_engine import AITaskEngine
from app.analytics import AnalyticsEngine

def test_date_detection():
    """Test the date detection specifically"""

    # Initialize components
    data_manager = DataManager()
    ai_engine = AITaskEngine()
    analytics_engine = AnalyticsEngine()

    chatbot = ChatbotEngine(data_manager, ai_engine, analytics_engine)

    # Test specifically the date detection method
    message = "this week"
    print(f"Testing date detection for: '{message}'")

    date_info = chatbot._detect_date_references(message)
    print(f"Date info: {date_info}")

    # Test with a more complex message
    message2 = "show me tasks this week"
    print(f"\nTesting date detection for: '{message2}'")

    date_info2 = chatbot._detect_date_references(message2)
    print(f"Date info: {date_info2}")

if __name__ == "__main__":
    test_date_detection()
