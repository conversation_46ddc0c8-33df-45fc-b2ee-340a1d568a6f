#!/usr/bin/env python3
"""
Debug script to test chatbot intent detection for temporal queries
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.chatbot_engine import ChatbotEngine
from app.data_manager import DataManager
from app.ai_engine import AITaskEngine
from app.analytics import AnalyticsEngine
from app.config import Config

def debug_chatbot_intent():
    """Debug the chatbot intent detection"""

    # Initialize components
    config = Config()
    data_manager = DataManager()
    ai_engine = AITaskEngine()
    analytics_engine = AnalyticsEngine()

    chatbot = ChatbotEngine(data_manager, ai_engine, analytics_engine)

    # Test messages that should trigger show_tasks_by_date
    test_messages = [
        "show me tasks this week",
        "show me tasks this year",
        "tasks this week",
        "tasks this year",
        "what do I have this week",
        "what do I have this year"
    ]

    print("=== Debugging Chatbot Intent Detection ===\n")

    for message in test_messages:
        print(f"Testing message: '{message}'")

        # Check intent detection
        intent, extracted_info = chatbot._understand_intent(message)
        print(f"  Intent: {intent}")
        print(f"  Contains date: {extracted_info.get('contains_date', False)}")
        print(f"  Date entities: {extracted_info.get('date_entities', [])}")
        print(f"  Relative dates: {extracted_info.get('relative_dates', [])}")
        print(f"  Weeks: {extracted_info.get('weeks', [])}")
        print(f"  Years: {extracted_info.get('years', [])}")
        print(f"  Temporal expressions: {extracted_info.get('temporal_expressions', [])}")
        print(f"  Temporal ranges: {extracted_info.get('temporal_ranges', [])}")

        # Test the full response
        print("  Testing full response...")
        try:
            response = chatbot.process_message(message)
            print(f"  Response: {response.content[:100]}...")
        except Exception as e:
            print(f"  Error in response generation: {e}")

        print()

if __name__ == "__main__":
    debug_chatbot_intent()
