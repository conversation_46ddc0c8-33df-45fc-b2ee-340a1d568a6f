"""
Comprehensive Out-of-Domain Testing for AdhocLog Chatbot
Tests domain guard-rails and rejection of non-IT queries
"""

import unittest
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.chatbot_engine import ChatbotEngine
from app.data_manager import DataManager
from app.ai_engine import AITaskEngine
from app.analytics import AnalyticsEngine


class TestOutOfDomainQueries(unittest.TestCase):
    """Test out-of-domain query detection and rejection"""

    def setUp(self):
        """Set up test environment"""
        self.data_manager = DataManager()
        self.ai_engine = AITaskEngine()
        self.analytics_engine = AnalyticsEngine()
        self.chatbot = ChatbotEngine(self.data_manager, self.ai_engine, self.analytics_engine)

    def test_medical_queries(self):
        """Test rejection of medical-related queries"""
        medical_queries = [
            "What is my blood pressure?",
            "How should I take my medication?",
            "I have a headache, what should I do?",
            "Can you diagnose my symptoms?",
            "What doctor should I see for this pain?",
            "Track my medical appointments",
            "Remind me to take my prescription",
            "What are the side effects of this drug?",
            "I need medical advice for my condition",
            "Hospital visiting hours today"
        ]

        for query in medical_queries:
            with self.subTest(query=query):
                response = self.chatbot.process_message(query)

                # Verify intent is classified as out_of_scope
                intent, extracted_info = self.chatbot._understand_intent(query)
                self.assertEqual(intent, 'out_of_scope',
                    f"Medical query '{query}' should be classified as out_of_scope")

                # Verify response contains polite rejection
                self.assertIn('outside my expertise', response.content.lower(),
                    f"Response should politely reject medical query: {query}")

                # Verify response message type
                self.assertEqual(response.message_type, 'out_of_scope',
                    f"Message type should be 'out_of_scope' for: {query}")

                # Verify response includes IT alternatives
                self.assertIn('task', response.content.lower(),
                    f"Response should include IT alternatives for: {query}")

    def test_financial_queries(self):
        """Test rejection of financial-related queries"""
        financial_queries = [
            "What's my bank balance?",
            "How should I invest my money?",
            "Track my stock portfolio performance",
            "What's the best credit card for me?",
            "Help me with my loan application",
            "Calculate my mortgage payments",
            "What are today's exchange rates?",
            "Should I buy or sell this stock?",
            "Track my cryptocurrency investments",
            "Help me with tax planning"
        ]

        for query in financial_queries:
            with self.subTest(query=query):
                response = self.chatbot.process_message(query)

                # Verify intent classification
                intent, extracted_info = self.chatbot._understand_intent(query)
                self.assertEqual(intent, 'out_of_scope',
                    f"Financial query '{query}' should be classified as out_of_scope")

                # Verify response contains polite rejection
                self.assertIn('outside my expertise', response.content.lower(),
                    f"Response should politely reject financial query: {query}")

                # Verify blacklist keywords are detected
                self.assertIn('blacklist_keywords', extracted_info,
                    f"Blacklist keywords should be detected for: {query}")

    def test_personal_health_queries(self):
        """Test rejection of personal health queries"""
        health_queries = [
            "Track my medication schedule",
            "What are the symptoms of the flu?",
            "How many calories should I eat?",
            "Track my fitness goals",
            "What's a healthy diet plan?",
            "Monitor my blood sugar levels",
            "Help me with my therapy schedule",
            "Track my surgery recovery",
            "What vitamins should I take?",
            "Monitor my heart rate"
        ]

        for query in health_queries:
            with self.subTest(query=query):
                response = self.chatbot.process_message(query)

                # Verify intent classification
                intent, extracted_info = self.chatbot._understand_intent(query)
                self.assertEqual(intent, 'out_of_scope',
                    f"Health query '{query}' should be classified as out_of_scope")

    def test_general_personal_queries(self):
        """Test rejection of general personal queries"""
        personal_queries = [
            "What's the weather like today?",
            "Find me a good restaurant nearby",
            "What's on TV tonight?",
            "Play my favorite music",
            "What's the latest news?",
            "Tell me a joke",
            "What's the traffic like?",
            "Book a vacation for me",
            "What movie should I watch?",
            "Find me a recipe for dinner"
        ]

        for query in personal_queries:
            with self.subTest(query=query):
                response = self.chatbot.process_message(query)

                # Verify intent classification
                intent, extracted_info = self.chatbot._understand_intent(query)
                self.assertEqual(intent, 'out_of_scope',
                    f"Personal query '{query}' should be classified as out_of_scope")

    def test_legitimate_it_queries_not_rejected(self):
        """Test that legitimate IT queries are not incorrectly rejected"""
        legitimate_queries = [
            "Create a task for system maintenance",
            "Show me my project tasks",
            "Schedule a task for server updates",
            "Track my development work",
            "Help me plan my IT projects",
            "What tasks do I have for network setup?",
            "Create a task for database backup",
            "Show me my completed coding tasks",
            "Schedule time for testing the application",
            "Track my time on security updates"
        ]

        for query in legitimate_queries:
            with self.subTest(query=query):
                response = self.chatbot.process_message(query)

                # Verify intent is NOT classified as out_of_scope
                intent, extracted_info = self.chatbot._understand_intent(query)
                self.assertNotEqual(intent, 'out_of_scope',
                    f"Legitimate IT query '{query}' should not be rejected")

                # Verify response is not an out-of-scope rejection
                self.assertNotIn('outside my expertise', response.content.lower(),
                    f"Legitimate query should not be rejected: {query}")

    def test_borderline_cases(self):
        """Test borderline cases that might be ambiguous"""
        borderline_queries = [
            "Track my health insurance claims",  # Could be IT task management
            "Monitor bank API integration",      # IT task about banking
            "Test the medical system database",  # IT task about medical systems
            "Update the payment processing system",  # IT task about finance
            "Debug the hospital management app",     # IT task about medical apps
        ]

        for query in borderline_queries:
            with self.subTest(query=query):
                response = self.chatbot.process_message(query)

                # These should be treated as IT tasks since they involve technical work
                intent, extracted_info = self.chatbot._understand_intent(query)

                # Accept either classification but document the behavior
                if intent == 'out_of_scope':
                    # If classified as out of scope, ensure it's handled gracefully
                    self.assertIn('outside my expertise', response.content.lower())
                else:
                    # If accepted as IT task, ensure proper handling
                    self.assertNotIn('outside my expertise', response.content.lower())

    def test_response_quality_and_alternatives(self):
        """Test that out-of-scope responses provide quality IT alternatives"""
        out_of_scope_query = "What medication should I take for my headache?"
        response = self.chatbot.process_message(out_of_scope_query)

        # Verify response structure
        self.assertIn('I can help you with:', response.content)
        self.assertIn('Try asking me things like:', response.content)

        # Verify IT alternatives are provided
        it_alternatives = [
            'task', 'project', 'schedule', 'management', 'planning'
        ]

        response_lower = response.content.lower()
        alternatives_found = sum(1 for alt in it_alternatives if alt in response_lower)
        self.assertGreaterEqual(alternatives_found, 3,
            "Response should include multiple IT-related alternatives")

    def test_analytics_tracking(self):
        """Test that out-of-scope queries are properly tracked in analytics"""
        out_of_scope_query = "What's my blood pressure reading?"

        # Process the query
        response = self.chatbot.process_message(out_of_scope_query)

        # Verify analytics tracking would occur
        # Note: In a full implementation, you would verify actual analytics events
        self.assertEqual(response.message_type, 'out_of_scope')
        if response.metadata:
            self.assertIsNotNone(response.metadata.get('rejected_keywords'))

    def test_success_rate_metrics(self):
        """Test overall success rate for out-of-domain detection"""
        test_cases = [
            # Out-of-domain queries (should be rejected)
            ("What's my blood pressure?", True),
            ("Invest my money in stocks", True),
            ("What's the weather today?", True),
            ("Find me a restaurant", True),
            ("Book a doctor appointment", True),

            # In-domain queries (should be accepted)
            ("Create a task for server maintenance", False),
            ("Show me my project schedule", False),
            ("Track my development time", False),
            ("Help me plan my IT tasks", False),
            ("What tasks do I have today?", False),
        ]

        correct_classifications = 0
        total_cases = len(test_cases)

        for query, should_be_rejected in test_cases:
            intent, _ = self.chatbot._understand_intent(query)
            is_rejected = (intent == 'out_of_scope')

            if is_rejected == should_be_rejected:
                correct_classifications += 1

        success_rate = (correct_classifications / total_cases) * 100

        # Require 100% success rate for domain filtering
        self.assertEqual(success_rate, 100.0,
            f"Domain filtering success rate should be 100%, got {success_rate}%")


if __name__ == '__main__':
    unittest.main()
