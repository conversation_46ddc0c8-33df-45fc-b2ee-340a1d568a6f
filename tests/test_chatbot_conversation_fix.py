#!/usr/bin/env python3
"""
Test script to verify the chatbot conversation flow fix
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.chatbot_engine import Cha<PERSON><PERSON><PERSON>ng<PERSON>, ConversationContext
from app.data_manager import DataManager
from app.ai_engine import AITaskEngine
from app.analytics import AnalyticsEngine

def test_conversation_flow():
    """Test the specific conversation flow issue"""

    # Initialize components
    data_manager = DataManager()
    ai_engine = AITaskEngine()
    analytics_engine = AnalyticsEngine()
    chatbot = ChatbotEngine(data_manager, ai_engine, analytics_engine)

    print("=== Testing Chatbot Conversation Flow Fix ===\n")

    # Simulate the conversation scenario
    print("1. User asks: 'Show me tasks for today'")
    response1 = chatbot.process_message("Show me tasks for today")
    print(f"Bot responds: {response1.content}\n")

    # Check if bot made an offer when no tasks found
    print("2. Checking if bot made an offer to help...")
    print(f"Last offer type: {chatbot.context.last_offer_type}")
    print(f"Conversation history length: {len(chatbot.context.conversation_history)}\n")

    # Test affirmative response
    print("3. User responds: 'Yes'")
    response2 = chatbot.process_message("Yes")
    print(f"Bot responds: {response2.content}\n")

    # Check if it was handled as follow-up
    print("4. Checking follow-up detection...")
    print(f"Response type: {response2.message_type}")
    print(f"Should be 'structured_fallback' if working correctly\n")

    # Test negative response in new conversation
    print("5. Reset and test negative response...")
    chatbot.context.clear_context()

    # First establish context again
    response3 = chatbot.process_message("Show me tasks for today")
    print(f"Bot: {response3.content[:100]}...")

    print("\n6. User responds: 'No'")
    response4 = chatbot.process_message("No")
    print(f"Bot responds: {response4.content}\n")

    print("=== Test completed ===")

if __name__ == "__main__":
    test_conversation_flow()
