import unittest
from app.ai_engine import AITaskEngine

class TestAITaskEngineAnalysis(unittest.TestCase):
    def setUp(self):
        self.engine = AITaskEngine()
        # Simulate user tasks history
        self.user_tasks = [
            {'title': 'Daily Stand-up', 'classification': 'Business Support Activities', 'description': '', 'est_time': 15, 'date': '2025-07-24'},
            {'title': 'Monthly Review Report', 'classification': 'Execution', 'description': '', 'est_time': 30, 'date': '2025-07-01'},
            {'title': 'CE - Special Platforms', 'classification': 'Planning', 'description': '', 'est_time': 30, 'date': '2025-07-02'},
            {'title': 'Shift Leader Role - Updates', 'classification': 'Planning', 'description': '', 'est_time': 30, 'date': '2025-07-02'},
            {'title': 'Client Systems Change Control Review', 'classification': 'Business Support Activities', 'description': '', 'est_time': 60, 'date': '2025-07-02'}
        ]

    def test_short_input(self):
        result = self.engine.analyze_task('Daily', '', self.user_tasks)
        self.assertIn('Add more details', result['insights'][0])
        self.assertEqual(result['priority']['level'], 'normal')

    def test_standup(self):
        result = self.engine.analyze_task('Daily Stand-up', '', self.user_tasks)
        self.assertIn('Business Support Activities', [alt[0] for alt in result['classification']['alternatives']])
        self.assertGreaterEqual(result['priority']['confidence'], 0.3)

    def test_platforms(self):
        result = self.engine.analyze_task('CE - Special Platforms', '', self.user_tasks)
        self.assertIn('Planning', [alt[0] for alt in result['classification']['alternatives']])
        self.assertGreaterEqual(result['priority']['confidence'], 0.3)
        self.assertTrue(any('similar tasks' in insight for insight in result['insights']))

    def test_detailed_task(self):
        result = self.engine.analyze_task('Monthly Review Report', 'Prepare and analyze monthly data for management.', self.user_tasks)
        self.assertIn('Offline Processing', [alt[0] for alt in result['classification']['alternatives']])
        self.assertGreaterEqual(result['priority']['confidence'], 0.4)
        self.assertTrue(any('complex' in insight or 'high-priority' in insight for insight in result['insights']))

if __name__ == '__main__':
    unittest.main()
