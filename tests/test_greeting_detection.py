#!/usr/bin/env python3
"""
Debug greeting detection specifically
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.chatbot_engine import ChatbotEngine
from app.data_manager import DataManager
from app.ai_engine import AITaskEngine
from app.analytics import AnalyticsEngine

def test_greeting_detection():
    """Test greeting detection"""

    message = "show me tasks this week"
    print(f"Testing greeting detection for: '{message}'")

    greeting_patterns = ['hi', 'hello', 'hey', 'good morning', 'good afternoon', 'good evening']
    for pattern in greeting_patterns:
        if pattern in message:
            print(f"  Matches greeting pattern: '{pattern}'")

    print(f"Any greeting match: {any(greeting in message for greeting in greeting_patterns)}")

    # Test follow-up detection as well
    data_manager = DataManager()
    ai_engine = AITaskEngine()
    analytics_engine = AnalyticsEngine()

    chatbot = ChatbotEngine(data_manager, ai_engine, analytics_engine)

    extracted_info = {
        'original_message': message,
        'cleaned_message': message,
        'contains_date': False,
        'contains_task_keywords': False,
        'contains_status_keywords': False,
        'contains_classification_keywords': False,
        'date_entities': [],
        'task_action': None,
        'is_follow_up': False,
        'context_reference': None,
        'groups': []
    }

    follow_up_intent, follow_up_info = chatbot._detect_follow_up_intent(message, extracted_info)
    print(f"Follow-up intent: {follow_up_intent}")
    print(f"Follow-up info: {follow_up_info}")

if __name__ == "__main__":
    test_greeting_detection()
