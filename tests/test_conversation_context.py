#!/usr/bin/env python3
"""
Test conversation context and follow-up functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.chatbot_engine import ChatbotEngine
from app.data_manager import DataManager
from app.ai_engine import AITaskEngine
from app.analytics import AnalyticsEngine

def test_conversation_context():
    """Test conversation context and follow-up questions"""
    print("🧪 Testing Conversation Context & Follow-up Questions")
    print("=" * 60)

    try:
        # Initialize chatbot
        data_manager = DataManager()
        ai_engine = AITaskEngine()
        analytics_engine = AnalyticsEngine()
        chatbot = ChatbotEngine(data_manager, ai_engine, analytics_engine)

        # Test conversation flow
        print("\n💬 Starting conversation simulation:")

        # First message - show tasks for a date
        print("\n👤 User: Show me tasks for July 10")
        response1 = chatbot.process_message("Show me tasks for July 10")
        print(f"🤖 Assistant: {response1.content[:100]}...")
        print(f"   Type: {response1.message_type}")

        # Check if context was saved
        context_saved = len(chatbot.context.conversation_history) > 0
        print(f"   ✅ Context saved: {context_saved}")

        # Follow-up question 1 - filter results
        print("\n👤 User: Show only the completed ones")
        response2 = chatbot.process_message("Show only the completed ones")
        print(f"🤖 Assistant: {response2.content[:100]}...")
        print(f"   Type: {response2.message_type}")

        # Check if it was detected as follow-up
        is_follow_up = "filter" in response2.content.lower() or "completed" in response2.content.lower()
        print(f"   ✅ Follow-up detected: {is_follow_up}")

        # Follow-up question 2 - ask for details
        print("\n👤 User: Tell me more about those tasks")
        response3 = chatbot.process_message("Tell me more about those tasks")
        print(f"🤖 Assistant: {response3.content[:100]}...")
        print(f"   Type: {response3.message_type}")

        # Follow-up question 3 - time information
        print("\n👤 User: How much time will they take?")
        response4 = chatbot.process_message("How much time will they take?")
        print(f"🤖 Assistant: {response4.content[:100]}...")
        print(f"   Type: {response4.message_type}")

        # Check conversation history
        history_count = len(chatbot.context.conversation_history)
        print(f"\n📊 Conversation history entries: {history_count}")

        # Test context reset with new topic
        print("\n👤 User: Create a new task for reviewing reports")
        response5 = chatbot.process_message("Create a new task for reviewing reports")
        print(f"🤖 Assistant: {response5.content[:100]}...")
        print(f"   Type: {response5.message_type}")

        print(f"\n📊 Final conversation history entries: {len(chatbot.context.conversation_history)}")

        # Summary
        print("\n" + "=" * 60)
        print("🏆 CONVERSATION CONTEXT TEST RESULTS")
        print("=" * 60)

        tests_passed = 0
        total_tests = 4

        if context_saved:
            print("✅ Context saving works")
            tests_passed += 1
        else:
            print("❌ Context saving failed")

        if history_count >= 4:
            print("✅ Conversation history tracking works")
            tests_passed += 1
        else:
            print("❌ Conversation history tracking failed")

        if is_follow_up:
            print("✅ Follow-up detection works")
            tests_passed += 1
        else:
            print("❌ Follow-up detection failed")

        if len(chatbot.context.conversation_history) > history_count:
            print("✅ Continuous conversation tracking works")
            tests_passed += 1
        else:
            print("❌ Continuous conversation tracking failed")

        success_rate = (tests_passed / total_tests) * 100
        print(f"\n📊 Success Rate: {tests_passed}/{total_tests} ({success_rate:.1f}%)")

        if success_rate >= 75:
            print("🎉 Conversation context is working well!")
            return True
        else:
            print("⚠️ Conversation context needs improvement.")
            return False

    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_conversation_context()
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILED'}")
    sys.exit(0 if success else 1)
