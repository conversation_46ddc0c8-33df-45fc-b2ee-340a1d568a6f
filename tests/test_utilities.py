#!/usr/bin/env python3
"""
Test script to verify all utility scripts and features work correctly
"""

import sys
import os
import subprocess
from pathlib import Path

def run_command(command, timeout=30):
    """Run a command and return success status and output"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout,
            cwd=Path.cwd()
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def test_script_directory_fixes():
    """Test that all scripts work from the correct directory"""
    print("🔍 Testing script directory fixes...")

    scripts_to_test = [
        ("Setup script", "./scripts/setup_missing_files.sh"),
        ("Diagnose script", "./scripts/diagnose.sh"),
        ("Regenerate data script", "./scripts/regenerate_sample_data.sh"),
    ]

    passed = 0
    total = len(scripts_to_test)

    for name, script in scripts_to_test:
        print(f"  Testing {name}...")
        success, stdout, stderr = run_command(script, timeout=15)

        if success:
            # Check if the script found the correct files
            if "✅" in stdout and ("app.py" in stdout or "Project directory:" in stdout or "AdhocLog" in stdout):
                print(f"    ✅ {name} - Found project files correctly")
                passed += 1
            elif success and "AdhocLog" in stdout:
                # Diagnose script runs successfully even if format is different
                print(f"    ✅ {name} - Ran successfully")
                passed += 1
            else:
                print(f"    ⚠️  {name} - Ran but may not have found files correctly")
                print(f"    Output: {stdout[:100]}...")
        else:
            print(f"    ❌ {name} - Failed to run")
            print(f"    Error: {stderr[:100]}...")

    return passed, total

def test_clear_data_script():
    """Test the clear data script (without actually clearing data)"""
    print("🔍 Testing clear data script...")

    # Test with "no" response to avoid actually clearing data
    success, stdout, stderr = run_command('echo "no" | ./scripts/clear_data.sh', timeout=10)

    if success and "Operation cancelled" in stdout:
        print("    ✅ Clear data script - Works correctly and respects cancellation")
        return 1, 1
    else:
        print("    ❌ Clear data script - Failed or doesn't handle cancellation")
        print(f"    Output: {stdout[:100]}...")
        return 0, 1

def test_gui_launcher_clear_data():
    """Test that GUI launcher has clear data functionality"""
    print("🔍 Testing GUI launcher clear data functionality...")

    test_code = '''
import gui_launcher
launcher = gui_launcher.TaskTrackerLauncher()
has_clear_data = hasattr(launcher, "clear_data")
launcher.root.destroy()
print("HAS_CLEAR_DATA:", has_clear_data)
'''

    success, stdout, stderr = run_command(f"source venv/bin/activate && python -c '{test_code}'", timeout=10)

    if success and "HAS_CLEAR_DATA: True" in stdout:
        print("    ✅ GUI launcher - Has clear_data method")
        return 1, 1
    else:
        print("    ❌ GUI launcher - Missing clear_data method or failed to load")
        print(f"    Error: {stderr[:100]}...")
        return 0, 1

def test_project_structure():
    """Test that all essential files exist in correct locations"""
    print("🔍 Testing project structure...")

    essential_files = [
        "app.py",
        "config.py",
        "data_manager.py",
        "gui_launcher.py",
        "run.py",
        "launch_app.sh",
        "requirements.txt",
        "templates/base.html",
        "templates/index.html",
        "scripts/setup_missing_files.sh",
        "scripts/diagnose.sh",
        "scripts/clear_data.sh"
    ]

    passed = 0
    total = len(essential_files)

    for file_path in essential_files:
        path = Path(file_path)
        if path.exists():
            print(f"    ✅ {file_path}")
            passed += 1
        else:
            print(f"    ❌ {file_path} - Missing")

    return passed, total

def test_virtual_environment():
    """Test virtual environment setup"""
    print("🔍 Testing virtual environment...")

    venv_path = Path("venv")
    if not venv_path.exists():
        print("    ❌ Virtual environment directory missing")
        return 0, 1

    # Test Flask import
    success, stdout, stderr = run_command('source venv/bin/activate && python -c "import flask; print(flask.__version__)"', timeout=10)

    if success and len(stdout.strip()) > 0:
        print(f"    ✅ Virtual environment - Flask {stdout.strip()} installed")
        return 1, 1
    else:
        print("    ❌ Virtual environment - Flask not properly installed")
        return 0, 1

def main():
    """Run all tests"""
    print("=" * 60)
    print("🧪 AdhocLog - Utility Scripts Test Suite")
    print("=" * 60)
    print(f"📁 Working directory: {Path.cwd()}")
    print()

    tests = [
        ("Project Structure", test_project_structure),
        ("Virtual Environment", test_virtual_environment),
        ("Script Directory Fixes", test_script_directory_fixes),
        ("Clear Data Script", test_clear_data_script),
        ("GUI Clear Data Feature", test_gui_launcher_clear_data),
    ]

    total_passed = 0
    total_tests = 0

    for test_name, test_func in tests:
        print(f"🔬 Running {test_name} tests...")
        try:
            passed, total = test_func()
            total_passed += passed
            total_tests += total
            print(f"   Result: {passed}/{total} passed")
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            total_tests += 1
        print()

    print("=" * 60)
    print(f"📊 Final Results: {total_passed}/{total_tests} tests passed")

    if total_passed == total_tests:
        print("🎉 All tests passed! All utilities are working correctly.")
        print()
        print("✅ Fixed Issues:")
        print("  • Scripts now look in correct project directory")
        print("  • Setup/repair utility finds all files")
        print("  • Diagnostics shows accurate information")
        print("  • Clear data functionality added with safety warnings")
        print("  • GUI launcher has clear data button")
        return 0
    else:
        print(f"❌ {total_tests - total_passed} tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
