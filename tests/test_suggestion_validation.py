"""
Conversation Suggestion Validation Testing for AdhocLog Chatbot
Ensures all conversation suggestions are actionable and appropriately IT-focused
"""

import unittest
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.chatbot_engine import ChatbotEngine
from app.data_manager import DataManager
from app.ai_engine import AITaskEngine
from app.analytics import AnalyticsEngine


class TestSuggestionValidation(unittest.TestCase):
    """Test conversation suggestion validation and actionability"""

    def setUp(self):
        """Set up test environment"""
        self.data_manager = DataManager()
        self.ai_engine = AITaskEngine()
        self.analytics_engine = AnalyticsEngine()
        self.chatbot = ChatbotEngine(self.data_manager, self.ai_engine, self.analytics_engine)

    def test_get_conversation_suggestions(self):
        """Test the suggestion retrieval system"""
        suggestions = self.chatbot.get_conversation_suggestions()

        # Verify suggestions are returned
        self.assertIsInstance(suggestions, list)
        self.assertGreater(len(suggestions), 0, "Should return at least one suggestion")

        # Verify all suggestions are strings
        for suggestion in suggestions:
            self.assertIsInstance(suggestion, str)
            self.assertGreater(len(suggestion.strip()), 5, "Suggestions should be substantial")

    def test_suggestion_actionability(self):
        """Test that all conversation suggestions are actionable"""
        suggestions = self.chatbot.get_conversation_suggestions()

        actionable_count = 0
        total_suggestions = len(suggestions)

        for suggestion in suggestions:
            with self.subTest(suggestion=suggestion):
                # Send suggestion as user message
                response = self.chatbot.process_message(suggestion)

                # Verify the suggestion produces a valid, relevant response
                is_actionable = self._is_response_actionable(response, suggestion)

                if is_actionable:
                    actionable_count += 1
                else:
                    print(f"Non-actionable suggestion: {suggestion}")
                    print(f"Response: {response.content[:100]}...")

        actionability_rate = (actionable_count / total_suggestions) * 100

        # Require 100% actionability rate
        self.assertEqual(actionability_rate, 100.0,
            f"All suggestions should be actionable, got {actionability_rate}% actionable")

    def _is_response_actionable(self, response, suggestion):
        """Helper method to determine if a response is actionable"""
        response_content = response.content.lower()

        # Check for error indicators
        error_indicators = [
            'error', 'sorry', "can't", "don't understand", 'trouble',
            'issue', 'problem', 'failed', 'unable'
        ]

        if any(indicator in response_content for indicator in error_indicators):
            return False

        # Check for meaningful response length
        if len(response.content.strip()) < 20:
            return False

        # Check for appropriate response type
        valid_response_types = [
            'task_created', 'task_list', 'suggestion', 'analytics',
            'text', 'time_summary', 'structured_fallback'
        ]

        if response.message_type not in valid_response_types:
            return False

        # Check for contextually appropriate response
        suggestion_lower = suggestion.lower()

        if 'create' in suggestion_lower and 'task' in suggestion_lower:
            # Should create a task or provide task creation guidance
            return ('task' in response_content and
                   ('created' in response_content or 'create' in response_content))

        elif 'show' in suggestion_lower or 'list' in suggestion_lower:
            # Should show tasks or provide guidance about viewing tasks
            return ('task' in response_content or 'show' in response_content)

        elif 'suggest' in suggestion_lower or 'recommend' in suggestion_lower:
            # Should provide suggestions
            return ('suggest' in response_content or 'recommend' in response_content or
                   'tip' in response_content or 'advice' in response_content)

        elif 'stats' in suggestion_lower or 'productivity' in suggestion_lower:
            # Should provide analytics or statistics
            return ('stat' in response_content or 'analytic' in response_content or
                   'productive' in response_content or 'time' in response_content)

        # For other suggestions, just verify it's not an error
        return True

    def test_domain_compliance(self):
        """Test that all suggestions are appropriately IT/project management focused"""
        suggestions = self.chatbot.get_conversation_suggestions()

        # Define appropriate IT/project management keywords
        it_keywords = [
            'task', 'project', 'schedule', 'planning', 'development', 'coding',
            'system', 'application', 'software', 'technical', 'implementation',
            'deployment', 'testing', 'review', 'analysis', 'design', 'architecture',
            'maintenance', 'update', 'optimization', 'productivity', 'management',
            'work', 'time', 'organize', 'prioritize', 'track', 'monitor'
        ]

        # Define inappropriate keywords that should not appear
        inappropriate_keywords = [
            'medical', 'health', 'doctor', 'patient', 'medication', 'hospital',
            'bank', 'finance', 'investment', 'stock', 'money', 'loan', 'credit',
            'weather', 'restaurant', 'recipe', 'movie', 'music', 'sports', 'game',
            'politics', 'news', 'celebrity', 'entertainment', 'personal life'
        ]

        compliant_suggestions = 0

        for suggestion in suggestions:
            suggestion_lower = suggestion.lower()

            # Check for appropriate keywords
            has_it_keywords = any(keyword in suggestion_lower for keyword in it_keywords)

            # Check for inappropriate keywords
            has_inappropriate_keywords = any(keyword in suggestion_lower
                                           for keyword in inappropriate_keywords)

            # Suggestion should have IT keywords and no inappropriate keywords
            is_compliant = has_it_keywords and not has_inappropriate_keywords

            if is_compliant:
                compliant_suggestions += 1
            else:
                print(f"Non-compliant suggestion: {suggestion}")

        compliance_rate = (compliant_suggestions / len(suggestions)) * 100

        # Require 100% domain compliance
        self.assertEqual(compliance_rate, 100.0,
            f"All suggestions should be IT-focused, got {compliance_rate}% compliant")

    def test_coverage_analysis(self):
        """Test that suggestions adequately represent chatbot capabilities"""
        suggestions = self.chatbot.get_conversation_suggestions()

        # Map suggestions to their corresponding intents
        intent_coverage = set()

        for suggestion in suggestions:
            # Determine intent for each suggestion
            intent, _ = self.chatbot._understand_intent(suggestion)
            intent_coverage.add(intent)

        # Define key capabilities that should be represented
        key_capabilities = [
            'create_task', 'show_tasks', 'get_suggestions', 'analytics_request',
            'time_tracking', 'help_request'
        ]

        covered_capabilities = 0
        for capability in key_capabilities:
            if capability in intent_coverage:
                covered_capabilities += 1

        coverage_rate = (covered_capabilities / len(key_capabilities)) * 100

        # Require at least 80% coverage of key capabilities
        self.assertGreaterEqual(coverage_rate, 80.0,
            f"Suggestions should cover key capabilities, got {coverage_rate}% coverage")

    def test_suggestion_diversity(self):
        """Test that suggestions provide good diversity of starting points"""
        suggestions = self.chatbot.get_conversation_suggestions()

        # Categorize suggestions by type
        categories = {
            'task_creation': [],
            'task_viewing': [],
            'analytics': [],
            'help': [],
            'planning': [],
            'optimization': []
        }

        for suggestion in suggestions:
            suggestion_lower = suggestion.lower()

            if 'create' in suggestion_lower or 'add' in suggestion_lower:
                categories['task_creation'].append(suggestion)
            elif 'show' in suggestion_lower or 'list' in suggestion_lower:
                categories['task_viewing'].append(suggestion)
            elif 'stats' in suggestion_lower or 'productivity' in suggestion_lower:
                categories['analytics'].append(suggestion)
            elif 'help' in suggestion_lower or 'assist' in suggestion_lower:
                categories['help'].append(suggestion)
            elif 'plan' in suggestion_lower or 'schedule' in suggestion_lower:
                categories['planning'].append(suggestion)
            elif 'optimize' in suggestion_lower or 'prioritize' in suggestion_lower:
                categories['optimization'].append(suggestion)

        # Verify diversity across categories
        non_empty_categories = sum(1 for cat_suggestions in categories.values()
                                 if len(cat_suggestions) > 0)

        self.assertGreaterEqual(non_empty_categories, 3,
            "Suggestions should span multiple categories for diversity")

    def test_user_experience_quality(self):
        """Test suggestions from a user experience perspective"""
        suggestions = self.chatbot.get_conversation_suggestions()

        ux_quality_checks = []

        for suggestion in suggestions:
            # Check suggestion clarity
            is_clear = (
                len(suggestion.split()) >= 3 and  # Not too short
                len(suggestion.split()) <= 12 and  # Not too long
                suggestion[0].isupper() and        # Proper capitalization
                not suggestion.endswith('...')     # Complete sentences
            )

            # Check suggestion helpfulness
            helpful_indicators = [
                'create', 'show', 'help', 'track', 'analyze', 'optimize',
                'plan', 'manage', 'view', 'get', 'find'
            ]

            is_helpful = any(indicator in suggestion.lower()
                           for indicator in helpful_indicators)

            # Check suggestion specificity
            is_specific = (
                'task' in suggestion.lower() or
                'project' in suggestion.lower() or
                'schedule' in suggestion.lower() or
                'time' in suggestion.lower() or
                'work' in suggestion.lower()
            )

            ux_quality = is_clear and is_helpful and is_specific
            ux_quality_checks.append(ux_quality)

            if not ux_quality:
                print(f"UX quality issue with suggestion: {suggestion}")

        quality_rate = (sum(ux_quality_checks) / len(ux_quality_checks)) * 100

        # Require 90% UX quality rate
        self.assertGreaterEqual(quality_rate, 90.0,
            f"Suggestions should meet UX quality standards, got {quality_rate}% quality")

    def test_suggestion_response_relevance(self):
        """Test that suggestion responses are relevant and timely"""
        suggestions = self.chatbot.get_conversation_suggestions()

        relevant_responses = 0

        for suggestion in suggestions:
            response = self.chatbot.process_message(suggestion)

            # Check relevance by comparing suggestion keywords with response
            suggestion_keywords = set(word.lower() for word in suggestion.split()
                                    if len(word) > 3)
            response_keywords = set(word.lower() for word in response.content.split()
                                  if len(word) > 3)

            # Calculate keyword overlap
            overlap = len(suggestion_keywords.intersection(response_keywords))
            relevance_score = overlap / len(suggestion_keywords) if suggestion_keywords else 0

            # Also check for semantic relevance
            suggestion_lower = suggestion.lower()
            response_lower = response.content.lower()

            semantic_relevance = False
            if 'create' in suggestion_lower and 'task' in suggestion_lower:
                semantic_relevance = 'task' in response_lower and ('create' in response_lower or 'add' in response_lower)
            elif 'show' in suggestion_lower:
                semantic_relevance = 'show' in response_lower or 'list' in response_lower or 'task' in response_lower
            elif 'stats' in suggestion_lower or 'productivity' in suggestion_lower:
                semantic_relevance = any(word in response_lower for word in ['stat', 'analytic', 'productive', 'time'])
            else:
                semantic_relevance = True  # Default to relevant for other cases

            is_relevant = relevance_score > 0.1 or semantic_relevance

            if is_relevant:
                relevant_responses += 1

        relevance_rate = (relevant_responses / len(suggestions)) * 100

        # Require 95% relevance rate
        self.assertGreaterEqual(relevance_rate, 95.0,
            f"Suggestion responses should be relevant, got {relevance_rate}% relevant")

    def test_suggestion_value_proposition(self):
        """Test that suggestions demonstrate key value propositions"""
        suggestions = self.chatbot.get_conversation_suggestions()

        # Key value propositions the chatbot should demonstrate
        value_propositions = {
            'productivity': ['productivity', 'efficient', 'optimize', 'time'],
            'organization': ['organize', 'manage', 'plan', 'schedule'],
            'automation': ['create', 'generate', 'automat', 'suggest'],
            'insights': ['stats', 'analytics', 'insights', 'track', 'analyze'],
            'convenience': ['show', 'quick', 'easy', 'help', 'assist']
        }

        demonstrated_values = set()

        for suggestion in suggestions:
            suggestion_lower = suggestion.lower()

            for value_prop, keywords in value_propositions.items():
                if any(keyword in suggestion_lower for keyword in keywords):
                    demonstrated_values.add(value_prop)

        value_coverage = len(demonstrated_values) / len(value_propositions) * 100

        # Require at least 80% value proposition coverage
        self.assertGreaterEqual(value_coverage, 80.0,
            f"Suggestions should demonstrate key value propositions, got {value_coverage}% coverage")

    def test_suggestion_integration_with_api(self):
        """Test suggestions work with the API endpoint (if available)"""
        # This test would verify API integration in a full implementation
        # For now, just verify the method exists and returns data

        suggestions = self.chatbot.get_conversation_suggestions()

        # Verify the method returns the expected format
        self.assertIsInstance(suggestions, list)
        self.assertTrue(all(isinstance(s, str) for s in suggestions))

        # Verify suggestions are non-empty and reasonable
        self.assertTrue(all(len(s.strip()) > 5 for s in suggestions))
        self.assertLessEqual(len(suggestions), 20)  # Reasonable upper limit
        self.assertGreaterEqual(len(suggestions), 3)  # Minimum useful number

    def test_comprehensive_success_metrics(self):
        """Test comprehensive success metrics for the suggestion system"""
        suggestions = self.chatbot.get_conversation_suggestions()

        # Collect all metrics
        metrics = {
            'total_suggestions': len(suggestions),
            'actionable_count': 0,
            'domain_compliant_count': 0,
            'ux_quality_count': 0,
            'relevant_response_count': 0
        }

        # Test each suggestion
        for suggestion in suggestions:
            # Test actionability
            response = self.chatbot.process_message(suggestion)
            if self._is_response_actionable(response, suggestion):
                metrics['actionable_count'] += 1

            # Test domain compliance
            suggestion_lower = suggestion.lower()
            it_keywords = ['task', 'project', 'work', 'time', 'schedule', 'productivity']
            bad_keywords = ['medical', 'financial', 'weather', 'personal']

            if (any(kw in suggestion_lower for kw in it_keywords) and
                not any(kw in suggestion_lower for kw in bad_keywords)):
                metrics['domain_compliant_count'] += 1

            # Test UX quality
            if (3 <= len(suggestion.split()) <= 12 and
                suggestion[0].isupper() and
                not suggestion.endswith('...')):
                metrics['ux_quality_count'] += 1

            # Test response relevance
            if len(response.content) > 20 and 'error' not in response.content.lower():
                metrics['relevant_response_count'] += 1

        # Calculate success rates
        total = metrics['total_suggestions']
        if total > 0:
            actionability_rate = (metrics['actionable_count'] / total) * 100
            compliance_rate = (metrics['domain_compliant_count'] / total) * 100
            quality_rate = (metrics['ux_quality_count'] / total) * 100
            relevance_rate = (metrics['relevant_response_count'] / total) * 100

            # Overall success criteria
            overall_success = (
                actionability_rate >= 100.0 and
                compliance_rate >= 100.0 and
                quality_rate >= 90.0 and
                relevance_rate >= 95.0
            )

            self.assertTrue(overall_success,
                f"Comprehensive success metrics failed:\n"
                f"Actionability: {actionability_rate}% (need 100%)\n"
                f"Domain compliance: {compliance_rate}% (need 100%)\n"
                f"UX quality: {quality_rate}% (need 90%)\n"
                f"Response relevance: {relevance_rate}% (need 95%)")


if __name__ == '__main__':
    unittest.main()
