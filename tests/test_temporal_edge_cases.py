#!/usr/bin/env python3
"""
Comprehensive test suite for temporal calculation edge cases
Tests leap years, month boundaries, quarter transitions, and week number calculations
"""

import unittest
import unittest.mock
import sys
import os
from datetime import date, timedelta
from unittest.mock import MagicMock

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.chatbot_engine import ChatbotEngine
from app.data_manager import DataManager
from app.ai_engine import AITaskEngine
from app.analytics import AnalyticsEngine


class TestTemporalEdgeCases(unittest.TestCase):
    """Test suite for temporal calculation edge cases"""

    def setUp(self):
        """Set up test fixtures with mock dependencies"""
        self.mock_data_manager = MagicMock(spec=DataManager)
        self.mock_ai_engine = MagicMock(spec=AITaskEngine)
        self.mock_analytics = MagicMock(spec=AnalyticsEngine)

        self.chatbot = ChatbotEngine(
            self.mock_data_manager,
            self.mock_ai_engine,
            self.mock_analytics
        )

    def test_quarter_edge_cases(self):
        """Test quarter calculation edge cases"""
        print("🧪 Testing Quarter Edge Cases...")

        # Test valid quarters
        test_cases = [
            ('Q1', 2024, date(2024, 1, 1), date(2024, 3, 31)),
            ('Q2', 2024, date(2024, 4, 1), date(2024, 6, 30)),
            ('Q3', 2024, date(2024, 7, 1), date(2024, 9, 30)),
            ('Q4', 2024, date(2024, 10, 1), date(2024, 12, 31)),
        ]

        for quarter, year, expected_start, expected_end in test_cases:
            with self.subTest(quarter=quarter, year=year):
                start, end = self.chatbot._calculate_quarter_dates(quarter, year)
                self.assertEqual(start, expected_start)
                self.assertEqual(end, expected_end)
                print(f"   ✅ {quarter} {year}: {start} to {end}")

        # Test leap year Q1 (includes Feb 29)
        start, end = self.chatbot._calculate_quarter_dates('Q1', 2024)  # 2024 is leap year
        self.assertEqual(start, date(2024, 1, 1))
        self.assertEqual(end, date(2024, 3, 31))
        print(f"   ✅ Leap year Q1 2024: {start} to {end}")

        # Test alternative quarter formats
        alternative_formats = [
            ('quarter 1', 'Q1'),
            ('QUARTER1', 'Q1'),
            ('first quarter', 'Q1'),
            ('1', 'Q1'),
            ('4', 'Q4')
        ]

        for alt_format, expected in alternative_formats:
            try:
                start, end = self.chatbot._calculate_quarter_dates(alt_format, 2024)
                expected_start, expected_end = self.chatbot._calculate_quarter_dates(expected, 2024)
                self.assertEqual(start, expected_start)
                self.assertEqual(end, expected_end)
                print(f"   ✅ Alternative format '{alt_format}' -> {expected}")
            except ValueError as e:
                self.fail(f"Failed to parse alternative format '{alt_format}': {e}")

        # Test invalid quarters
        invalid_quarters = ['Q5', 'Quarter 0', 'invalid', '', None]
        for invalid in invalid_quarters:
            with self.subTest(invalid_quarter=invalid):
                with self.assertRaises(ValueError):
                    self.chatbot._calculate_quarter_dates(invalid, 2024)
                print(f"   ✅ Correctly rejected invalid quarter: {invalid}")

    def test_month_boundary_edge_cases(self):
        """Test month boundary edge cases"""
        print("🧪 Testing Month Boundary Edge Cases...")

        # Test December to January transition
        # Mock today as December 15, 2024
        with unittest.mock.patch('chatbot_engine.date') as mock_date:
            mock_date.today.return_value = date(2024, 12, 15)
            mock_date.side_effect = lambda *args, **kw: date(*args, **kw)

            # Test next month (should be January 2025)
            start, end = self.chatbot._calculate_month_dates('next_month')
            self.assertEqual(start, date(2025, 1, 1))
            self.assertEqual(end, date(2025, 1, 31))
            print(f"   ✅ Dec->Jan transition: {start} to {end}")

        # Test January to December transition
        with unittest.mock.patch('chatbot_engine.date') as mock_date:
            mock_date.today.return_value = date(2024, 1, 15)
            mock_date.side_effect = lambda *args, **kw: date(*args, **kw)

            # Test last month (should be December 2023)
            start, end = self.chatbot._calculate_month_dates('last_month')
            self.assertEqual(start, date(2023, 12, 1))
            self.assertEqual(end, date(2023, 12, 31))
            print(f"   ✅ Jan->Dec transition: {start} to {end}")

        # Test leap year February
        with unittest.mock.patch('chatbot_engine.date') as mock_date:
            mock_date.today.return_value = date(2024, 2, 15)  # 2024 is leap year
            mock_date.side_effect = lambda *args, **kw: date(*args, **kw)

            start, end = self.chatbot._calculate_month_dates('this_month')
            self.assertEqual(start, date(2024, 2, 1))
            self.assertEqual(end, date(2024, 2, 29))  # Leap year has 29 days
            print(f"   ✅ Leap year February: {start} to {end}")

        # Test non-leap year February
        with unittest.mock.patch('chatbot_engine.date') as mock_date:
            mock_date.today.return_value = date(2023, 2, 15)  # 2023 is not leap year
            mock_date.side_effect = lambda *args, **kw: date(*args, **kw)

            start, end = self.chatbot._calculate_month_dates('this_month')
            self.assertEqual(start, date(2023, 2, 1))
            self.assertEqual(end, date(2023, 2, 28))  # Non-leap year has 28 days
            print(f"   ✅ Non-leap year February: {start} to {end}")

        # Test alternative month period formats
        alternative_formats = [
            ('current_month', 'this_month'),
            ('previous_month', 'last_month'),
            ('past_month', 'last_month'),
            ('following_month', 'next_month'),
            ('upcoming_month', 'next_month')
        ]

        for alt_format, expected in alternative_formats:
            try:
                # Use a fixed date for consistent testing
                with unittest.mock.patch('chatbot_engine.date') as mock_date:
                    mock_date.today.return_value = date(2024, 6, 15)
                    mock_date.side_effect = lambda *args, **kw: date(*args, **kw)

                    start_alt, end_alt = self.chatbot._calculate_month_dates(alt_format)
                    start_exp, end_exp = self.chatbot._calculate_month_dates(expected)
                    self.assertEqual(start_alt, start_exp)
                    self.assertEqual(end_alt, end_exp)
                    print(f"   ✅ Alternative format '{alt_format}' -> {expected}")
            except ValueError as e:
                self.fail(f"Failed to parse alternative format '{alt_format}': {e}")

    def test_week_number_edge_cases(self):
        """Test week number calculation edge cases"""
        print("🧪 Testing Week Number Edge Cases...")

        # Test week 1 of different years
        test_years = [2020, 2021, 2022, 2023, 2024, 2025]
        for year in test_years:
            try:
                start, end = self.chatbot._calculate_week_dates('1', year)
                # Week 1 should always contain January 4th (ISO 8601 standard)
                jan_4 = date(year, 1, 4)
                self.assertTrue(start <= jan_4 <= end,
                              f"Week 1 of {year} should contain Jan 4: {start} to {end}")
                print(f"   ✅ Week 1 of {year}: {start} to {end} (contains Jan 4: {jan_4})")
            except ValueError as e:
                self.fail(f"Failed to calculate week 1 of {year}: {e}")

        # Test last week of year (week 52/53)
        for year in test_years:
            try:
                # Get the number of weeks in the year
                weeks_in_year = self.chatbot._get_weeks_in_year(year)
                last_week = str(weeks_in_year)

                start, end = self.chatbot._calculate_week_dates(last_week, year)
                # Last week should contain December 28th (ISO 8601 standard)
                dec_28 = date(year, 12, 28)
                self.assertTrue(start <= dec_28 <= end,
                              f"Last week of {year} should contain Dec 28: {start} to {end}")
                print(f"   ✅ Week {weeks_in_year} of {year}: {start} to {end} (contains Dec 28: {dec_28})")
            except ValueError as e:
                self.fail(f"Failed to calculate last week of {year}: {e}")

        # Test invalid week numbers
        invalid_weeks = ['0', '54', '100', 'invalid', '', None]
        for invalid in invalid_weeks:
            with self.subTest(invalid_week=invalid):
                with self.assertRaises(ValueError):
                    self.chatbot._calculate_week_dates(invalid, 2024)
                print(f"   ✅ Correctly rejected invalid week: {invalid}")

        # Test week 53 existence
        # Some years have 53 weeks, others only 52
        years_with_53_weeks = []
        years_with_52_weeks = []

        for year in range(2020, 2030):
            weeks_in_year = self.chatbot._get_weeks_in_year(year)
            if weeks_in_year == 53:
                years_with_53_weeks.append(year)
                # Test that week 53 exists
                start, end = self.chatbot._calculate_week_dates('53', year)
                print(f"   ✅ {year} has 53 weeks, week 53: {start} to {end}")
            else:
                years_with_52_weeks.append(year)
                # Test that week 53 doesn't exist
                with self.assertRaises(ValueError):
                    self.chatbot._calculate_week_dates('53', year)
                print(f"   ✅ {year} has only 52 weeks, week 53 correctly rejected")

    def test_leap_year_handling(self):
        """Test leap year handling across all temporal methods"""
        print("🧪 Testing Leap Year Handling...")

        leap_years = [2000, 2004, 2008, 2012, 2016, 2020, 2024, 2028]
        non_leap_years = [1900, 2001, 2002, 2003, 2005, 2021, 2022, 2023]

        for year in leap_years:
            # Test Q1 in leap year (should include Feb 29)
            start, end = self.chatbot._calculate_quarter_dates('Q1', year)
            self.assertEqual(end, date(year, 3, 31))

            # Test February in leap year
            feb_start, feb_end = self.chatbot._get_month_boundaries(year, 2)
            self.assertEqual(feb_end, date(year, 2, 29))
            print(f"   ✅ Leap year {year}: Feb has 29 days, Q1 ends {end}")

        for year in non_leap_years:
            # Test February in non-leap year
            feb_start, feb_end = self.chatbot._get_month_boundaries(year, 2)
            self.assertEqual(feb_end, date(year, 2, 28))
            print(f"   ✅ Non-leap year {year}: Feb has 28 days")

        # Test edge case: year 1900 (divisible by 100 but not 400, so not leap)
        feb_start, feb_end = self.chatbot._get_month_boundaries(1900, 2)
        self.assertEqual(feb_end, date(1900, 2, 28))
        print(f"   ✅ Year 1900 (special case): Feb has 28 days")

        # Test edge case: year 2000 (divisible by 400, so is leap)
        feb_start, feb_end = self.chatbot._get_month_boundaries(2000, 2)
        self.assertEqual(feb_end, date(2000, 2, 29))
        print(f"   ✅ Year 2000 (special case): Feb has 29 days")

    def test_date_boundary_validation(self):
        """Test comprehensive date boundary validation"""
        print("🧪 Testing Date Boundary Validation...")

        # Test valid date ranges
        valid_ranges = [
            (date(2024, 1, 1), date(2024, 1, 31), "same month"),
            (date(2024, 1, 1), date(2024, 12, 31), "same year"),
            (date(2024, 1, 1), date(2025, 1, 1), "year boundary"),
        ]

        for start, end, context in valid_ranges:
            try:
                result = self.chatbot._validate_date_boundaries(start, end, context)
                self.assertTrue(result)
                print(f"   ✅ Valid range {context}: {start} to {end}")
            except ValueError as e:
                self.fail(f"Valid range {context} was rejected: {e}")

        # Test invalid date ranges
        invalid_ranges = [
            (date(2024, 1, 31), date(2024, 1, 1), "start after end"),
            (date(1800, 1, 1), date(1800, 12, 31), "too far in past"),
            (date(2200, 1, 1), date(2200, 12, 31), "too far in future"),
        ]

        for start, end, context in invalid_ranges:
            with self.subTest(context=context):
                with self.assertRaises(ValueError):
                    self.chatbot._validate_date_boundaries(start, end, context)
                print(f"   ✅ Correctly rejected {context}: {start} to {end}")

    def test_temporal_input_normalization(self):
        """Test temporal input normalization"""
        print("🧪 Testing Temporal Input Normalization...")

        normalization_cases = [
            ('  This Month  ', 'this month'),
            ('CURRENT QUARTER', 'this quarter'),
            ('Previous Year', 'last year'),
            ('upcoming week', 'next week'),
            ('   Q1   ', 'q1'),
        ]

        for input_str, expected in normalization_cases:
            result = self.chatbot._normalize_temporal_input(input_str)
            # Note: the actual normalization might be more complex
            self.assertIsInstance(result, str)
            self.assertEqual(result.strip(), expected.strip().lower())
            print(f"   ✅ '{input_str}' -> '{result}'")

        # Test invalid inputs
        invalid_inputs = [None, '', '   ']
        for invalid in invalid_inputs:
            with self.subTest(invalid_input=invalid):
                with self.assertRaises(ValueError):
                    self.chatbot._normalize_temporal_input(invalid)
                print(f"   ✅ Correctly rejected invalid input: {repr(invalid)}")

    def test_master_temporal_validation(self):
        """Test the master temporal validation method"""
        print("🧪 Testing Master Temporal Validation...")

        # Test all temporal types with valid inputs
        test_cases = [
            ('quarter', 'Q1', 2024),
            ('quarter', 'Q4', None),  # Use current year
            ('month', 'this_month', None),
            ('month', 'last_month', None),
            ('year', 'this_year', None),
            ('year', 'last_year', None),
            ('week', '1', 2024),
            ('week', '15', None),  # Use current year
        ]

        for temporal_type, temporal_value, year in test_cases:
            try:
                start, end = self.chatbot._validate_and_calculate_temporal_range(
                    temporal_type, temporal_value, year
                )
                self.assertIsInstance(start, date)
                self.assertIsInstance(end, date)
                self.assertLessEqual(start, end)
                print(f"   ✅ {temporal_type} '{temporal_value}' {year or 'current'}: {start} to {end}")
            except ValueError as e:
                self.fail(f"Valid temporal calculation failed: {e}")

        # Test invalid temporal types
        with self.assertRaises(ValueError):
            self.chatbot._validate_and_calculate_temporal_range('invalid_type', 'Q1', 2024)
        print(f"   ✅ Correctly rejected invalid temporal type")

    def test_quarter_year_transitions(self):
        """Test quarter calculations across year boundaries"""
        print("🧪 Testing Quarter-Year Transitions...")

        # Test quarters in different years
        year_tests = [2020, 2023, 2024, 2025, 2028]  # Mix of leap and non-leap years

        for year in year_tests:
            for quarter in ['Q1', 'Q2', 'Q3', 'Q4']:
                start, end = self.chatbot._calculate_quarter_dates(quarter, year)

                # Verify the quarter belongs to the correct year
                self.assertEqual(start.year, year)
                self.assertEqual(end.year, year)

                # Verify quarter boundaries are correct
                if quarter == 'Q1':
                    self.assertEqual(start.month, 1)
                    self.assertEqual(end.month, 3)
                elif quarter == 'Q2':
                    self.assertEqual(start.month, 4)
                    self.assertEqual(end.month, 6)
                elif quarter == 'Q3':
                    self.assertEqual(start.month, 7)
                    self.assertEqual(end.month, 9)
                elif quarter == 'Q4':
                    self.assertEqual(start.month, 10)
                    self.assertEqual(end.month, 12)

                print(f"   ✅ {quarter} {year}: {start} to {end}")

    def run_all_tests(self):
        """Run all temporal edge case tests"""
        print("=" * 60)
        print("🚀 RUNNING COMPREHENSIVE TEMPORAL EDGE CASE TESTS")
        print("=" * 60)

        test_methods = [
            self.test_quarter_edge_cases,
            self.test_month_boundary_edge_cases,
            self.test_week_number_edge_cases,
            self.test_leap_year_handling,
            self.test_date_boundary_validation,
            self.test_temporal_input_normalization,
            self.test_master_temporal_validation,
            self.test_quarter_year_transitions,
        ]

        passed_tests = 0
        total_tests = len(test_methods)

        for test_method in test_methods:
            try:
                test_method()
                passed_tests += 1
                print(f"✅ {test_method.__name__} PASSED\n")
            except Exception as e:
                print(f"❌ {test_method.__name__} FAILED: {e}\n")

        print("=" * 60)
        print(f"🎯 TEMPORAL EDGE CASE TEST SUMMARY")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {passed_tests}")
        print(f"   Failed: {total_tests - passed_tests}")
        print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")

        if passed_tests == total_tests:
            print("   🎉 ALL TEMPORAL EDGE CASE TESTS PASSED!")
        else:
            print("   ⚠️  Some temporal edge case tests need attention")

        print("=" * 60)


if __name__ == '__main__':
    # Run the comprehensive test suite
    test_suite = TestTemporalEdgeCases()
    test_suite.setUp()
    test_suite.run_all_tests()

    # Also run with unittest for more detailed output if needed
    print("\n" + "=" * 60)
    print("🔍 RUNNING DETAILED UNITTEST ANALYSIS")
    print("=" * 60)
    unittest.main(verbosity=2, exit=False)
