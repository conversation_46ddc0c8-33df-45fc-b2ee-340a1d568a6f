#!/usr/bin/env python3
"""
Test script to verify analytics user isolation and migration works correctly.
"""

import os
import sys
import json
import tempfile
import shutil
from pathlib import Path

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Import after fixing the path
import analytics
import config

AnalyticsEngine = analytics.AnalyticsEngine
Config = config.Config

def test_analytics_user_isolation():
    """Test that analytics files are user-specific"""

    print("🧪 Testing Analytics User Isolation")
    print("=" * 50)

    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Using temporary directory: {temp_dir}")

        # Create legacy shared files
        legacy_analytics = {
            'suggestions': {'test_suggestion': {'count': 5}},
            'user_behavior': {'test_behavior': 'data'},
            'performance': {},
            'ab_tests': {}
        }

        legacy_events = [
            {
                'event_type': 'test_event',
                'timestamp': '2025-01-15T10:00:00',
                'user_id': 'jackvincent.balcita',
                'data': {'test': 'data1'}
            },
            {
                'event_type': 'another_event',
                'timestamp': '2025-01-15T11:00:00',
                'user_id': 'other_user',
                'data': {'test': 'data2'}
            }
        ]

        # Create legacy files
        legacy_analytics_file = os.path.join(temp_dir, 'analytics.json')
        legacy_events_file = os.path.join(temp_dir, 'user_events.json')

        with open(legacy_analytics_file, 'w') as f:
            json.dump(legacy_analytics, f, indent=2)

        with open(legacy_events_file, 'w') as f:
            json.dump(legacy_events, f, indent=2)

        print(f"✅ Created legacy analytics.json with {len(legacy_analytics)} sections")
        print(f"✅ Created legacy user_events.json with {len(legacy_events)} events")

        # Initialize analytics engine with the temp directory
        analytics_engine = AnalyticsEngine(data_dir=temp_dir)

        # Check that user-specific files were created
        username = analytics_engine.username
        expected_analytics_file = os.path.join(temp_dir, f'analytics_{username}.json')
        expected_events_file = os.path.join(temp_dir, f'user_events_{username}.json')

        print(f"🔍 Checking for user-specific files for user: {username}")

        if os.path.exists(expected_analytics_file):
            print(f"✅ User-specific analytics file created: analytics_{username}.json")

            # Check migration
            with open(expected_analytics_file, 'r') as f:
                migrated_data = json.load(f)

            if 'suggestions' in migrated_data and 'test_suggestion' in migrated_data['suggestions']:
                print("✅ Analytics data successfully migrated")
            else:
                print("❌ Analytics data migration failed")

        else:
            print(f"❌ User-specific analytics file not created")

        if os.path.exists(expected_events_file):
            print(f"✅ User-specific events file created: user_events_{username}.json")

            # Check event migration and filtering
            with open(expected_events_file, 'r') as f:
                migrated_events = json.load(f)

            user_events = [e for e in migrated_events if e.get('user_id') == username or e.get('user_id') == 'default_user']
            other_user_events = [e for e in migrated_events if e.get('user_id') != username and e.get('user_id') != 'default_user']

            print(f"✅ Events migrated: {len(migrated_events)} total")
            print(f"   - User events: {len(user_events)}")
            print(f"   - Other user events: {len(other_user_events)}")

        else:
            print(f"❌ User-specific events file not created")

        # Test analytics tracking
        print("\n🔬 Testing Analytics Tracking")
        print("-" * 30)

        analytics_engine.track_event('test_isolation', {
            'test_data': 'user_specific',
            'timestamp': 'now'
        }, user_id=username)

        print("✅ Event tracked successfully")

        # Check that event was saved to user-specific file
        with open(expected_events_file, 'r') as f:
            updated_events = json.load(f)

        if any(e.get('event_type') == 'test_isolation' for e in updated_events):
            print("✅ Event saved to user-specific file")
        else:
            print("❌ Event not saved correctly")

        # Test multiple user simulation
        print("\n👥 Testing Multiple User Simulation")
        print("-" * 35)

        # Simulate different user by creating another analytics engine
        # (In real scenario, this would be different system users)
        simulated_user_dir = os.path.join(temp_dir, 'user_testuser')
        os.makedirs(simulated_user_dir, exist_ok=True)

        # Manually create files for simulated user
        simulated_analytics_file = os.path.join(simulated_user_dir, 'analytics_testuser.json')
        simulated_events_file = os.path.join(simulated_user_dir, 'user_events_testuser.json')

        with open(simulated_analytics_file, 'w') as f:
            json.dump({'suggestions': {}, 'user_behavior': {}, 'performance': {}, 'ab_tests': {}}, f)

        with open(simulated_events_file, 'w') as f:
            json.dump([], f)

        print("✅ Simulated second user's analytics files")

        # List all analytics files
        analytics_files = [f for f in os.listdir(temp_dir) if f.startswith('analytics_')]
        events_files = [f for f in os.listdir(temp_dir) if f.startswith('user_events_')]

        print(f"📊 Analytics files found: {analytics_files}")
        print(f"📝 Events files found: {events_files}")

        if len(analytics_files) >= 1 and len(events_files) >= 1:
            print("✅ User isolation working correctly")
        else:
            print("❌ User isolation may have issues")

        print("\n🎯 Analytics Migration Test Complete!")
        print("=" * 50)

        return True

def test_sharepoint_integration():
    """Test SharePoint directory integration"""

    print("\n🌐 Testing SharePoint Integration")
    print("=" * 40)

    # Simulate SharePoint environment variables
    os.environ['ADHOCLOG_SHAREPOINT_MODE'] = 'true'

    with tempfile.TemporaryDirectory() as temp_dir:
        # Create user directory structure
        user_dir = os.path.join(temp_dir, 'user_jackvincent.balcita')
        os.makedirs(user_dir, exist_ok=True)

        os.environ['ADHOCLOG_USER_DATA_DIR'] = user_dir

        # Create new config and analytics engine
        config = Config()
        print(f"📁 SharePoint mode: {config.IS_SHAREPOINT_DEPLOYMENT}")
        print(f"📁 Effective data dir: {config.EFFECTIVE_DATA_DIR}")

        analytics_engine = AnalyticsEngine()

        # Verify files are created in user directory
        expected_analytics = os.path.join(user_dir, f'analytics_{analytics_engine.username}.json')
        expected_events = os.path.join(user_dir, f'user_events_{analytics_engine.username}.json')

        if os.path.exists(expected_analytics):
            print("✅ Analytics file created in SharePoint user directory")
        else:
            print("❌ Analytics file not in SharePoint user directory")

        if os.path.exists(expected_events):
            print("✅ Events file created in SharePoint user directory")
        else:
            print("❌ Events file not in SharePoint user directory")

        # Clean up environment
        del os.environ['ADHOCLOG_SHAREPOINT_MODE']
        del os.environ['ADHOCLOG_USER_DATA_DIR']

        print("✅ SharePoint integration test complete")

if __name__ == '__main__':
    try:
        test_analytics_user_isolation()
        test_sharepoint_integration()
        print("\n🎉 All tests completed successfully!")

    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
