#!/usr/bin/env python3
"""
Enhanced Chatbot Functionality Tests
Tests the new date-based queries, task filtering, and interactive features
"""

import sys
import os
import json
from datetime import datetime, date, timedelta

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_enhanced_chatbot():
    """Test the enhanced chatbot functionality with comprehensive scenarios"""

    print("🤖 Testing Enhanced AI Chatbot Functionality")
    print("=" * 60)

    try:
        # Import required modules
        from app.chatbot_engine import ChatbotEngine
        from app.data_manager import DataManager
        from app.ai_engine import AITaskEngine
        from app.analytics import AnalyticsEngine

        print("✅ Successfully imported all modules")

        # Initialize components
        data_manager = DataManager()
        ai_engine = AITaskEngine()
        analytics_engine = AnalyticsEngine()
        chatbot = ChatbotEngine(data_manager, ai_engine, analytics_engine)

        print("✅ Successfully initialized chatbot engine")

        # Create some sample tasks for testing
        today = date.today()
        yesterday = today - timedelta(days=1)

        sample_tasks = [
            {
                'title': 'Review quarterly reports',
                'description': 'Analyze Q4 financial data',
                'date': today.strftime('%Y-%m-%d'),
                'classification': 'Planning',
                'est_time': 60,
                'completed': False
            },
            {
                'title': 'Team meeting preparation',
                'description': 'Prepare agenda and materials',
                'date': yesterday.strftime('%Y-%m-%d'),
                'classification': 'Business Support Activities',
                'est_time': 30,
                'completed': True
            },
            {
                'title': 'Database optimization',
                'description': 'Optimize query performance',
                'date': today.strftime('%Y-%m-%d'),
                'classification': 'Execution',
                'est_time': 90,
                'completed': False
            }
        ]

        # Add sample tasks to data manager
        for task in sample_tasks:
            try:
                data_manager.add_task(task)
            except Exception as e:
                print(f"Note: Task may already exist - {e}")

        print("✅ Sample tasks prepared for testing")
        print()

        # Test scenarios
        # Enhanced test scenarios with new capabilities
        test_scenarios = [
            {
                'name': 'Date-based Query (Yesterday)',
                'query': 'show me all my tasks from yesterday',
                'expected_keywords': ['yesterday', 'task', 'team meeting']
            },
            {
                'name': 'Date-based Query (Today)',
                'query': 'what tasks do I have today?',
                'expected_keywords': ['today', 'task', 'review', 'database']
            },
            {
                'name': 'Status-based Query (Completed)',
                'query': 'show me my completed tasks',
                'expected_keywords': ['completed', 'task', 'team meeting']
            },
            {
                'name': 'Status-based Query (Pending)',
                'query': 'list my pending tasks',
                'expected_keywords': ['pending', 'task', 'review', 'database']
            },
            {
                'name': 'Classification Query',
                'query': 'show me planning tasks',
                'expected_keywords': ['planning', 'task', 'review']
            },
            {
                'name': 'Search Functionality',
                'query': 'search for tasks about meeting',
                'expected_keywords': ['found', 'meeting', 'team']
            },
            {
                'name': 'Time Tracking Query',
                'query': 'how much time do I have scheduled today?',
                'expected_keywords': ['time', 'today', 'minutes']
            },
            {
                'name': 'Help Request',
                'query': 'help me with the chatbot features',
                'expected_keywords': ['help', 'assistant', 'tasks', 'natural']
            },
            {
                'name': 'Export Information',
                'query': 'how do I export my tasks?',
                'expected_keywords': ['export', 'csv', 'tasks', 'download']
            },
            {
                'name': 'Task Creation',
                'query': 'create a task for reviewing budget analysis',
                'expected_keywords': ['created', 'budget', 'analysis']
            },
            {
                'name': 'Help Guide Example - Today Tasks',
                'query': 'Show me today\'s tasks',
                'expected_keywords': ['today', 'tasks', 'review', 'database']
            },
            {
                'name': 'Help Guide Example - Classification Filter',
                'query': 'Show me all my Business Support Activities tasks',
                'expected_keywords': ['business support activities', 'tasks']
            },
            {
                'name': 'Dropdown Option - Create Task',
                'query': 'Create Task',
                'expected_keywords': ['create', 'task', 'help']
            },
            {
                'name': 'Dropdown Option - Show Schedule',
                'query': 'Show Schedule',
                'expected_keywords': ['schedule', 'tasks', 'today']
            },
            {
                'name': 'Dropdown Option - Get Suggestions',
                'query': 'Get Suggestions',
                'expected_keywords': ['suggestions', 'work', 'next']
            },
            {
                'name': 'Help Guide Example - Yesterday Query',
                'query': 'What tasks did I have yesterday?',
                'expected_keywords': ['yesterday', 'tasks', 'team meeting']
            },
            {
                'name': 'Help Guide Example - Productivity Query',
                'query': 'How productive have I been this week?',
                'expected_keywords': ['productive', 'week', 'time', 'analytics']
            }
        ]

        # Run test scenarios
        passed_tests = 0
        total_tests = len(test_scenarios)
        detailed_results = []

        for i, scenario in enumerate(test_scenarios, 1):
            print(f"🧪 Test {i}/{total_tests}: {scenario['name']}")
            print(f"   Query: '{scenario['query']}'")

            try:
                # Process the message
                response = chatbot.process_message(scenario['query'])

                # Check if response exists and has content
                if response and hasattr(response, 'content') and response.content:
                    content_lower = response.content.lower()

                    # Check for expected keywords
                    keywords_found = []
                    keywords_missing = []

                    for keyword in scenario['expected_keywords']:
                        if keyword.lower() in content_lower:
                            keywords_found.append(keyword)
                        else:
                            keywords_missing.append(keyword)

                    # Enhanced success criteria (95% keyword match required)
                    keyword_ratio = len(keywords_found) / len(scenario['expected_keywords'])
                    response_quality = (
                        len(response.content) > 20 and  # Substantial response
                        keyword_ratio >= 0.75 and      # 75% keyword match minimum
                        not any(error in content_lower for error in ['error', 'sorry', "can't", 'trouble'])  # No errors
                    )

                    # Test follow-up scenario if present
                    follow_up_passed = True
                    if 'follow_up' in scenario:
                        follow_up = scenario['follow_up']
                        follow_up_response = chatbot.process_message(follow_up['query'])

                        if follow_up_response and follow_up_response.content:
                            follow_up_content = follow_up_response.content.lower()
                            follow_up_keywords_found = sum(1 for kw in follow_up['expected_keywords']
                                                         if kw.lower() in follow_up_content)
                            follow_up_ratio = follow_up_keywords_found / len(follow_up['expected_keywords'])
                            follow_up_passed = follow_up_ratio >= 0.5

                            if follow_up_passed:
                                print(f"   🔗 Follow-up PASSED: '{follow_up['query']}'")
                            else:
                                print(f"   🔗 Follow-up FAILED: '{follow_up['query']}'")
                        else:
                            follow_up_passed = False

                    test_passed = response_quality and follow_up_passed

                    if test_passed:
                        print(f"   ✅ PASSED - Response length: {len(response.content)} chars")
                        print(f"   📝 Keywords found: {', '.join(keywords_found)} ({keyword_ratio:.1%})")
                        passed_tests += 1
                    else:
                        print(f"   ❌ FAILED - Keywords missing: {', '.join(keywords_missing)}")
                        print(f"   📝 Keyword match: {keyword_ratio:.1%}")
                        print(f"   📝 Response preview: {response.content[:100]}...")

                    # Show response type and additional metadata
                    if hasattr(response, 'message_type'):
                        print(f"   🏷️  Response type: {response.message_type}")

                    # Validate response time (basic performance check)
                    if len(response.content) > 0:
                        print(f"   ⚡ Response generated successfully")

                    # Store detailed results for analysis
                    detailed_results.append({
                        'name': scenario['name'],
                        'passed': test_passed,
                        'keyword_ratio': keyword_ratio,
                        'response_length': len(response.content),
                        'message_type': getattr(response, 'message_type', 'unknown')
                    })

                else:
                    print(f"   ❌ FAILED - No valid response received")
                    detailed_results.append({
                        'name': scenario['name'],
                        'passed': False,
                        'keyword_ratio': 0.0,
                        'response_length': 0,
                        'message_type': 'none'
                    })

            except Exception as e:
                print(f"   ❌ ERROR - Exception occurred: {str(e)}")
                detailed_results.append({
                    'name': scenario['name'],
                    'passed': False,
                    'keyword_ratio': 0.0,
                    'response_length': 0,
                    'message_type': 'error'
                })

            print()

        # Enhanced Test Summary with detailed analysis
        print("=" * 60)
        print(f"🎯 ENHANCED TEST SUMMARY")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {passed_tests}")
        print(f"   Failed: {total_tests - passed_tests}")

        success_rate = (passed_tests/total_tests)*100
        print(f"   Success Rate: {success_rate:.1f}%")

        # Break down results by category
        categories = {
            'Task Management': [r for r in detailed_results if 'task' in r['name'].lower()],
            'Temporal Queries': [r for r in detailed_results if any(word in r['name'].lower()
                               for word in ['date', 'time', 'quarter', 'month', 'year', 'week'])],
            'Domain Filtering': [r for r in detailed_results if 'out-of-scope' in r['name'].lower()],
            'Structured Fallback': [r for r in detailed_results if 'fallback' in r['name'].lower()],
            'Advanced Features': [r for r in detailed_results if any(word in r['name'].lower()
                                for word in ['analytics', 'optimization', 'suggestion', 'complex'])]
        }

        print("\n📊 CATEGORY BREAKDOWN:")
        for category, results in categories.items():
            if results:
                passed_in_category = sum(1 for r in results if r['passed'])
                category_rate = (passed_in_category / len(results)) * 100
                print(f"   {category}: {passed_in_category}/{len(results)} ({category_rate:.1f}%)")

        # Success rate targets
        if success_rate >= 95.0:
            print("\n   🎉 EXCELLENT! 95%+ success rate achieved - Enhanced chatbot is working perfectly!")
        elif success_rate >= 90.0:
            print("\n   🎊 VERY GOOD! 90%+ success rate - Most enhanced features are working well!")
        elif success_rate >= 80.0:
            print("\n   👍 GOOD! 80%+ success rate - Core enhanced features are functional!")
        elif success_rate >= 70.0:
            print("\n   ⚠️  NEEDS IMPROVEMENT! 70%+ success rate - Some enhanced features need attention")
        else:
            print("\n   🚨 SIGNIFICANT ISSUES! <70% success rate - Enhanced features need major fixes")

        # Performance insights
        avg_response_length = sum(r['response_length'] for r in detailed_results) / len(detailed_results)
        avg_keyword_match = sum(r['keyword_ratio'] for r in detailed_results) / len(detailed_results)

        print(f"\n📈 PERFORMANCE METRICS:")
        print(f"   Average response length: {avg_response_length:.0f} characters")
        print(f"   Average keyword match: {avg_keyword_match:.1%}")

        # Message type distribution
        message_types = {}
        for result in detailed_results:
            msg_type = result['message_type']
            message_types[msg_type] = message_types.get(msg_type, 0) + 1

        print(f"\n🏷️  MESSAGE TYPE DISTRIBUTION:")
        for msg_type, count in sorted(message_types.items()):
            print(f"   {msg_type}: {count}")

        print()

        # Test additional chatbot features
        print("🔍 Testing Additional Features:")

        # Test message creation functionality
        try:
            test_msg = chatbot._create_message("Test message", "text")
            print(f"   ✅ Message creation: {test_msg.message_type}")
        except Exception as e:
            print(f"   ❌ Message creation error: {e}")

        # Test intent recognition
        try:
            intent, info = chatbot._understand_intent("create a task for testing")
            print(f"   ✅ Intent recognition: detected '{intent}'")
        except Exception as e:
            print(f"   ❌ Intent recognition error: {e}")

        # Test command pattern matching
        try:
            test_patterns = [
                "show me today's tasks",
                "create a task for planning",
                "help me with the app"
            ]
            patterns_working = 0
            for pattern in test_patterns:
                try:
                    intent, info = chatbot._understand_intent(pattern)
                    if intent != 'unknown':
                        patterns_working += 1
                except:
                    pass
            print(f"   ✅ Command patterns: {patterns_working}/{len(test_patterns)} working")
        except Exception as e:
            print(f"   ❌ Pattern matching error: {e}")

        print()
        print("🚀 Enhanced Chatbot Testing Complete!")
        print("   The chatbot now supports:")
        print("   • Date-based task queries (yesterday, today, this week)")
        print("   • Status-based filtering (completed, pending)")
        print("   • Classification-based filtering")
        print("   • Task search functionality")
        print("   • Time tracking and analytics")
        print("   • Comprehensive help system")
        print("   • Export guidance")
        print("   • Natural language interaction")

        return passed_tests >= total_tests * 0.7

    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("   Make sure all required modules are available")
        return False
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        return False

if __name__ == "__main__":
    success = test_enhanced_chatbot()
    sys.exit(0 if success else 1)
