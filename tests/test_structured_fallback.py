"""
Structured Fallback Response Testing for AdhocLog Chatbot
Tests intelligent fallback mechanisms and structured response handling
"""

import unittest
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.chatbot_engine import Chatbot<PERSON>ng<PERSON>
from app.data_manager import DataManager
from app.ai_engine import AITaskEngine
from app.analytics import AnalyticsEngine


class TestStructuredFallback(unittest.TestCase):
    """Test structured fallback response validation"""

    def setUp(self):
        """Set up test environment"""
        self.data_manager = DataManager()
        self.ai_engine = AITaskEngine()
        self.analytics_engine = AnalyticsEngine()
        self.chatbot = ChatbotEngine(self.data_manager, self.ai_engine, self.analytics_engine)

    def test_vague_query_fallback(self):
        """Test fallback responses for vague queries"""
        vague_queries = [
            "help me",
            "I need assistance",
            "what can you do",
            "help",
            "I'm not sure what to ask",
            "can you help",
            "what should I do",
            "I need help with something"
        ]

        for query in vague_queries:
            with self.subTest(query=query):
                response = self.chatbot.process_message(query)

                # Verify structured fallback response
                self.assertIn('I can help you with:', response.content)
                self.assertIn('Try asking me things like:', response.content)

                # Verify message type is structured fallback or help
                self.assertIn(response.message_type, ['structured_fallback', 'text'])

                # Verify response provides actionable guidance
                actionable_keywords = ['create', 'show', 'task', 'schedule', 'time', 'help']
                response_lower = response.content.lower()
                self.assertTrue(
                    any(keyword in response_lower for keyword in actionable_keywords),
                    f"Vague query response should provide actionable guidance: {query}"
                )

    def test_incomplete_query_fallback(self):
        """Test fallback responses for incomplete queries"""
        incomplete_queries = [
            "show me",
            "create a",
            "I want to",
            "how do I",
            "can you show",
            "help me with",
            "I need to",
            "what about"
        ]

        for query in incomplete_queries:
            with self.subTest(query=query):
                response = self.chatbot.process_message(query)

                # Verify response provides completion guidance
                self.assertTrue(
                    any(phrase in response.content.lower()
                        for phrase in ['more specific', 'example', 'try asking', 'like this']),
                    f"Incomplete query should receive completion guidance: {query}"
                )

                # Verify examples are provided
                self.assertTrue(
                    '•' in response.content or 'example' in response.content.lower(),
                    f"Response should include examples for incomplete query: {query}"
                )

    def test_ambiguous_query_fallback(self):
        """Test fallback responses for ambiguous queries"""
        ambiguous_queries = [
            "yesterday",
            "the project",
            "that thing",
            "it",
            "them",
            "those tasks",
            "the meeting",
            "my work"
        ]

        for query in ambiguous_queries:
            with self.subTest(query=query):
                response = self.chatbot.process_message(query)

                # Verify response requests clarification
                clarification_phrases = [
                    'more specific', 'which', 'what', 'clarify', 'specific',
                    'details', 'be more', 'tell me more'
                ]

                response_lower = response.content.lower()
                self.assertTrue(
                    any(phrase in response_lower for phrase in clarification_phrases),
                    f"Ambiguous query should request clarification: {query}"
                )

    def test_typo_handling_fallback(self):
        """Test fallback responses for queries with typos"""
        typo_queries = [
            "crete task",          # create task
            "shwo tasks",          # show tasks
            "taks for today",      # tasks for today
            "tim tracking",        # time tracking
            "hlep me",            # help me
            "prject planning",     # project planning
            "schdule meeting",     # schedule meeting
            "analitycs"           # analytics
        ]

        for query in typo_queries:
            with self.subTest(query=query):
                response = self.chatbot.process_message(query)

                # Verify response provides helpful alternatives instead of errors
                self.assertNotIn('error', response.content.lower())

                # Verify response includes suggestions or examples
                self.assertTrue(
                    any(word in response.content.lower()
                        for word in ['try', 'example', 'ask', 'help', 'like']),
                    f"Typo query should receive helpful alternatives: {query}"
                )

    def test_structured_response_components(self):
        """Test that structured fallback responses contain required components"""
        unclear_query = "I'm not sure what to ask"
        response = self.chatbot.process_message(unclear_query)

        # Verify response structure
        required_sections = [
            'Task Management',
            'Schedule & Planning',
            'Analytics',
            'Examples'
        ]

        response_content = response.content
        sections_found = sum(1 for section in required_sections
                           if any(keyword in response_content for keyword in section.split()))

        self.assertGreaterEqual(sections_found, 2,
            "Structured fallback should include multiple help categories")

        # Verify actionable examples are provided
        example_indicators = ['•', 'example', 'try asking', 'like this']
        self.assertTrue(
            any(indicator in response_content.lower() for indicator in example_indicators),
            "Structured fallback should include actionable examples"
        )

    def test_context_aware_fallback(self):
        """Test that fallback responses are contextually appropriate"""
        # Test task-related unclear query
        task_unclear = "something about tasks"
        response = self.chatbot.process_message(task_unclear)

        # Should provide task-specific guidance
        self.assertIn('task', response.content.lower())

        # Test date-related unclear query
        date_unclear = "something about dates"
        response = self.chatbot.process_message(date_unclear)

        # Should provide date/schedule-specific guidance
        self.assertTrue(
            any(keyword in response.content.lower()
                for keyword in ['date', 'schedule', 'time', 'when']),
            "Date-related unclear query should receive date-specific guidance"
        )

    def test_fallback_suggestions_actionability(self):
        """Test that all suggestions in fallback responses are actionable"""
        unclear_query = "I don't know what to ask"
        response = self.chatbot.process_message(unclear_query)

        # Extract suggestions from response metadata if available
        suggestions = []
        if hasattr(response, 'metadata') and response.metadata:
            suggestions = response.metadata.get('suggestions', [])

        # If no metadata suggestions, extract from content
        if not suggestions:
            # Look for bullet points or quoted suggestions in content
            import re
            pattern = r'["\'](.*?)["\']'
            suggestions = re.findall(pattern, response.content)

            # Also look for bullet point suggestions
            lines = response.content.split('\n')
            for line in lines:
                if line.strip().startswith('•') and len(line.strip()) > 5:
                    suggestion = line.strip()[1:].strip()  # Remove bullet point
                    suggestions.append(suggestion)

        # Test actionability of each suggestion
        for suggestion in suggestions[:5]:  # Test first 5 suggestions
            if len(suggestion.strip()) > 10:  # Only test substantial suggestions
                suggestion_response = self.chatbot.process_message(suggestion)

                # Verify suggestion produces a meaningful response
                self.assertNotIn('error', suggestion_response.content.lower())
                self.assertNotIn("i don't understand", suggestion_response.content.lower())
                self.assertNotIn("try again", suggestion_response.content.lower())

    def test_fallback_message_types(self):
        """Test that fallback responses use appropriate message types"""
        test_cases = [
            ("help me with tasks", 'task_related'),
            ("something about dates", 'date_related'),
            ("I need productivity help", 'analytics_related'),
            ("what can you do", 'general')
        ]

        for query, expected_category in test_cases:
            with self.subTest(query=query):
                response = self.chatbot.process_message(query)

                # Verify message type is appropriate for fallback
                expected_types = ['structured_fallback', 'text', 'help']
                self.assertIn(response.message_type, expected_types,
                    f"Fallback response should use appropriate message type for: {query}")

                # Verify metadata includes category information when structured
                if response.message_type == 'structured_fallback':
                    self.assertIsNotNone(response.metadata)
                    if response.metadata and 'category' in response.metadata:
                        self.assertEqual(response.metadata['category'], expected_category)

    def test_interactive_elements_in_fallback(self):
        """Test that fallback responses include interactive elements"""
        unclear_query = "I'm not sure how to use this"
        response = self.chatbot.process_message(unclear_query)

        # Check for interactive elements in metadata
        if response.message_type == 'structured_fallback' and response.metadata:
            suggestions = response.metadata.get('suggestions', [])

            # Verify suggestions are provided
            self.assertGreater(len(suggestions), 0,
                "Structured fallback should include interactive suggestions")

            # Verify suggestions are diverse and cover different areas
            suggestion_types = set()
            for suggestion in suggestions:
                suggestion_lower = suggestion.lower()
                if 'create' in suggestion_lower or 'task' in suggestion_lower:
                    suggestion_types.add('task_creation')
                elif 'show' in suggestion_lower or 'list' in suggestion_lower:
                    suggestion_types.add('task_viewing')
                elif 'time' in suggestion_lower or 'analytics' in suggestion_lower:
                    suggestion_types.add('analytics')
                elif 'help' in suggestion_lower or 'prioritize' in suggestion_lower:
                    suggestion_types.add('assistance')

            # Should cover at least 2 different types of actions
            self.assertGreaterEqual(len(suggestion_types), 2,
                "Fallback suggestions should cover multiple action types")

    def test_escalation_pattern_recognition(self):
        """Test that repeated unclear queries trigger enhanced guidance"""
        # Simulate multiple unclear queries in sequence
        unclear_queries = [
            "I don't know",
            "still confused",
            "this doesn't help"
        ]

        responses = []
        for query in unclear_queries:
            response = self.chatbot.process_message(query)
            responses.append(response)

        # Later responses should be more comprehensive or suggest different approaches
        # This is a basic test - a full implementation might track conversation state
        last_response = responses[-1]

        # Verify last response is comprehensive
        self.assertGreater(len(last_response.content), 200,
            "Repeated unclear queries should trigger comprehensive guidance")

    def test_fallback_professional_tone(self):
        """Test that fallback responses maintain professional, helpful tone"""
        unclear_queries = [
            "this is confusing",
            "I don't get it",
            "this doesn't work",
            "help me understand"
        ]

        for query in unclear_queries:
            with self.subTest(query=query):
                response = self.chatbot.process_message(query)

                # Verify tone is professional and encouraging
                positive_phrases = [
                    "i'd love to help", "i can help", "let me help",
                    "happy to assist", "here to help", "glad to help"
                ]

                response_lower = response.content.lower()
                self.assertTrue(
                    any(phrase in response_lower for phrase in positive_phrases),
                    f"Fallback response should be encouraging and helpful: {query}"
                )

                # Verify no negative or frustrated language
                negative_phrases = [
                    "i don't understand", "this is wrong", "you're confused",
                    "that's not right", "invalid", "error"
                ]

                self.assertFalse(
                    any(phrase in response_lower for phrase in negative_phrases),
                    f"Fallback response should avoid negative language: {query}"
                )

    def test_success_rate_structured_fallback(self):
        """Test overall success rate for structured fallback responses"""
        unclear_scenarios = [
            "help",
            "what can you do",
            "I'm confused",
            "show me",
            "I need assistance",
            "how do I",
            "what about",
            "unclear request",
            "vague question",
            "general inquiry"
        ]

        successful_fallbacks = 0

        for query in unclear_scenarios:
            response = self.chatbot.process_message(query)

            # Check if fallback response is structured and helpful
            is_structured = (
                # Has clear sections or examples
                ('•' in response.content or 'example' in response.content.lower()) and
                # Provides actionable guidance
                any(action in response.content.lower()
                    for action in ['create', 'show', 'help', 'ask', 'try']) and
                # Maintains professional tone
                ('help' in response.content.lower() or 'assist' in response.content.lower()) and
                # Provides specific suggestions
                len(response.content) > 100  # Substantial response
            )

            if is_structured:
                successful_fallbacks += 1

        success_rate = (successful_fallbacks / len(unclear_scenarios)) * 100

        # Require 100% structured fallback response rate
        self.assertEqual(success_rate, 100.0,
            f"Structured fallback success rate should be 100%, got {success_rate}%")

    def test_suggestion_actionability_rate(self):
        """Test that 100% of provided suggestions are actionable"""
        fallback_response = self.chatbot.process_message("I need help with everything")

        # Extract suggestions
        suggestions = []
        if hasattr(fallback_response, 'metadata') and fallback_response.metadata:
            suggestions = fallback_response.metadata.get('suggestions', [])

        if not suggestions:
            # Extract from content if no metadata
            lines = fallback_response.content.split('\n')
            for line in lines:
                if '•' in line and len(line.strip()) > 10:
                    suggestion = line.split('•')[1].strip()
                    # Remove quotes if present
                    suggestion = suggestion.strip('"\'')
                    if suggestion:
                        suggestions.append(suggestion)

        actionable_count = 0
        total_suggestions = len(suggestions)

        for suggestion in suggestions:
            if len(suggestion.strip()) > 5:  # Only test substantial suggestions
                suggestion_response = self.chatbot.process_message(suggestion)

                # Check if suggestion produces actionable response
                is_actionable = (
                    not any(error_phrase in suggestion_response.content.lower()
                           for error_phrase in ['error', 'sorry', "can't", "don't understand"]) and
                    len(suggestion_response.content) > 20  # Substantial response
                )

                if is_actionable:
                    actionable_count += 1

        if total_suggestions > 0:
            actionability_rate = (actionable_count / total_suggestions) * 100

            # Require 100% actionability rate for all suggestions
            self.assertEqual(actionability_rate, 100.0,
                f"All suggestions should be actionable, got {actionability_rate}% actionable")


if __name__ == '__main__':
    unittest.main()
