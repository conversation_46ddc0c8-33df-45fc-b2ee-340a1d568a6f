"""
Extended Temporal Query Testing for AdhocLog Chatbot
Tests comprehensive temporal parsing and aggregation capabilities
"""

import unittest
import sys
import os
from datetime import date, datetime, timedelta
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.chatbot_engine import ChatbotEngine
from app.data_manager import DataManager
from app.ai_engine import AITaskEngine
from app.analytics import AnalyticsEngine


class TestTemporalAggregation(unittest.TestCase):
    """Test extended temporal parsing and aggregation capabilities"""

    def setUp(self):
        """Set up test environment with sample tasks spanning multiple time periods"""
        self.data_manager = DataManager()
        self.ai_engine = AITaskEngine()
        self.analytics_engine = AnalyticsEngine()
        self.chatbot = ChatbotEngine(self.data_manager, self.ai_engine, self.analytics_engine)

        # Create sample tasks across different time periods
        self.setup_sample_tasks()

    def setup_sample_tasks(self):
        """Create sample tasks spanning multiple months, quarters, and years"""
        today = date.today()

        # Sample tasks for different time periods
        sample_tasks = [
            # Current year Q1 tasks
            {"title": "Q1 Planning Task", "date": "2025-01-15", "est_time": 120, "classification": "Planning"},
            {"title": "Q1 Development Task", "date": "2025-02-20", "est_time": 180, "classification": "Execution"},
            {"title": "Q1 Review Task", "date": "2025-03-25", "est_time": 60, "classification": "Business Support Activities"},

            # Current year Q2 tasks
            {"title": "Q2 Planning Task", "date": "2025-04-10", "est_time": 90, "classification": "Planning"},
            {"title": "Q2 Implementation", "date": "2025-05-15", "est_time": 240, "classification": "Execution"},
            {"title": "Q2 Testing", "date": "2025-06-20", "est_time": 150, "classification": "Operational Project Involvement"},

            # Current year Q3 tasks
            {"title": "Q3 Architecture", "date": "2025-07-05", "est_time": 200, "classification": "Planning"},
            {"title": "Q3 Development", "date": "2025-08-12", "est_time": 300, "classification": "Execution"},
            {"title": "Q3 Deployment", "date": "2025-09-18", "est_time": 120, "classification": "Operational Project Involvement"},

            # Current year Q4 tasks
            {"title": "Q4 Planning", "date": "2025-10-08", "est_time": 100, "classification": "Planning"},
            {"title": "Q4 Optimization", "date": "2025-11-15", "est_time": 180, "classification": "Execution"},
            {"title": "Q4 Year-end Review", "date": "2025-12-20", "est_time": 90, "classification": "Business Support Activities"},

            # Last year tasks
            {"title": "2024 Final Project", "date": "2024-12-15", "est_time": 160, "classification": "Execution"},
            {"title": "2024 Documentation", "date": "2024-11-20", "est_time": 80, "classification": "Offline Processing"},

            # Current month tasks
            {"title": f"Current Month Task 1", "date": today.strftime('%Y-%m') + "-05", "est_time": 45, "classification": "Planning"},
            {"title": f"Current Month Task 2", "date": today.strftime('%Y-%m') + "-15", "est_time": 75, "classification": "Execution"},
        ]

        # Add last month tasks
        last_month = today.replace(day=1) - timedelta(days=1)
        sample_tasks.extend([
            {"title": "Last Month Planning", "date": last_month.strftime('%Y-%m') + "-10", "est_time": 60, "classification": "Planning"},
            {"title": "Last Month Implementation", "date": last_month.strftime('%Y-%m') + "-20", "est_time": 120, "classification": "Execution"},
        ])

        # Add tasks to data manager
        for task_data in sample_tasks:
            try:
                self.data_manager.add_task(task_data)
            except Exception:
                # Skip if task already exists or fails to add
                pass

    def test_quarter_queries(self):
        """Test quarter-based temporal queries"""
        quarter_queries = [
            "Show me tasks from Q1 2025",
            "How many minutes did I spend in Q2?",
            "Tasks for quarter 3",
            "Time summary for Q4",
            "What did I work on in the first quarter?",
            "Show me second quarter tasks",
            "How much time in third quarter?",
            "Fourth quarter task breakdown"
        ]

        for query in quarter_queries:
            with self.subTest(query=query):
                response = self.chatbot.process_message(query)

                # Verify response is not an error
                self.assertNotIn('error', response.content.lower())
                self.assertNotIn('sorry', response.content.lower())

                # Verify time information is provided
                self.assertTrue(
                    any(keyword in response.content.lower()
                        for keyword in ['time', 'minutes', 'hours', 'tasks']),
                    f"Quarter query response should include time/task information: {query}"
                )

    def test_quarter_date_calculation(self):
        """Test quarter date range calculations"""
        # Test Q1 calculation
        start_date, end_date = self.chatbot._calculate_quarter_dates('Q1', 2025)
        self.assertEqual(start_date, date(2025, 1, 1))
        self.assertEqual(end_date, date(2025, 3, 31))

        # Test Q2 calculation
        start_date, end_date = self.chatbot._calculate_quarter_dates('Q2', 2025)
        self.assertEqual(start_date, date(2025, 4, 1))
        self.assertEqual(end_date, date(2025, 6, 30))

        # Test Q3 calculation
        start_date, end_date = self.chatbot._calculate_quarter_dates('Q3', 2025)
        self.assertEqual(start_date, date(2025, 7, 1))
        self.assertEqual(end_date, date(2025, 9, 30))

        # Test Q4 calculation
        start_date, end_date = self.chatbot._calculate_quarter_dates('Q4', 2025)
        self.assertEqual(start_date, date(2025, 10, 1))
        self.assertEqual(end_date, date(2025, 12, 31))

    def test_month_queries(self):
        """Test month-based temporal queries"""
        month_queries = [
            "Tasks from last month",
            "How much time did I spend this month?",
            "Show me last month's work",
            "Time breakdown for this month",
            "What did I accomplish last month?",
            "This month's task summary",
            "Previous month time analysis",
            "Current month productivity"
        ]

        for query in month_queries:
            with self.subTest(query=query):
                response = self.chatbot.process_message(query)

                # Verify response handles month queries appropriately
                self.assertNotIn('error', response.content.lower())

                # Verify time/task information is provided
                self.assertTrue(
                    any(keyword in response.content.lower()
                        for keyword in ['time', 'minutes', 'hours', 'tasks', 'month']),
                    f"Month query response should include relevant information: {query}"
                )

    def test_month_date_calculation(self):
        """Test month date range calculations"""
        today = date.today()

        # Test this month calculation
        start_date, end_date = self.chatbot._calculate_month_dates('this_month')
        self.assertEqual(start_date.year, today.year)
        self.assertEqual(start_date.month, today.month)
        self.assertEqual(start_date.day, 1)

        # Test last month calculation
        start_date, end_date = self.chatbot._calculate_month_dates('last_month')
        if today.month == 1:
            expected_month = 12
            expected_year = today.year - 1
        else:
            expected_month = today.month - 1
            expected_year = today.year

        self.assertEqual(start_date.year, expected_year)
        self.assertEqual(start_date.month, expected_month)
        self.assertEqual(start_date.day, 1)

    def test_year_queries(self):
        """Test year-based temporal queries"""
        year_queries = [
            "What did I work on last year?",
            "This year's task summary",
            "How much time spent this year?",
            "Last year's productivity analysis",
            "Show me this year's tasks",
            "Previous year time breakdown",
            "Current year progress",
            "Annual task review"
        ]

        for query in year_queries:
            with self.subTest(query=query):
                response = self.chatbot.process_message(query)

                # Verify response handles year queries
                self.assertNotIn('error', response.content.lower())

                # Verify year-related information is provided
                self.assertTrue(
                    any(keyword in response.content.lower()
                        for keyword in ['time', 'tasks', 'year', 'annual']),
                    f"Year query response should include relevant information: {query}"
                )

    def test_year_date_calculation(self):
        """Test year date range calculations"""
        today = date.today()

        # Test this year calculation
        start_date, end_date = self.chatbot._calculate_year_dates('this_year')
        self.assertEqual(start_date, date(today.year, 1, 1))
        self.assertEqual(end_date, date(today.year, 12, 31))

        # Test last year calculation
        start_date, end_date = self.chatbot._calculate_year_dates('last_year')
        self.assertEqual(start_date, date(today.year - 1, 1, 1))
        self.assertEqual(end_date, date(today.year - 1, 12, 31))

    def test_week_number_queries(self):
        """Test week number-based queries"""
        week_queries = [
            "Show me tasks from week 32",
            "How much time in week 15?",
            "Tasks for calendar week 20",
            "Week 45 task summary",
            "What did I do in week number 10?",
            "Time breakdown for week 52"
        ]

        for query in week_queries:
            with self.subTest(query=query):
                response = self.chatbot.process_message(query)

                # Verify response handles week queries
                # Note: Week queries might not have data, so just verify no errors
                self.assertNotIn('encountered an issue', response.content.lower())

    def test_week_date_calculation(self):
        """Test week number date calculations"""
        # Test week 1 calculation
        start_date, end_date = self.chatbot._calculate_week_dates('1', 2025)
        self.assertEqual((end_date - start_date).days, 6)  # Should be 7-day span

        # Test week 26 (mid-year)
        start_date, end_date = self.chatbot._calculate_week_dates('26', 2025)
        self.assertEqual((end_date - start_date).days, 6)

        # Verify start date is a Monday (weekday 0)
        self.assertEqual(start_date.weekday(), 0, "Week should start on Monday")

    def test_time_aggregation_accuracy(self):
        """Test accuracy of time aggregation calculations"""
        # Query for Q1 2025 specifically
        response = self.chatbot.process_message("How much time did I spend in Q1 2025?")

        if 'Q1' in response.content and 'minutes' in response.content:
            # Extract Q1 tasks manually to verify
            q1_start, q1_end = self.chatbot._calculate_quarter_dates('Q1', 2025)
            q1_tasks = self.data_manager.filter_tasks({
                'start_date': q1_start.strftime('%Y-%m-%d'),
                'end_date': q1_end.strftime('%Y-%m-%d')
            })['tasks']

            expected_time = sum(task.get('est_time', 0) for task in q1_tasks)

            # Verify the response mentions time information
            self.assertIn('time', response.content.lower())

            # If we have tasks, verify time is mentioned
            if expected_time > 0:
                self.assertTrue(
                    any(str(expected_time) in response.content or
                        str(expected_time // 60) in response.content  # hours
                        for _ in [1]),  # Just run the check once
                    "Response should include calculated time"
                )

    def test_classification_breakdown(self):
        """Test task classification breakdown in time summaries"""
        response = self.chatbot.process_message("Time summary for Q2 2025")

        if 'Q2' in response.content:
            # Verify classification breakdown is provided
            classification_keywords = ['planning', 'execution', 'business', 'operational']

            breakdown_mentioned = any(keyword in response.content.lower()
                                    for keyword in classification_keywords)

            # If we have Q2 tasks, breakdown should be provided
            q2_start, q2_end = self.chatbot._calculate_quarter_dates('Q2', 2025)
            q2_tasks = self.data_manager.filter_tasks({
                'start_date': q2_start.strftime('%Y-%m-%d'),
                'end_date': q2_end.strftime('%Y-%m-%d')
            })['tasks']

            if q2_tasks:
                self.assertTrue(breakdown_mentioned or 'breakdown' in response.content.lower(),
                    "Time summary should include classification breakdown when tasks exist")

    def test_edge_cases_date_boundaries(self):
        """Test edge cases for date boundaries"""
        # Test leap year handling (if applicable)
        if date.today().year % 4 == 0:  # Simplified leap year check
            start_date, end_date = self.chatbot._calculate_quarter_dates('Q1', date.today().year)
            # February should have 29 days in leap year Q1
            self.assertTrue(end_date >= date(date.today().year, 2, 29))

        # Test year boundary transitions
        start_date, end_date = self.chatbot._calculate_month_dates('last_month')
        self.assertTrue(start_date <= end_date, "Start date should be before or equal to end date")

        # Test quarter boundaries
        q4_start, q4_end = self.chatbot._calculate_quarter_dates('Q4', 2025)
        self.assertEqual(q4_end.month, 12)
        self.assertEqual(q4_end.day, 31)

    def test_integration_with_data_manager(self):
        """Test integration with DataManager.filter_tasks method"""
        # Test that temporal queries properly integrate with data filtering
        q1_start, q1_end = self.chatbot._calculate_quarter_dates('Q1', 2025)

        # Direct data manager call
        result = self.data_manager.filter_tasks({
            'start_date': q1_start.strftime('%Y-%m-%d'),
            'end_date': q1_end.strftime('%Y-%m-%d')
        })

        # Verify result structure
        self.assertIsInstance(result, dict)
        self.assertIn('tasks', result)
        self.assertIsInstance(result['tasks'], list)

        # Test through chatbot
        response = self.chatbot.process_message("Show me Q1 2025 tasks")

        # Verify the response corresponds to the data
        if result['tasks']:
            self.assertIn('task', response.content.lower())
        else:
            self.assertIn('no tasks', response.content.lower())

    def test_performance_with_large_datasets(self):
        """Test performance with larger datasets"""
        # This is a basic performance test - in a full implementation,
        # you would add timing measurements and memory usage checks

        # Query multiple periods to test efficiency
        queries = [
            "Show me Q1 tasks",
            "How much time last month?",
            "This year's summary",
            "Week 30 breakdown"
        ]

        for query in queries:
            response = self.chatbot.process_message(query)
            # Verify response is generated without errors
            self.assertIsNotNone(response)
            self.assertNotIn('error', response.content.lower())

    def test_success_rate_temporal_parsing(self):
        """Test overall success rate for temporal parsing scenarios"""
        test_scenarios = [
            # Should succeed
            ("How much time did I spend in Q1?", True),
            ("Show me last month's tasks", True),
            ("This year's task summary", True),
            ("Tasks from week 25", True),
            ("Q2 time breakdown", True),
            ("Last quarter analysis", True),

            # Edge cases that should be handled gracefully
            ("Time for quarter 5", False),  # Invalid quarter
            ("Show me week 60", False),     # Invalid week
            ("Tasks from month 15", False), # Invalid month reference
        ]

        successful_parses = 0
        total_scenarios = len([s for s in test_scenarios if s[1]])  # Only count valid scenarios

        for query, should_succeed in test_scenarios:
            if should_succeed:
                response = self.chatbot.process_message(query)

                # Check if temporal parsing succeeded
                if not any(error_word in response.content.lower()
                          for error_word in ['error', 'sorry', 'trouble', "couldn't"]):
                    successful_parses += 1

        success_rate = (successful_parses / total_scenarios) * 100 if total_scenarios > 0 else 0

        # Require 95%+ success rate for temporal parsing
        self.assertGreaterEqual(success_rate, 95.0,
            f"Temporal parsing success rate should be 95%+, got {success_rate}%")


if __name__ == '__main__':
    unittest.main()
