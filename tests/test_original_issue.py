#!/usr/bin/env python3
"""Test the original reported search issue"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.chatbot_engine import ChatbotEngine
from app.data_manager import DataManager
from app.ai_engine import AITaskEngine
from app.analytics import AnalyticsEngine

def test_original_issue():
    """Test the original issue reported by the user"""
    print("🔍 Testing Original Search Issue")
    print("=" * 50)

    # Initialize components
    data_manager = DataManager()
    ai_engine = AITaskEngine()
    analytics_engine = AnalyticsEngine()
    chatbot = ChatbotEngine(data_manager, ai_engine, analytics_engine)

    print("Original issue:")
    print("❌ User: 'look' or 'look for' responded with 'No tasks found containing 'look for''")
    print("❌ Should ask: 'What would you like to search for?'")
    print()

    # Test the original failing cases
    test_cases = [
        "look",
        "look for",
        "search for daily"
    ]

    for i, message in enumerate(test_cases, 1):
        print(f"Test {i}: '{message}'")
        response = chatbot.process_message(message)
        print(f"🤖 Response: {response.content}")

        if message in ["look", "look for"]:
            if "What would you like to search for" in response.content:
                print("✅ FIXED: Now correctly asks what to search for")
            else:
                print("❌ STILL BROKEN: Should ask what to search for")
        elif message == "search for daily":
            if "containing 'daily'" in response.content:
                print("✅ FIXED: Now searches for 'daily' instead of 'search for daily'")
            elif "containing 'search for daily'" in response.content:
                print("❌ STILL BROKEN: Still searching for command words")
            else:
                print("❓ Different response than expected")

        print()

if __name__ == "__main__":
    test_original_issue()
