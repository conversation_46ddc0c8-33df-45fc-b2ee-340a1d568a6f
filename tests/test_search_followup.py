#!/usr/bin/env python3
"""Test search functionality and follow-up responses"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.chatbot_engine import ChatbotEngine
from app.data_manager import DataManager
from app.ai_engine import AITaskEngine
from app.analytics import AnalyticsEngine

def test_search_and_followup():
    """Test search commands and follow-up responses"""
    print("🔍 Testing Search Commands and Follow-up Responses")
    print("=" * 60)

    # Initialize components
    data_manager = DataManager()
    ai_engine = AITaskEngine()
    analytics_engine = AnalyticsEngine()
    chatbot = ChatbotEngine(data_manager, ai_engine, analytics_engine)

    # Test cases for search functionality
    test_cases = [
        {
            'scenario': 'Basic search commands',
            'tests': [
                ('search', 'Should ask what to search for'),
                ('find', 'Should ask what to search for'),
                ('look for', 'Should ask what to search for'),
                ('search for', 'Should ask what to search for'),
            ]
        },
        {
            'scenario': 'Search with terms',
            'tests': [
                ('search for daily', 'Should search for "daily"'),
                ('find meetings', 'Should search for "meetings"'),
                ('look for stand-up', 'Should search for "stand-up"'),
                ('search report', 'Should search for "report"'),
                ('locate review', 'Should search for "review"'),
            ]
        },
        {
            'scenario': 'Follow-up after search prompt',
            'tests': [
                # First message: incomplete search
                ('search', 'Should ask what to search for'),
                # Follow-up response
                ('daily', 'Should search for "daily" as follow-up'),
            ]
        }
    ]

    for scenario_data in test_cases:
        scenario = scenario_data['scenario']
        print(f"\n📋 Testing: {scenario}")
        print("-" * 40)

        if scenario == 'Follow-up after search prompt':
            # Test follow-up scenario
            print("🔄 Step 1: Initial incomplete search command")
            message = 'search'
            response = chatbot.process_message(message)
            print(f"📝 Input: '{message}'")
            print(f"🤖 Response: {response.content}")

            if "What would you like to search for" in response.content:
                print("✅ Correctly asked for search term")

                print("\n🔄 Step 2: Follow-up with search term")
                message = 'daily'
                response = chatbot.process_message(message)
                print(f"📝 Input: '{message}'")
                print(f"🤖 Response: {response.content}")

                if "Found" in response.content and "daily" in response.content:
                    print("✅ Correctly performed search as follow-up")
                elif "No tasks found containing 'daily'" in response.content:
                    print("✅ Correctly performed search as follow-up (no results)")
                else:
                    print("❌ Failed to perform search as follow-up")
                    print(f"   Expected: Search results for 'daily'")
                    print(f"   Got: {response.content}")
            else:
                print("❌ Failed to ask for search term")
        else:
            # Test regular scenarios
            for message, expected in scenario_data['tests']:
                print(f"📝 Testing: '{message}'")
                response = chatbot.process_message(message)
                print(f"🤖 Response: {response.content}")

                if scenario == 'Basic search commands':
                    if "What would you like to search for" in response.content:
                        print("✅ Correctly asked for search term")
                    else:
                        print("❌ Failed to ask for search term")
                elif scenario == 'Search with terms':
                    # Extract expected search term from message
                    if 'search for ' in message:
                        expected_term = message.replace('search for ', '')
                    elif 'find ' in message:
                        expected_term = message.replace('find ', '')
                    elif 'look for ' in message:
                        expected_term = message.replace('look for ', '')
                    elif 'search ' in message:
                        expected_term = message.replace('search ', '')
                    elif 'locate ' in message:
                        expected_term = message.replace('locate ', '')
                    else:
                        expected_term = message

                    if f"containing '{expected_term}'" in response.content:
                        print(f"✅ Correctly searched for '{expected_term}'")
                    else:
                        print(f"❌ Failed to search for correct term")
                        print(f"   Expected term: '{expected_term}'")
                        print(f"   Response: {response.content}")

                print()

if __name__ == "__main__":
    test_search_and_followup()
