#!/usr/bin/env python3
"""
Script to update import statements in test files after refactoring
"""

import os
import glob
import re

def update_test_imports():
    """Update import statements in all test files"""
    test_files = glob.glob("/Users/<USER>/Documents/dev/cardinal-health/adhoc-log-app/tests/*.py")

    # Mapping of old imports to new imports
    import_mappings = {
        'from ai_engine import': 'from app.ai_engine import',
        'from data_manager import': 'from app.data_manager import',
        'from chatbot_engine import': 'from app.chatbot_engine import',
        'from analytics import': 'from app.analytics import',
        'from config import': 'from app.config import',
        'import ai_engine': 'import app.ai_engine as ai_engine',
        'import data_manager': 'import app.data_manager as data_manager',
        'import chatbot_engine': 'import app.chatbot_engine as chatbot_engine',
        'import analytics': 'import app.analytics as analytics',
        'import config': 'import app.config as config',
    }

    updated_files = []

    for test_file in test_files:
        if test_file.endswith('__init__.py'):
            continue

        print(f"Processing: {os.path.basename(test_file)}")

        try:
            with open(test_file, 'r', encoding='utf-8') as f:
                content = f.read()

            original_content = content

            # Apply import mappings
            for old_import, new_import in import_mappings.items():
                content = content.replace(old_import, new_import)

            # Write back if changed
            if content != original_content:
                with open(test_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                updated_files.append(os.path.basename(test_file))
                print(f"  ✅ Updated: {os.path.basename(test_file)}")
            else:
                print(f"  ⏭️  No changes needed: {os.path.basename(test_file)}")

        except Exception as e:
            print(f"  ❌ Error processing {os.path.basename(test_file)}: {e}")

    print(f"\n📋 Summary:")
    print(f"Total files processed: {len(test_files)}")
    print(f"Files updated: {len(updated_files)}")
    if updated_files:
        print(f"Updated files: {', '.join(updated_files)}")

if __name__ == "__main__":
    update_test_imports()
