#!/usr/bin/env python3
"""
Test script to verify search functionality fixes
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.chatbot_engine import Chatbot<PERSON>ngine
from app.data_manager import DataManager
from app.ai_engine import AITaskEngine
from app.analytics import AnalyticsEngine

def test_search_commands():
    """Test various search command scenarios"""

    # Initialize the chatbot
    data_manager = DataManager()
    ai_engine = AITaskEngine()
    analytics_engine = AnalyticsEngine()
    chatbot = ChatbotEngine(data_manager, ai_engine, analytics_engine)

    test_cases = [
        "search",
        "find",
        "look for",
        "search for",
        "look for tasks",
        "search for daily",
        "find meetings",
        "look for stand-up"
    ]

    print("🔍 Testing Search Command Fixes")
    print("=" * 50)

    for test_message in test_cases:
        print(f"\n📝 Testing: '{test_message}'")
        try:
            response = chatbot.process_message(test_message)
            print(f"🤖 Response: {response.content}")

            # Check if it properly asks for search term for incomplete commands
            if test_message in ["search", "find", "look for", "search for", "look for tasks"]:
                if "What would you like to search for" in response.content:
                    print("✅ Correctly asked for search term")
                else:
                    print("❌ Should have asked for search term")

            # Check if it searches when given a proper search term
            elif "daily" in test_message or "meetings" in test_message or "stand-up" in test_message:
                if "No tasks found containing" in response.content or "Found" in response.content:
                    print("✅ Performed search correctly")
                elif "What would you like to search for" in response.content:
                    print("❌ Should have performed search, not asked for term")

        except Exception as e:
            print(f"❌ Error: {e}")

    print("\n" + "=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    test_search_commands()
