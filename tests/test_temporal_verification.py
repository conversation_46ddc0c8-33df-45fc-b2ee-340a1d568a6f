#!/usr/bin/env python3
"""
Quick verification test for the temporal edge case handling
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import date
from app.chatbot_engine import Chatbot<PERSON><PERSON><PERSON>
from unittest.mock import Magic<PERSON>ock

def test_temporal_edge_cases_quick():
    """Quick verification of temporal edge case handling"""

    print("🔬 QUICK TEMPORAL EDGE CASE VERIFICATION")
    print("=" * 50)

    # Create chatbot with mock dependencies
    mock_data_manager = MagicMock()
    mock_ai_engine = MagicMock()
    mock_analytics = MagicMock()

    chatbot = ChatbotEngine(mock_data_manager, mock_ai_engine, mock_analytics)

    # Test cases that should work
    edge_cases = [
        # Leap year tests
        ("Quarter Q1 in leap year 2024", lambda: chatbot._calculate_quarter_dates('Q1', 2024)),
        ("February 2024 (leap year)", lambda: chatbot._get_month_boundaries(2024, 2)),
        ("February 2023 (non-leap year)", lambda: chatbot._get_month_boundaries(2023, 2)),

        # Year boundary tests
        ("December 2024", lambda: chatbot._get_month_boundaries(2024, 12)),
        ("January 2025", lambda: chatbot._get_month_boundaries(2025, 1)),

        # Week number edge cases
        ("Week 1 of 2024", lambda: chatbot._calculate_week_dates('1', 2024)),
        ("Week 52 of 2024", lambda: chatbot._calculate_week_dates('52', 2024)),
        ("Number of weeks in 2020 (53-week year)", lambda: chatbot._get_weeks_in_year(2020)),
        ("Number of weeks in 2021 (52-week year)", lambda: chatbot._get_weeks_in_year(2021)),

        # Alternative format tests
        ("Quarter alternative format", lambda: chatbot._calculate_quarter_dates('quarter 1', 2024)),
        ("Input normalization", lambda: chatbot._normalize_temporal_input('  This Month  ')),

        # Master validation tests
        ("Master temporal validation Q1", lambda: chatbot._validate_and_calculate_temporal_range('quarter', 'Q1', 2024)),
        ("Master temporal validation month", lambda: chatbot._validate_and_calculate_temporal_range('month', 'this_month')),
    ]

    # Test cases that should fail
    error_cases = [
        ("Invalid quarter", lambda: chatbot._calculate_quarter_dates('Q5', 2024)),
        ("Invalid week number", lambda: chatbot._calculate_week_dates('0', 2024)),
        ("Invalid month", lambda: chatbot._get_month_boundaries(2024, 13)),
        ("None quarter", lambda: chatbot._calculate_quarter_dates(None, 2024)),  # type: ignore
        ("Empty temporal input", lambda: chatbot._normalize_temporal_input('')),
        ("Invalid temporal type", lambda: chatbot._validate_and_calculate_temporal_range('invalid', 'Q1')),
    ]

    passed_edge_cases = 0
    passed_error_cases = 0

    print("✅ TESTING VALID EDGE CASES:")
    for name, test_func in edge_cases:
        try:
            result = test_func()
            print(f"   ✅ {name}: {result}")
            passed_edge_cases += 1
        except Exception as e:
            print(f"   ❌ {name}: FAILED - {e}")

    print(f"\n❌ TESTING ERROR CASES (should fail gracefully):")
    for name, test_func in error_cases:
        try:
            result = test_func()
            print(f"   ❌ {name}: FAILED TO REJECT - {result}")
        except (ValueError, TypeError) as e:
            print(f"   ✅ {name}: Correctly rejected - {type(e).__name__}")
            passed_error_cases += 1
        except Exception as e:
            print(f"   ⚠️  {name}: Unexpected error - {type(e).__name__}: {e}")

    print("\n" + "=" * 50)
    print(f"📊 VERIFICATION SUMMARY:")
    print(f"   Valid edge cases: {passed_edge_cases}/{len(edge_cases)} passed")
    print(f"   Error cases: {passed_error_cases}/{len(error_cases)} correctly rejected")

    total_passed = passed_edge_cases + passed_error_cases
    total_tests = len(edge_cases) + len(error_cases)
    success_rate = (total_passed / total_tests) * 100

    print(f"   Overall success rate: {success_rate:.1f}%")

    if success_rate >= 95:
        print("   🎉 EXCELLENT! Temporal edge case handling is robust!")
    elif success_rate >= 85:
        print("   ✅ GOOD! Most edge cases are handled properly!")
    else:
        print("   ⚠️  NEEDS IMPROVEMENT! Some edge cases need attention!")

    return success_rate >= 85

if __name__ == '__main__':
    test_temporal_edge_cases_quick()
