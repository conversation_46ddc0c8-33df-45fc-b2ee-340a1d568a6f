#!/usr/bin/env python3
"""Quick test for single word look command"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.chatbot_engine import ChatbotEngine
from app.data_manager import DataManager
from app.ai_engine import AITaskEngine
from app.analytics import AnalyticsEngine

def test_single_look():
    """Test the single word 'look' command"""
    print("🔍 Testing Single Word 'look' Command")
    print("=" * 40)

    # Initialize components
    data_manager = DataManager()
    ai_engine = AITaskEngine()
    analytics_engine = AnalyticsEngine()
    chatbot = ChatbotEngine(data_manager, ai_engine, analytics_engine)

    message = "look"
    print(f"📝 Testing: '{message}'")
    response = chatbot.process_message(message)
    print(f"🤖 Response: {response.content}")

    if "What would you like to search for" in response.content:
        print("✅ SUCCESS: 'look' now correctly asks what to search for")
    else:
        print("❌ FAILED: 'look' should ask what to search for")

if __name__ == "__main__":
    test_single_look()
