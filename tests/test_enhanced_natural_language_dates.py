#!/usr/bin/env python3
"""
Test Enhanced Natural Language Date Parsing for Chatbot
Tests comprehensive natural language temporal expressions like "2 months ago", "last week", etc.
"""

import sys
import os
from datetime import datetime, date, timedelta

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_natural_language_dates():
    """Test the enhanced natural language date parsing functionality"""

    print("🗓️ Testing Enhanced Natural Language Date Parsing")
    print("=" * 70)

    try:
        # Import required modules
        from app.chatbot_engine import ChatbotEngine
        from app.data_manager import DataManager
        from app.ai_engine import AITaskEngine
        from app.analytics import AnalyticsEngine

        print("✅ Successfully imported all modules")

        # Initialize components
        data_manager = DataManager()
        ai_engine = AITaskEngine()
        analytics_engine = AnalyticsEngine()
        chatbot = ChatbotEngine(data_manager, ai_engine, analytics_engine)

        print("✅ Successfully initialized chatbot engine")

        # Create comprehensive sample tasks across multiple time periods
        today = date.today()
        sample_tasks = []

        # Create tasks for various dates to test temporal queries
        date_ranges = [
            (today - timedelta(days=90), "3 months ago"),
            (today - timedelta(days=60), "2 months ago"),
            (today - timedelta(days=30), "1 month ago"),
            (today - timedelta(days=14), "2 weeks ago"),
            (today - timedelta(days=7), "1 week ago"),
            (today - timedelta(days=1), "yesterday"),
            (today, "today"),
            (today + timedelta(days=1), "tomorrow"),
            (today + timedelta(days=7), "next week"),
            (today + timedelta(days=30), "next month"),
        ]

        for task_date, description in date_ranges:
            task = {
                'title': f'Sample task from {description}',
                'description': f'Test task for {description}',
                'date': task_date.strftime('%Y-%m-%d'),
                'classification': 'Planning',
                'est_time': 30,
                'completed': False
            }
            sample_tasks.append(task)

        # Add sample tasks
        for task in sample_tasks:
            try:
                data_manager.add_task(task)
            except Exception as e:
                print(f"Note: Task may already exist - {e}")

        print("✅ Sample tasks across multiple time periods prepared")
        print()

        # Comprehensive test scenarios for natural language date expressions
        natural_language_test_scenarios = [
            {
                'category': 'Temporal Expressions (N periods ago)',
                'tests': [
                    {
                        'query': 'show me tasks from 2 months ago',
                        'expected_keywords': ['2 months ago', 'task', 'sample'],
                        'description': '2 months ago'
                    },
                    {
                        'query': 'what did I work on 3 weeks ago?',
                        'expected_keywords': ['3 weeks ago', 'task', 'work'],
                        'description': '3 weeks ago'
                    },
                    {
                        'query': 'tasks from 1 week ago',
                        'expected_keywords': ['1 week ago', 'task'],
                        'description': '1 week ago'
                    },
                    {
                        'query': 'show me work from 30 days ago',
                        'expected_keywords': ['30 days ago', 'work'],
                        'description': '30 days ago'
                    }
                ]
            },
            {
                'category': 'Temporal Ranges (last N periods)',
                'tests': [
                    {
                        'query': 'show me tasks from the last 2 months',
                        'expected_keywords': ['last 2 months', 'task'],
                        'description': 'last 2 months'
                    },
                    {
                        'query': 'what have I been working on in the past 3 weeks?',
                        'expected_keywords': ['past 3 weeks', 'working'],
                        'description': 'past 3 weeks'
                    },
                    {
                        'query': 'tasks from the previous 2 weeks',
                        'expected_keywords': ['previous 2 weeks', 'task'],
                        'description': 'previous 2 weeks'
                    },
                    {
                        'query': 'show me work from the last 30 days',
                        'expected_keywords': ['last 30 days', 'work'],
                        'description': 'last 30 days'
                    }
                ]
            },
            {
                'category': 'Natural Language Time Periods',
                'tests': [
                    {
                        'query': 'show me last month\'s tasks',
                        'expected_keywords': ['last month', 'task'],
                        'description': 'last month'
                    },
                    {
                        'query': 'what did I do last week?',
                        'expected_keywords': ['last week', 'task'],
                        'description': 'last week'
                    },
                    {
                        'query': 'tasks from this week',
                        'expected_keywords': ['this week', 'task'],
                        'description': 'this week'
                    },
                    {
                        'query': 'show me next week\'s schedule',
                        'expected_keywords': ['next week', 'schedule'],
                        'description': 'next week'
                    }
                ]
            },
            {
                'category': 'Time Summary Queries',
                'tests': [
                    {
                        'query': 'how much time did I spend in the last 2 months?',
                        'expected_keywords': ['time', 'last 2 months'],
                        'description': 'time summary for last 2 months'
                    },
                    {
                        'query': 'show me my productivity for the past month',
                        'expected_keywords': ['productivity', 'past month'],
                        'description': 'productivity for past month'
                    },
                    {
                        'query': 'work breakdown for the last 3 weeks',
                        'expected_keywords': ['work breakdown', 'last 3 weeks'],
                        'description': 'work breakdown for last 3 weeks'
                    }
                ]
            },
            {
                'category': 'Flexible Expressions',
                'tests': [
                    {
                        'query': 'what was I working on a month back?',
                        'expected_keywords': ['month back', 'working'],
                        'description': 'month back'
                    },
                    {
                        'query': 'show me tasks from 2 weeks in the past',
                        'expected_keywords': ['2 weeks', 'past', 'task'],
                        'description': '2 weeks in the past'
                    },
                    {
                        'query': 'what do I have scheduled 1 week from now?',
                        'expected_keywords': ['1 week from now', 'scheduled'],
                        'description': '1 week from now'
                    }
                ]
            }
        ]

        # Run comprehensive natural language date parsing tests
        total_tests = 0
        passed_tests = 0
        category_results = {}

        for category_data in natural_language_test_scenarios:
            category_name = category_data['category']
            category_tests = category_data['tests']
            category_passed = 0
            category_total = len(category_tests)

            print(f"\n🧪 Testing {category_name}")
            print("-" * 50)

            for i, test_case in enumerate(category_tests, 1):
                query = test_case['query']
                expected_keywords = test_case['expected_keywords']
                description = test_case['description']

                print(f"   Test {i}/{category_total}: {description}")
                print(f"   Query: '{query}'")

                try:
                    # Process the message
                    response = chatbot.process_message(query)

                    # Check if response exists and has content
                    if response and hasattr(response, 'content') and response.content:
                        content_lower = response.content.lower()

                        # Check if it's a valid response (not an error)
                        is_valid_response = True
                        error_indicators = [
                            "couldn't determine which",
                            "couldn't figure out",
                            "try being more specific",
                            "error",
                            "couldn't understand"
                        ]

                        for error_indicator in error_indicators:
                            if error_indicator in content_lower:
                                is_valid_response = False
                                break

                        if is_valid_response:
                            # Check for expected keywords
                            keywords_found = []
                            keywords_missing = []

                            for keyword in expected_keywords:
                                if keyword.lower() in content_lower:
                                    keywords_found.append(keyword)
                                else:
                                    keywords_missing.append(keyword)

                            # Determine if test passed
                            keyword_ratio = len(keywords_found) / len(expected_keywords) if expected_keywords else 1
                            response_quality = len(response.content) > 30  # Reasonable response length

                            test_passed = keyword_ratio >= 0.5 and response_quality

                            if test_passed:
                                print(f"      ✅ PASSED - Natural language parsing successful!")
                                print(f"      📝 Keywords found: {', '.join(keywords_found)}")
                                if hasattr(response, 'message_type'):
                                    print(f"      🏷️  Response type: {response.message_type}")
                                passed_tests += 1
                                category_passed += 1
                            else:
                                print(f"      ⚠️  PARTIAL - Parsed but missing keywords")
                                print(f"      📝 Missing: {', '.join(keywords_missing)}")
                                print(f"      📝 Response preview: {response.content[:100]}...")
                                passed_tests += 0.5
                                category_passed += 0.5
                        else:
                            print(f"      ❌ FAILED - Natural language not recognized")
                            print(f"      📝 Error response: {response.content[:100]}...")
                    else:
                        print(f"      ❌ FAILED - No valid response received")

                except Exception as e:
                    print(f"      ❌ ERROR - Exception occurred: {str(e)}")

                total_tests += 1

            # Category summary
            category_success_rate = (category_passed / category_total) * 100
            category_results[category_name] = {
                'passed': category_passed,
                'total': category_total,
                'rate': category_success_rate
            }

            print(f"\n   📊 {category_name} Results: {category_passed}/{category_total} ({category_success_rate:.1f}%)")

        # Overall Test Summary
        print("\n" + "=" * 70)
        print(f"🎯 ENHANCED NATURAL LANGUAGE DATE PARSING TEST SUMMARY")
        print("=" * 70)

        overall_success_rate = (passed_tests / total_tests) * 100

        print(f"📈 Overall Results:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {passed_tests}")
        print(f"   Failed: {total_tests - passed_tests}")
        print(f"   Success Rate: {overall_success_rate:.1f}%")
        print()

        print(f"📊 Category Breakdown:")
        for category, results in category_results.items():
            status_emoji = "🎉" if results['rate'] >= 80 else "👍" if results['rate'] >= 60 else "⚠️"
            print(f"   {status_emoji} {category}: {results['rate']:.1f}% ({results['passed']}/{results['total']})")
        print()

        if overall_success_rate >= 80:
            print("🎉 EXCELLENT! Enhanced natural language date parsing is working great!")
            print("   The chatbot now understands complex temporal expressions like:")
            print("   • 'tasks from 2 months ago'")
            print("   • 'what did I work on last week?'")
            print("   • 'show me the past 3 weeks'")
            print("   • 'how much time in the last month?'")
        elif overall_success_rate >= 60:
            print("👍 GOOD! Most natural language date formats are recognized!")
            print("   Consider refining edge cases for even better performance.")
        else:
            print("⚠️  Enhanced natural language date parsing needs more work")
            print("   Focus on improving the temporal expression recognition.")

        print()
        print("🚀 Enhanced Natural Language Date Parsing Testing Complete!")
        print("   The chatbot now supports:")
        print("   ✅ Temporal expressions (2 months ago, 3 weeks ago)")
        print("   ✅ Temporal ranges (last 2 months, past 3 weeks)")
        print("   ✅ Natural language periods (last month, this week)")
        print("   ✅ Flexible time references (month back, weeks in the past)")
        print("   ✅ Time summary queries with temporal expressions")

        return overall_success_rate >= 70

    except ImportError as e:
        print(f"❌ Import Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        print(f"Exception details: {type(e).__name__}: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_enhanced_natural_language_dates()
    sys.exit(0 if success else 1)
