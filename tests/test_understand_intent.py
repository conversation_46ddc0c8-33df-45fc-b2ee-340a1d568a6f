#!/usr/bin/env python3
"""
Debug the full _understand_intent flow
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.chatbot_engine import Chatbot<PERSON>ngine
from app.data_manager import DataManager
from app.ai_engine import AITaskEngine
from app.analytics import AnalyticsEngine

def test_understand_intent():
    """Test the full _understand_intent method"""

    # Initialize components
    data_manager = DataManager()
    ai_engine = AITaskEngine()
    analytics_engine = AnalyticsEngine()

    chatbot = ChatbotEngine(data_manager, ai_engine, analytics_engine)

    message = "show me tasks this week"
    print(f"Testing _understand_intent for: '{message}'")

    # Test the date detection first
    date_info = chatbot._detect_date_references(message)
    print(f"Date detection result: {date_info}")

    # Test the full intent understanding
    intent, extracted_info = chatbot._understand_intent(message)
    print(f"Intent: {intent}")
    print(f"Extracted info:")
    for key, value in extracted_info.items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    test_understand_intent()
