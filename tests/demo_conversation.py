#!/usr/bin/env python3
"""
Interactive demo of conversation context and follow-up functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.chatbot_engine import ChatbotEngine
from app.data_manager import DataManager
from app.ai_engine import AITaskEngine
from app.analytics import Analytics<PERSON>ng<PERSON>

def interactive_demo():
    """Interactive demo of conversation context"""
    print("🤖 AdhocLog Chatbot - Conversation Context Demo")
    print("=" * 60)
    print("This demo shows how the chatbot remembers previous conversations")
    print("and can answer follow-up questions!")
    print("\nTry this conversation flow:")
    print("1. 'Show me tasks for July 10'")
    print("2. 'Show only the completed ones' (follow-up)")
    print("3. 'How much time will they take?' (follow-up)")
    print("4. 'Tell me more details' (follow-up)")
    print("\nType 'quit' to exit, 'reset' to clear conversation history")
    print("=" * 60)

    # Initialize chatbot
    data_manager = DataManager()
    ai_engine = AITaskEngine()
    analytics_engine = AnalyticsEngine()
    chatbot = ChatbotEngine(data_manager, ai_engine, analytics_engine)

    conversation_count = 0

    while True:
        try:
            user_input = input(f"\n👤 You: ").strip()

            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("👋 Goodbye!")
                break

            if user_input.lower() == 'reset':
                chatbot.context.clear_context()
                print("🔄 Conversation history cleared!")
                conversation_count = 0
                continue

            if not user_input:
                continue

            conversation_count += 1

            # Get chatbot response
            response = chatbot.process_message(user_input)

            # Show response
            print(f"\n🤖 Assistant: {response.content}")

            # Show conversation context info
            history_count = len(chatbot.context.conversation_history)
            follow_up_count = chatbot.context.follow_up_count

            if history_count > 1:
                print(f"📊 Context: {history_count} interactions, {follow_up_count} follow-ups detected")

                if response.message_type == 'text' and any(phrase in response.content.lower() for phrase in ['previous', 'those', 'them', 'follow-up']):
                    print("🔗 This was a follow-up response based on conversation history!")

        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def demo_script():
    """Run automated demo script"""
    print("🎬 Running Automated Conversation Demo")
    print("=" * 60)

    # Initialize chatbot
    data_manager = DataManager()
    ai_engine = AITaskEngine()
    analytics_engine = AnalyticsEngine()
    chatbot = ChatbotEngine(data_manager, ai_engine, analytics_engine)

    # Demo conversation
    demo_messages = [
        "Show me tasks for July 10",
        "Show only the completed ones",
        "How much time will they take?",
        "Tell me more about those tasks",
        "Create a similar task"
    ]

    for i, message in enumerate(demo_messages, 1):
        print(f"\n{i}. 👤 User: {message}")
        response = chatbot.process_message(message)
        print(f"   🤖 Assistant: {response.content[:150]}{'...' if len(response.content) > 150 else ''}")

        # Show if it was detected as follow-up
        history = chatbot.context.conversation_history
        if len(history) > 1:
            latest = history[-1]
            if latest.get('intent', '').startswith('follow_up'):
                print(f"   🔗 Detected as: {latest['intent']}")

    print(f"\n📊 Final conversation state:")
    print(f"   • Total interactions: {len(chatbot.context.conversation_history)}")
    print(f"   • Follow-ups detected: {chatbot.context.follow_up_count}")
    print(f"   • Last mentioned tasks: {len(chatbot.context.last_mentioned_tasks or [])}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == 'demo':
        demo_script()
    else:
        interactive_demo()
