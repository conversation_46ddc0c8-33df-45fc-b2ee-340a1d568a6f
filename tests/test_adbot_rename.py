#!/usr/bin/env python3
"""
Test script to verify AdBot rename is working
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.chatbot_engine import ChatbotEngine
from app.data_manager import DataManager
from app.ai_engine import AITaskEngine
from app.analytics import AnalyticsEngine

def test_adbot_rename():
    """Test that the chatbot now identifies itself as AdBot"""

    # Initialize components
    data_manager = DataManager()
    ai_engine = AITaskEngine()
    analytics_engine = AnalyticsEngine()

    chatbot = ChatbotEngine(data_manager, ai_engine, analytics_engine)

    # Test greeting messages
    test_messages = [
        "hello",
        "hi",
        "help",
        "what can you do"
    ]

    print("=== Testing AdBot Rename ===\n")

    for message in test_messages:
        print(f"Testing message: '{message}'")

        try:
            response = chatbot.process_message(message)
            print(f"Response: {response.content[:150]}...")

            # Check if response contains "AdBot"
            if "AdBot" in response.content:
                print("✅ PASS - Response contains 'AdBot'")
            else:
                print("❌ FAIL - Response doesn't contain 'AdBot'")

        except Exception as e:
            print(f"❌ ERROR: {e}")

        print()

if __name__ == "__main__":
    test_adbot_rename()
