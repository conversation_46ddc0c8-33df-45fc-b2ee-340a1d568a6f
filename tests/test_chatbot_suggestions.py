#!/usr/bin/env python3
"""
Test script for verifying all chatbot conversation suggestions work properly
This script tests each suggestion to ensure they are processed correctly by the chatbot engine.
"""

import sys
import os
import json
from datetime import datetime, date
from typing import List, Dict, Any, Optional

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.chatbot_engine import ChatbotEngine
from app.data_manager import DataManager
from app.ai_engine import AITaskEngine
from app.analytics import AnalyticsEngine


class ChatbotSuggestionTester:
    """Test all chatbot conversation suggestions"""

    def __init__(self):
        """Initialize the test environment"""
        # Initialize components
        self.data_manager = DataManager()
        self.ai_engine = AITaskEngine()
        self.analytics_engine = AnalyticsEngine()
        self.chatbot = ChatbotEngine(self.data_manager, self.ai_engine, self.analytics_engine)

        # Create some sample tasks for testing
        self._create_sample_tasks()

        # Get the suggestions to test
        self.suggestions = self.chatbot.get_conversation_suggestions()

        # Test results
        self.test_results = []
        self.passed_tests = 0
        self.failed_tests = 0

    def _create_sample_tasks(self):
        """Create sample tasks for testing"""
        sample_tasks = [
            {
                'title': 'Review quarterly reports',
                'classification': 'Planning',
                'description': 'Review Q3 quarterly reports for accuracy',
                'est_time': 45,
                'date': date.today().strftime('%Y-%m-%d'),
                'completed': False
            },
            {
                'title': 'System maintenance check',
                'classification': 'Execution',
                'description': 'Perform routine system maintenance',
                'est_time': 30,
                'date': date.today().strftime('%Y-%m-%d'),
                'completed': True
            },
            {
                'title': 'Team meeting preparation',
                'classification': 'Business Support Activities',
                'description': 'Prepare agenda for weekly team meeting',
                'est_time': 20,
                'date': (date.today()).strftime('%Y-%m-%d'),
                'completed': False
            },
            {
                'title': 'Documentation update',
                'classification': 'Planning',
                'description': 'Update project documentation',
                'est_time': 60,
                'date': date.today().strftime('%Y-%m-%d'),
                'completed': False
            }
        ]

        for task_data in sample_tasks:
            try:
                self.data_manager.add_task(task_data)
            except Exception as e:
                print(f"Warning: Could not create sample task '{task_data['title']}': {e}")

    def test_suggestion(self, suggestion: str) -> Dict[str, Any]:
        """Test a single suggestion"""
        print(f"\n🧪 Testing suggestion: '{suggestion}'")

        test_result = {
            'suggestion': suggestion,
            'success': False,
            'response_type': None,
            'response_content': None,
            'error': None,
            'metadata': None
        }

        try:
            # Process the suggestion through the chatbot
            response = self.chatbot.process_message(suggestion)

            # Check if we got a valid response
            if response and response.content:
                test_result['success'] = True
                test_result['response_type'] = response.message_type
                test_result['response_content'] = response.content[:200] + "..." if len(response.content) > 200 else response.content
                test_result['metadata'] = response.metadata

                print(f"✅ SUCCESS - Response type: {response.message_type}")
                print(f"   Response preview: {response.content[:100]}{'...' if len(response.content) > 100 else ''}")

                # Additional validation based on suggestion type
                validation_result = self._validate_response(suggestion, response)
                if not validation_result['valid']:
                    test_result['success'] = False
                    test_result['error'] = f"Validation failed: {validation_result['reason']}"
                    print(f"❌ VALIDATION FAILED: {validation_result['reason']}")

            else:
                test_result['error'] = "No response received"
                print(f"❌ FAILED - No response received")

        except Exception as e:
            test_result['error'] = str(e)
            print(f"❌ FAILED - Exception: {e}")

        return test_result

    def _validate_response(self, suggestion: str, response) -> Dict[str, Any]:
        """Validate that the response is appropriate for the suggestion"""
        suggestion_lower = suggestion.lower()

        # Check for task creation suggestions
        if any(word in suggestion_lower for word in ['create', 'generate']):
            if response.message_type not in ['task_created', 'task_list', 'text']:
                return {'valid': False, 'reason': f'Expected task creation response, got {response.message_type}'}

            # Check if task was actually created for create suggestions
            if 'create a task' in suggestion_lower and response.message_type != 'task_created':
                if 'created' not in response.content.lower():
                    return {'valid': False, 'reason': 'Task creation response should confirm task was created'}

        # Check for show/display suggestions
        elif any(word in suggestion_lower for word in ['show', 'display', 'list']):
            if response.message_type not in ['task_list', 'text', 'analytics']:
                return {'valid': False, 'reason': f'Expected task list response, got {response.message_type}'}

        # Check for time/analytics suggestions
        elif any(word in suggestion_lower for word in ['time', 'spend', 'much']):
            if response.message_type not in ['analytics', 'time_summary', 'text']:
                return {'valid': False, 'reason': f'Expected analytics response, got {response.message_type}'}

        # Check for help/guidance suggestions
        elif any(word in suggestion_lower for word in ['help', 'organize', 'prioritize', 'what should']):
            if response.message_type not in ['suggestion', 'text', 'structured_fallback']:
                return {'valid': False, 'reason': f'Expected suggestion response, got {response.message_type}'}

        # Check for break down suggestions
        elif 'break down' in suggestion_lower:
            if response.message_type not in ['task_list', 'text']:
                return {'valid': False, 'reason': f'Expected task breakdown response, got {response.message_type}'}

        # Check response has meaningful content
        if len(response.content.strip()) < 10:
            return {'valid': False, 'reason': 'Response content too short'}

        # Check for error messages in response
        if any(error_word in response.content.lower() for error_word in ['error', 'failed', 'sorry, i', 'something went wrong']):
            return {'valid': False, 'reason': 'Response contains error message'}

        return {'valid': True, 'reason': 'Response validation passed'}

    def run_all_tests(self):
        """Run tests for all suggestions"""
        print(f"🚀 Starting chatbot suggestions test suite")
        print(f"Testing {len(self.suggestions)} conversation suggestions...")
        print("=" * 60)

        for i, suggestion in enumerate(self.suggestions, 1):
            print(f"\n[{i}/{len(self.suggestions)}]", end="")
            result = self.test_suggestion(suggestion)
            self.test_results.append(result)

            if result['success']:
                self.passed_tests += 1
            else:
                self.failed_tests += 1

        self._print_summary()
        return self.test_results

    def _print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("🎯 TEST SUMMARY")
        print("=" * 60)
        print(f"✅ Passed: {self.passed_tests}/{len(self.suggestions)}")
        print(f"❌ Failed: {self.failed_tests}/{len(self.suggestions)}")
        print(f"📊 Success Rate: {(self.passed_tests/len(self.suggestions)*100):.1f}%")

        if self.failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   • '{result['suggestion']}' - {result['error']}")

        print("\n📋 DETAILED RESULTS:")
        for i, result in enumerate(self.test_results, 1):
            status = "✅" if result['success'] else "❌"
            print(f"{i:2d}. {status} {result['suggestion']}")
            if result['success']:
                print(f"     Response type: {result['response_type']}")
            else:
                print(f"     Error: {result['error']}")

    def save_results(self, filename: Optional[str] = None):
        """Save test results to JSON file"""
        if filename is None:
            filename = f"chatbot_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        results_data = {
            'timestamp': datetime.now().isoformat(),
            'total_suggestions': len(self.suggestions),
            'passed_tests': self.passed_tests,
            'failed_tests': self.failed_tests,
            'success_rate': self.passed_tests/len(self.suggestions)*100,
            'suggestions_tested': self.suggestions,
            'detailed_results': self.test_results
        }

        try:
            with open(filename, 'w') as f:
                json.dump(results_data, f, indent=2, default=str)
            print(f"\n💾 Test results saved to: {filename}")
        except Exception as e:
            print(f"\n❌ Failed to save results: {e}")

    def test_individual_suggestion(self, suggestion: str):
        """Test a single specific suggestion"""
        print(f"🧪 Testing individual suggestion: '{suggestion}'")
        result = self.test_suggestion(suggestion)

        print("\n📊 Individual Test Result:")
        if result['success']:
            print(f"✅ SUCCESS")
            print(f"   Response Type: {result['response_type']}")
            print(f"   Response Content: {result['response_content']}")
            if result['metadata']:
                print(f"   Metadata: {result['metadata']}")
        else:
            print(f"❌ FAILED")
            print(f"   Error: {result['error']}")

        return result


def main():
    """Main function to run the tests"""
    print("🤖 Chatbot Suggestions Test Suite")
    print("Testing all conversation suggestions for functionality...")

    tester = ChatbotSuggestionTester()

    # Run all tests
    results = tester.run_all_tests()

    # Save results
    tester.save_results()

    # Return appropriate exit code
    if tester.failed_tests == 0:
        print("\n🎉 All tests passed! The chatbot suggestions are working correctly.")
        return 0
    else:
        print(f"\n⚠️  {tester.failed_tests} test(s) failed. Please review the results above.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
