#!/usr/bin/env python3
"""
SharePoint Deployment Test Script
Tests the new SharePoint deployment features for AdhocLog
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_sharepoint_detection():
    """Test SharePoint environment detection in data manager"""
    print("🧪 Testing SharePoint detection...")

    # Test 1: Local mode (no environment variables)
    os.environ.pop('ADHOCLOG_SHAREPOINT_MODE', None)
    os.environ.pop('ADHOCLOG_USER_DATA_DIR', None)

    from app.data_manager import DataManager
    from app.config import Config

    # Clear the config instance to reset it
    config = Config()
    dm_local = DataManager()

    print(f"✅ Local mode - SharePoint mode: {dm_local.sharepoint_mode}")
    print(f"✅ Local mode - Data file: {dm_local.data_file}")
    print(f"✅ Local mode - User data dir: {dm_local.user_data_dir}")

    # Test 2: SharePoint mode with environment variables
    with tempfile.TemporaryDirectory() as temp_dir:
        user_dir = os.path.join(temp_dir, 'user_testuser')
        os.makedirs(user_dir, exist_ok=True)

        os.environ['ADHOCLOG_SHAREPOINT_MODE'] = '1'
        os.environ['ADHOCLOG_USER_DATA_DIR'] = user_dir

        # Need to reload modules to pick up new environment
        if 'app.config' in sys.modules:
            del sys.modules['app.config']
        if 'app.data_manager' in sys.modules:
            del sys.modules['app.data_manager']

        from app.data_manager import DataManager
        from app.config import Config

        config_sp = Config()
        dm_sharepoint = DataManager()

        print(f"✅ SharePoint mode - SharePoint mode: {dm_sharepoint.sharepoint_mode}")
        print(f"✅ SharePoint mode - Data file: {dm_sharepoint.data_file}")
        print(f"✅ SharePoint mode - User data dir: {dm_sharepoint.user_data_dir}")

        # Test data operations
        test_task = {
            'title': 'Test SharePoint Task',
            'classification': 'Planning',
            'description': 'Testing SharePoint deployment',
            'est_time': 30
        }

        added_task = dm_sharepoint.add_task(test_task)
        print(f"✅ Task added with ID: {added_task['id']}")

        # Verify file was created in correct location
        expected_file = os.path.join(user_dir, 'tasks.json')
        if os.path.exists(expected_file):
            print(f"✅ Task file created in correct location: {expected_file}")
        else:
            print(f"❌ Task file not found at: {expected_file}")

        # Test task retrieval
        tasks = dm_sharepoint.get_all_tasks()
        if len(tasks) > 0:
            print(f"✅ Retrieved {len(tasks)} task(s)")
        else:
            print("❌ No tasks retrieved")

def test_migration():
    """Test data migration from legacy to new structure"""
    print("\n🧪 Testing data migration...")

    with tempfile.TemporaryDirectory() as temp_dir:
        # Create legacy data structure
        legacy_data_dir = temp_dir
        user_dir = os.path.join(temp_dir, 'user_testuser')
        os.makedirs(user_dir, exist_ok=True)

        # Create legacy file
        legacy_file = os.path.join(legacy_data_dir, 'tasks_testuser.json')
        legacy_data = [
            {
                'id': 1,
                'title': 'Legacy Task',
                'classification': 'Planning',
                'description': 'This is a legacy task',
                'est_time': 45,
                'team_member': 'testuser'
            }
        ]

        import json
        with open(legacy_file, 'w') as f:
            json.dump(legacy_data, f, indent=2)

        print(f"✅ Created legacy file: {legacy_file}")

        # Set up environment for SharePoint mode
        os.environ['ADHOCLOG_SHAREPOINT_MODE'] = '1'
        os.environ['ADHOCLOG_USER_DATA_DIR'] = user_dir

        # Mock the config DATA_DIR to point to our temp directory
        # Need to reload modules
        if 'app.config' in sys.modules:
            del sys.modules['app.config']
        if 'app.data_manager' in sys.modules:
            del sys.modules['app.data_manager']

        from app.data_manager import DataManager

        # Temporarily modify the data directory
        original_data_dir = None
        try:
            dm = DataManager()
            # Manually set the config DATA_DIR for testing
            dm.config.DATA_DIR = legacy_data_dir

            # Trigger migration manually
            dm._migrate_legacy_data()

            # Check if migration occurred
            new_file = os.path.join(user_dir, 'tasks.json')
            if os.path.exists(new_file):
                print(f"✅ Migration successful: {new_file}")

                # Verify data integrity
                with open(new_file, 'r') as f:
                    migrated_data = json.load(f)

                if len(migrated_data) == 1 and migrated_data[0]['title'] == 'Legacy Task':
                    print("✅ Data integrity verified")
                else:
                    print("❌ Data integrity check failed")

                # Check for backup file
                backup_files = [f for f in os.listdir(legacy_data_dir) if f.startswith('tasks_testuser.json.backup_')]
                if backup_files:
                    print(f"✅ Backup created: {backup_files[0]}")
                else:
                    print("❌ No backup file found")
            else:
                print(f"❌ Migration failed: {new_file} not found")

        except Exception as e:
            print(f"❌ Migration test failed: {e}")

def test_cache_isolation():
    """Test cache isolation functionality"""
    print("\n🧪 Testing cache isolation...")

    # Test environment variable setting
    cache_env_vars = [
        'PYTHONDONTWRITEBYTECODE',
        'PYTHONPYCACHEPREFIX'
    ]

    for var in cache_env_vars:
        if var in os.environ:
            print(f"✅ {var} = {os.environ[var]}")
        else:
            print(f"⚠️ {var} not set (may be set by launcher)")

def main():
    """Run all tests"""
    print("🚀 AdhocLog SharePoint Deployment Tests")
    print("=" * 50)

    try:
        test_sharepoint_detection()
        test_migration()
        test_cache_isolation()

        print("\n" + "=" * 50)
        print("✅ All tests completed!")
        print("💡 Note: Some environment variables are set by the launcher scripts")

    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

    finally:
        # Clean up environment variables
        os.environ.pop('ADHOCLOG_SHAREPOINT_MODE', None)
        os.environ.pop('ADHOCLOG_USER_DATA_DIR', None)

if __name__ == '__main__':
    main()
