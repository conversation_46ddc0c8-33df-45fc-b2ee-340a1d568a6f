#!/usr/bin/env python3
"""
Help Guide Debug Test Script
============================

This script specifically tests the help guide examples and dropdown options that the user reported
as not working properly. It provides detailed debugging information to identify the root cause
of the "I'm not sure what you're referring to" responses.

Usage:
    python test_help_guide_debug.py
"""

import sys
import os
from datetime import date, timedelta

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.data_manager import DataManager
from app.ai_engine import AITaskEngine
from app.analytics import AnalyticsEngine
from app.chatbot_engine import ChatbotEngine


def test_help_guide_examples():
    """Test specific help guide examples that are failing"""
    print("🔍 HELP GUIDE DEBUGGING TEST")
    print("=" * 60)
    print("Testing specific examples that trigger 'I'm not sure' responses...")

    try:
        # Initialize components
        data_manager = DataManager()
        ai_engine = AITaskEngine()
        analytics_engine = AnalyticsEngine()
        chatbot = ChatbotEngine(data_manager, ai_engine, analytics_engine)

        # Create sample data for testing
        today = date.today()
        yesterday = today - timedelta(days=1)

        sample_tasks = [
            {
                'title': 'Business Support Test Task',
                'description': 'Test task for classification filtering',
                'date': today.strftime('%Y-%m-%d'),
                'classification': 'Business Support Activities',
                'est_time': 30,
                'completed': False
            },
            {
                'title': 'Planning Task',
                'description': 'Test planning task',
                'date': today.strftime('%Y-%m-%d'),
                'classification': 'Planning',
                'est_time': 45,
                'completed': True
            },
            {
                'title': 'Yesterday Task',
                'description': 'Task from yesterday',
                'date': yesterday.strftime('%Y-%m-%d'),
                'classification': 'Execution',
                'est_time': 60,
                'completed': False
            }
        ]

        # Add sample tasks (ignore errors if they already exist)
        for task in sample_tasks:
            try:
                data_manager.add_task(task)
            except:
                pass

        # Test problematic queries
        problematic_queries = [
            # Help guide examples that are failing
            "Show me today's tasks",
            "Show me my tasks for today",
            "Show me all my Business Support Activities tasks",
            "What tasks did I have yesterday?",
            "Create a task for the quarterly review",
            "How productive have I been this week?",
            "What do I have scheduled for July 10?",
            "Show me my completed work",
            "How much time did I spend last month?",
            "Create 5 tasks for Q1 planning",

            # Dropdown options that should work
            "Create Task",
            "Show Schedule",
            "Get Suggestions",

            # Additional variations
            "Help me create tasks",
            "Show me my schedule",
            "I need task suggestions"
        ]

        total_tests = len(problematic_queries)
        passed_tests = 0
        failed_details = []

        print(f"\nTesting {total_tests} problematic queries...")
        print("-" * 60)

        for i, query in enumerate(problematic_queries, 1):
            print(f"\n{i:2d}/{total_tests}: Testing '{query}'")

            try:
                # Step 1: Test intent detection
                intent, extracted_info = chatbot._understand_intent(query)
                print(f"    Intent: '{intent}'")
                print(f"    Extracted: {extracted_info}")

                # Step 2: Test full response
                response = chatbot.process_message(query)
                response_content = response.content.lower()

                # Step 3: Check for failure indicators
                failure_phrases = [
                    "i'm not sure what you're referring to",
                    "could you be more specific",
                    "i don't understand",
                    "i'm not sure",
                    "i don't know what you mean"
                ]

                is_failure = any(phrase in response_content for phrase in failure_phrases)
                has_substantial_content = len(response.content) > 50
                has_helpful_content = any(word in response_content for word in [
                    'task', 'help', 'create', 'show', 'schedule', 'suggestions'
                ])

                # Step 4: Determine success
                if not is_failure and has_substantial_content and has_helpful_content:
                    print(f"    ✅ SUCCESS - Proper response ({len(response.content)} chars)")
                    passed_tests += 1
                else:
                    print(f"    ❌ FAILED - Problematic response")
                    print(f"    📝 Preview: {response.content[:100]}...")

                    # Detailed failure analysis
                    failure_info = {
                        'query': query,
                        'intent': intent,
                        'response_length': len(response.content),
                        'is_failure_response': is_failure,
                        'has_substantial_content': has_substantial_content,
                        'has_helpful_content': has_helpful_content,
                        'response_preview': response.content[:150]
                    }
                    failed_details.append(failure_info)

                    if is_failure:
                        print(f"    🚨 Contains failure phrase")
                    if not has_substantial_content:
                        print(f"    🚨 Response too short")
                    if not has_helpful_content:
                        print(f"    🚨 Not helpful enough")

            except Exception as e:
                print(f"    ❌ ERROR: {str(e)}")
                failed_details.append({
                    'query': query,
                    'error': str(e)
                })

        # Results summary
        print("\n" + "=" * 60)
        print("📊 RESULTS SUMMARY")
        print("=" * 60)

        success_rate = (passed_tests / total_tests) * 100
        print(f"Tests passed: {passed_tests}/{total_tests} ({success_rate:.1f}%)")

        if failed_details:
            print(f"\n🔍 FAILURE ANALYSIS ({len(failed_details)} failures):")
            print("-" * 60)

            for i, failure in enumerate(failed_details, 1):
                print(f"\nFailure #{i}: '{failure['query']}'")
                if 'intent' in failure:
                    print(f"  Intent: {failure['intent']}")
                    print(f"  Length: {failure['response_length']} chars")
                    print(f"  Has failure phrase: {failure['is_failure_response']}")
                    print(f"  Preview: {failure['response_preview']}")
                if 'error' in failure:
                    print(f"  Error: {failure['error']}")

        # Recommendations
        print(f"\n🛠️  DEBUGGING RECOMMENDATIONS:")
        print("-" * 60)

        intent_failures = sum(1 for f in failed_details if f.get('intent') == 'unknown')
        if intent_failures > 0:
            print(f"• {intent_failures} queries have 'unknown' intent")
            print("  → Check _understand_intent() method patterns")
            print("  → Review _determine_smart_intent() logic")

        failure_responses = sum(1 for f in failed_details if f.get('is_failure_response', False))
        if failure_responses > 0:
            print(f"• {failure_responses} queries trigger generic failure responses")
            print("  → Check _handle_unknown_request() method")
            print("  → Review fallback response logic")

        short_responses = sum(1 for f in failed_details if not f.get('has_substantial_content', True))
        if short_responses > 0:
            print(f"• {short_responses} queries generate short responses")
            print("  → Check response generation methods")
            print("  → Ensure proper content is being created")

        if passed_tests == total_tests:
            print("🎉 All help guide examples working!")
            return True
        elif passed_tests >= total_tests * 0.8:
            print("🎊 Most examples working well!")
            return True
        else:
            print("⚠️ Significant issues found - debugging needed!")
            return False

    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_intent_detection_patterns():
    """Test specific intent detection patterns in detail"""
    print("\n🎯 INTENT DETECTION PATTERN ANALYSIS")
    print("=" * 60)

    try:
        # Initialize chatbot
        data_manager = DataManager()
        ai_engine = AITaskEngine()
        analytics_engine = AnalyticsEngine()
        chatbot = ChatbotEngine(data_manager, ai_engine, analytics_engine)

        # Test specific patterns that should work
        pattern_tests = [
            {
                'query': 'Show me today\'s tasks',
                'expected_keywords': ['show', 'today', 'tasks'],
                'expected_intent_category': 'show_tasks'
            },
            {
                'query': 'Create Task',
                'expected_keywords': ['create', 'task'],
                'expected_intent_category': 'create_task'
            },
            {
                'query': 'Show Schedule',
                'expected_keywords': ['show', 'schedule'],
                'expected_intent_category': 'show_tasks'
            },
            {
                'query': 'Get Suggestions',
                'expected_keywords': ['get', 'suggestions'],
                'expected_intent_category': 'get_suggestions'
            },
            {
                'query': 'Show me all my Business Support Activities tasks',
                'expected_keywords': ['show', 'business support activities', 'tasks'],
                'expected_intent_category': 'show_tasks'
            }
        ]

        print(f"Testing {len(pattern_tests)} intent detection patterns...")

        for i, test in enumerate(pattern_tests, 1):
            print(f"\n{i}. Testing: '{test['query']}'")

            try:
                # Test step by step
                message_lower = test['query'].lower()

                # Check keyword detection
                found_keywords = []
                for keyword in test['expected_keywords']:
                    if keyword.lower() in message_lower:
                        found_keywords.append(keyword)

                print(f"   Keywords found: {found_keywords}")
                print(f"   Keywords expected: {test['expected_keywords']}")

                # Test action detection
                try:
                    action = chatbot._detect_task_action(message_lower)
                    print(f"   Detected action: {action}")
                except Exception as e:
                    print(f"   Action detection error: {e}")

                # Test full intent detection
                intent, extracted_info = chatbot._understand_intent(test['query'])
                print(f"   Final intent: '{intent}'")
                print(f"   Extracted info: {extracted_info}")

                # Check if intent makes sense
                intent_matches_category = test['expected_intent_category'] in intent.lower()
                if intent != 'unknown' and (intent_matches_category or len(found_keywords) >= 2):
                    print(f"   ✅ Intent detection working")
                else:
                    print(f"   ❌ Intent detection failed")
                    print(f"      Expected category: {test['expected_intent_category']}")
                    print(f"      Got: {intent}")

            except Exception as e:
                print(f"   ❌ Error: {e}")

        return True

    except Exception as e:
        print(f"❌ Pattern test error: {e}")
        return False


def main():
    """Run help guide debugging tests"""
    print("🚀 HELP GUIDE DEBUG TEST SUITE")
    print("=" * 80)
    print("This test specifically debugs the help guide and dropdown issues")
    print("that the user reported as not working properly.")
    print("=" * 80)

    # Run tests
    test1_passed = test_help_guide_examples()
    test2_passed = test_intent_detection_patterns()

    # Final summary
    print("\n" + "=" * 80)
    print("🏆 FINAL DEBUG SUMMARY")
    print("=" * 80)

    tests_passed = sum([test1_passed, test2_passed])
    total_tests = 2

    print(f"Help Guide Examples: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"Intent Pattern Analysis: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    print(f"\nOverall: {tests_passed}/{total_tests} tests passed")

    if tests_passed == total_tests:
        print("🎉 Help guide functionality is working correctly!")
    elif tests_passed >= 1:
        print("🔧 Some issues found - check the detailed output above for fixes")
    else:
        print("🚨 Significant problems detected - debugging required!")

    print("\n💡 Next Steps:")
    if not test1_passed:
        print("  • Fix intent detection for help guide examples")
        print("  • Improve dropdown option recognition")
        print("  • Enhance fallback response handling")
    if not test2_passed:
        print("  • Review pattern matching algorithms")
        print("  • Update keyword detection logic")

    return tests_passed == total_tests


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
