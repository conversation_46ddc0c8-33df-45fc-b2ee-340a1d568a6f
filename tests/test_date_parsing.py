#!/usr/bin/env python3
"""
Test Enhanced Date Parsing for Chatbot
Tests the improved date recognition and parsing capabilities
"""

import sys
import os
from datetime import datetime, date, timedelta

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_date_parsing():
    """Test the enhanced date parsing functionality"""

    print("🗓️ Testing Enhanced Date Parsing Functionality")
    print("=" * 60)

    try:
        # Import required modules
        from app.chatbot_engine import ChatbotEngine
        from app.data_manager import DataManager
        from app.ai_engine import AITaskEngine
        from app.analytics import AnalyticsEngine

        print("✅ Successfully imported all modules")

        # Initialize components
        data_manager = DataManager()
        ai_engine = AITaskEngine()
        analytics_engine = AnalyticsEngine()
        chatbot = ChatbotEngine(data_manager, ai_engine, analytics_engine)

        print("✅ Successfully initialized chatbot engine")

        # Create sample tasks for different dates
        today = date.today()
        sample_dates = [
            today.strftime('%Y-%m-%d'),
            (today - timedelta(days=1)).strftime('%Y-%m-%d'),
            '2025-07-10',  # Specific date for testing
            '2025-07-15',
            '2025-08-01'
        ]

        sample_tasks = [
            {
                'title': f'Task for {sample_dates[0]}',
                'description': 'Today task',
                'date': sample_dates[0],
                'classification': 'Planning',
                'est_time': 30,
                'completed': False
            },
            {
                'title': f'Task for {sample_dates[1]}',
                'description': 'Yesterday task',
                'date': sample_dates[1],
                'classification': 'Execution',
                'est_time': 45,
                'completed': True
            },
            {
                'title': 'Review quarterly reports',
                'description': 'July 10 task',
                'date': '2025-07-10',
                'classification': 'Business Support Activities',
                'est_time': 60,
                'completed': False
            },
            {
                'title': 'Team meeting preparation',
                'description': 'July 15 task',
                'date': '2025-07-15',
                'classification': 'Planning',
                'est_time': 30,
                'completed': False
            }
        ]

        # Add sample tasks
        for task in sample_tasks:
            try:
                data_manager.add_task(task)
            except Exception as e:
                print(f"Note: Task may already exist - {e}")

        print("✅ Sample tasks with various dates prepared")
        print()

        # Test various date queries
        date_test_scenarios = [
            {
                'name': 'Month Name + Day',
                'query': 'show me the task on july 10',
                'expected_keywords': ['july', 'task', 'review', 'quarterly']
            },
            {
                'name': 'Month Name + Day (Capitalized)',
                'query': 'Show me tasks for July 15',
                'expected_keywords': ['july', 'task', 'team', 'meeting']
            },
            {
                'name': 'Numeric Date (M/D/Y)',
                'query': 'tasks for 7/10/2025',
                'expected_keywords': ['task', 'review', 'quarterly']
            },
            {
                'name': 'Numeric Date (M/D)',
                'query': 'show tasks on 7/15',
                'expected_keywords': ['task', 'team', 'meeting']
            },
            {
                'name': 'ISO Date Format',
                'query': 'list tasks for 2025-07-10',
                'expected_keywords': ['task', 'review', 'quarterly']
            },
            {
                'name': 'Today Reference',
                'query': 'what tasks do I have today?',
                'expected_keywords': ['today', 'task']
            },
            {
                'name': 'Yesterday Reference',
                'query': 'show yesterday tasks',
                'expected_keywords': ['yesterday', 'task']
            },
            {
                'name': 'Complex Date Query',
                'query': 'show me the tasks I have on July 10th',
                'expected_keywords': ['july', 'task']
            }
        ]

        # Run date parsing tests
        passed_tests = 0
        total_tests = len(date_test_scenarios)

        for i, scenario in enumerate(date_test_scenarios, 1):
            print(f"🧪 Test {i}/{total_tests}: {scenario['name']}")
            print(f"   Query: '{scenario['query']}'")

            try:
                # Process the message
                response = chatbot.process_message(scenario['query'])

                # Check if response exists and has content
                if response and hasattr(response, 'content') and response.content:
                    content_lower = response.content.lower()

                    # Check if it's not an error response
                    is_error = any(error_word in content_lower for error_word in [
                        "couldn't find tasks matching",
                        "available types:",
                        "couldn't understand",
                        "try a different"
                    ])

                    if not is_error:
                        # Check for expected keywords
                        keywords_found = []
                        keywords_missing = []

                        for keyword in scenario['expected_keywords']:
                            if keyword.lower() in content_lower:
                                keywords_found.append(keyword)
                            else:
                                keywords_missing.append(keyword)

                        # Determine if test passed
                        keyword_ratio = len(keywords_found) / len(scenario['expected_keywords']) if scenario['expected_keywords'] else 1
                        test_passed = keyword_ratio >= 0.5 and len(response.content) > 20

                        if test_passed:
                            print(f"   ✅ PASSED - Date parsing successful!")
                            print(f"   📝 Keywords found: {', '.join(keywords_found)}")
                            if hasattr(response, 'message_type'):
                                print(f"   🏷️  Response type: {response.message_type}")
                            passed_tests += 1
                        else:
                            print(f"   ⚠️  PARTIAL - Date parsed but missing some keywords")
                            print(f"   📝 Missing: {', '.join(keywords_missing)}")
                            print(f"   📝 Response: {response.content[:100]}...")
                            passed_tests += 0.5
                    else:
                        print(f"   ❌ FAILED - Date not recognized correctly")
                        print(f"   📝 Error response: {response.content[:100]}...")
                else:
                    print(f"   ❌ FAILED - No valid response received")

            except Exception as e:
                print(f"   ❌ ERROR - Exception occurred: {str(e)}")

            print()

        # Test Summary
        print("=" * 60)
        print(f"🎯 DATE PARSING TEST SUMMARY")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {passed_tests}")
        print(f"   Failed: {total_tests - passed_tests}")
        print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")

        if passed_tests >= total_tests * 0.8:
            print("   🎉 EXCELLENT! Date parsing is working well!")
        elif passed_tests >= total_tests * 0.6:
            print("   👍 GOOD! Most date formats are recognized!")
        else:
            print("   ⚠️  Date parsing needs improvement")

        print()

        # Test the specific date parsing method directly
        print("🔍 Testing Direct Date Parsing Method:")
        test_messages = [
            "july 10",
            "July 15",
            "7/10/2025",
            "7/15",
            "2025-07-10",
            "10 July",
            "august 1st"
        ]

        today = date.today()
        for msg in test_messages:
            try:
                parsed_date = chatbot._parse_specific_date(msg.lower(), today)
                if parsed_date:
                    print(f"   ✅ '{msg}' → {parsed_date.strftime('%Y-%m-%d (%B %d, %Y)')}")
                else:
                    print(f"   ❌ '{msg}' → Could not parse")
            except Exception as e:
                print(f"   ❌ '{msg}' → Error: {e}")

        print()
        print("🚀 Enhanced Date Parsing Testing Complete!")
        print("   The chatbot now intelligently recognizes:")
        print("   • Month names (July 10, Jul 15)")
        print("   • Numeric dates (7/10/2025, 7/15)")
        print("   • ISO format (2025-07-10)")
        print("   • Relative dates (today, yesterday)")
        print("   • Various formats and cases")

        return passed_tests >= total_tests * 0.7

    except ImportError as e:
        print(f"❌ Import Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        return False

if __name__ == "__main__":
    success = test_date_parsing()
    sys.exit(0 if success else 1)
