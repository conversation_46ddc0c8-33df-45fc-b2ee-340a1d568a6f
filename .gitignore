# AdhocLog - Git Ignore File

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# User-specific virtual environments (SharePoint multi-user support)
# These should be excluded from SharePoint sync to prevent cross-platform conflicts
venvs/
venvs/user_*/
**/*venv*/
**/*ENV*/

# Flask
instance/
.webassets-cache

# Environment Variables
.env
.env.local
.env.production
.env.staging

# Data Files (User-specific data should be synced in SharePoint but excluded from git)
# Legacy data structure
data/tasks_*.json
data/archived_tasks_*.json
data/templates_*.json
data/user_events.json
data/user_events_*.json
data/analytics.json
data/analytics_*.json
data/enhanced_keywords.json

# New SharePoint user directory structure - INCLUDE in SharePoint sync
!data/user_*/
!data/user_*/tasks.json
!data/user_*/archived_tasks.json
!data/user_*/templates.json
!data/user_*/analytics_*.json
!data/user_*/user_events_*.json

# Exclude temporary and cache files from SharePoint sync
data/user_*/*.tmp
data/user_*/*.temp
data/user_*/*.bak
data/user_*/*.backup
data/user_*/cache/

# Keep important directory structure
!data/.gitkeep
!data/backup/.gitkeep

# SharePoint-specific excludes
# Temporary cache directories created by launcher
**/adhoclog_cache_*/
**/__pycache__/
*.pyc
*.pyo

# SharePoint sync files
.lock-*
~$*
*.tmp
desktop.ini
Thumbs.db

# Backup Files
data/backup/*
!data/backup/.gitkeep

# Logs
*.log
logs/

# IDE / Editor Files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# OS Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Temporary Files
*.tmp
*.temp
*.bak
*.backup

# Coverage Reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# GitHub configuration
.github/

# Development prompts and AI context
.prompts/

# Node.js (if using any npm packages)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# SQLite databases (if using SQLite)
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup
*.old

# Backup folder (created by GUI backup functionality)
backups/

# Archive files
*.zip
*.tar.gz
*.rar

# Application specific excludes
# Exclude user data but keep sample data structure
!data/sample_data.json

# Keep important empty directories
!static/css/.gitkeep
!static/js/.gitkeep
!static/images/.gitkeep
!tests/.gitkeep
!docs/.gitkeep
