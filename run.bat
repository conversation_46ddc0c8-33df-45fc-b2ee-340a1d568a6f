@echo off
setlocal enabledelayedexpansion
echo ========================================
echo   [LAUNCH] AdhocLog
echo   Windows Launcher
echo ========================================

REM Change to the script's directory
cd /d "%~dp0"

echo.
echo [INFO] Checking deployment environment...

REM Detect SharePoint/OneDrive environment
set SHAREPOINT_MODE=false
set SHAREPOINT_TYPE=""

echo %CD% | findstr /i "OneDrive" >nul
if !errorlevel! equ 0 (
    set SHAREPOINT_MODE=true
    echo %CD% | findstr /i "OneDrive - " >nul
    if !errorlevel! equ 0 (
        set SHAREPOINT_TYPE=OneDrive Business
    ) else (
        set SHAREPOINT_TYPE=OneDrive Personal
    )
    echo 🌐 SharePoint environment detected: !SHAREPOINT_TYPE!
    echo 📁 Path: %CD%
    echo 🚀 Configuring for SharePoint deployment...

    REM Setup cache isolation for SharePoint
    set PYTHONDONTWRITEBYTECODE=1
    echo 🔒 Cache isolation enabled (PYTHONDONTWRITEBYTECODE=1)

    REM Create user-specific data directory
    for /f "tokens=*" %%i in ('whoami') do set USERNAME_CLEAN=%%i
    set USERNAME_CLEAN=!USERNAME_CLEAN:\=_!
    set USER_DATA_DIR=data\user_!USERNAME_CLEAN!

    if not exist "!USER_DATA_DIR!" (
        mkdir "!USER_DATA_DIR!" 2>nul
        if exist "!USER_DATA_DIR!" (
            echo ✅ User data directory created: !USER_DATA_DIR!
            set ADHOCLOG_USER_DATA_DIR=!USER_DATA_DIR!
            set ADHOCLOG_SHAREPOINT_MODE=1
        ) else (
            echo ⚠️ Could not create user data directory: !USER_DATA_DIR!
            echo 💡 Falling back to legacy data structure
        )
    ) else (
        echo ✅ User data directory exists: !USER_DATA_DIR!
        set ADHOCLOG_USER_DATA_DIR=!USER_DATA_DIR!
        set ADHOCLOG_SHAREPOINT_MODE=1
    )

    echo ✅ SharePoint configuration complete
) else (
    echo 💻 Local development environment detected
    echo ✅ Using standard configuration
)

echo.

REM Check for Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo.
    echo 📥 Please install Python 3.7+ from https://python.org/downloads
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo ✅ Python found:
python --version

REM Setup local virtual environment (Documents folder for performance)
if "%SHAREPOINT_MODE%"=="true" (
    echo.
    echo 🔧 Setting up local virtual environment for SharePoint deployment...
    echo 💾 Using local Documents folder (no OneDrive sync for performance)

    REM Get platform info
    for /f "tokens=2 delims==" %%i in ('wmic os get Caption /value') do set OS_NAME=%%i
    for /f "tokens=2 delims==" %%i in ('wmic computersystem get SystemType /value') do set ARCH=%%i

    REM Clean up architecture name
    set ARCH=!ARCH: =!
    if "!ARCH!"=="x64-based PC" set ARCH=AMD64
    if "!ARCH!"=="ARM64-based PC" set ARCH=ARM64

    REM Get username
    for /f "tokens=*" %%i in ('whoami') do set CLEAN_USERNAME=%%i
    set CLEAN_USERNAME=!CLEAN_USERNAME:\=_!

    REM Use home directory for venv storage
    set VENV_BASE_DIR=%USERPROFILE%\.venvs
    set VENV_DIR=%VENV_BASE_DIR%\adhoc-log-app

    echo [INFO] User: !CLEAN_USERNAME!
    echo [INFO] Virtual environment: !VENV_DIR!
    echo [INFO] Platform: Windows !ARCH!
    echo [INFO] App directory: %CD%

    REM Create .venvs directory structure in home directory
    if not exist "%VENV_BASE_DIR%" (
        mkdir "%VENV_BASE_DIR%" 2>nul
        if exist "%VENV_BASE_DIR%" (
            echo [SUCCESS] Created venv directory: %VENV_BASE_DIR%
        ) else (
            echo [ERROR] Cannot create venv directory: %VENV_BASE_DIR%
            echo [INFO] Falling back to system Python
            goto :check_gui
        )
    ) else (
        echo [SUCCESS] Home venv directory ready: %VENV_BASE_DIR%
    )

    REM Check if virtual environment exists and is valid
    if exist "!VENV_DIR!" (
        echo 🔍 Checking existing virtual environment...
        set VENV_PYTHON=!VENV_DIR!\Scripts\python.exe

        if exist "!VENV_PYTHON!" (
            "!VENV_PYTHON!" --version >nul 2>&1
            if !errorlevel! equ 0 (
                echo ✅ Existing virtual environment is compatible

                REM Check if Flask is installed
                "!VENV_PYTHON!" -c "import flask; print('Flask available')" >nul 2>&1
                if !errorlevel! equ 0 (
                    echo ✅ Required packages already installed
                    set PYTHON_CMD=!VENV_PYTHON!
                    goto :venv_ready
                ) else (
                    echo 📦 Need to install/update packages
                    call :install_requirements "!VENV_DIR!"
                    set PYTHON_CMD=!VENV_PYTHON!
                    goto :venv_ready
                )
            ) else (
                echo ⚠️ Virtual environment exists but is broken
                echo 🔧 Removing broken virtual environment...
                rmdir /s /q "!VENV_DIR!" 2>nul
            )
        )
    )

    REM Create new virtual environment
    echo 📦 Creating new virtual environment...
    python -m venv "!VENV_DIR!" 2>nul
    if !errorlevel! equ 0 (
        echo ✅ Virtual environment created successfully
        set VENV_PYTHON=!VENV_DIR!\Scripts\python.exe

        if exist "!VENV_PYTHON!" (
            "!VENV_PYTHON!" --version >nul 2>&1
            if !errorlevel! equ 0 (
                echo ✅ Virtual environment Python is functional
                call :install_requirements "!VENV_DIR!"
                set PYTHON_CMD=!VENV_PYTHON!
                goto :venv_ready
            ) else (
                echo ❌ Virtual environment Python is not functional
                echo 💡 Falling back to system Python
                rmdir /s /q "!VENV_DIR!" 2>nul
            )
        ) else (
            echo ❌ Virtual environment Python executable not found
            echo 💡 Falling back to system Python
            rmdir /s /q "!VENV_DIR!" 2>nul
        )
    ) else (
        echo ❌ Failed to create virtual environment
        echo 💡 Falling back to system Python
    )

    :venv_ready
    if defined PYTHON_CMD (
        echo ✅ Virtual environment ready: !PYTHON_CMD!
    ) else (
        echo ⚠️ Virtual environment setup failed, using system Python
        set PYTHON_CMD=python
    )
) else (
    set PYTHON_CMD=python
)

:check_gui
echo.

REM Check GUI availability
echo 🔍 Checking GUI availability...
%PYTHON_CMD% -c "import tkinter; print('tkinter available')" >nul 2>&1
if !errorlevel! equ 0 (
    echo ✅ GUI is available!
    set GUI_AVAILABLE=true
) else (
    echo ❌ GUI is not available (tkinter not found)
    set GUI_AVAILABLE=false
)

:start
echo.
echo Choose how to launch the application:
echo.
if "%GUI_AVAILABLE%"=="true" (
    echo 1. 🖥️  GUI Launcher (Recommended) - Easy graphical interface
    echo 2. 🚀 Quick Start - Start app immediately
    echo 3. ❓ Help
    echo.
    set /p choice="Enter your choice (1-3): "
) else (
    echo 🖥️ GUI Launcher not available (tkinter not found)
    echo Available options:
    echo 1. 🚀 Quick Start - Start app immediately
    echo 2. ❓ Help
    echo.
    set /p choice="Enter your choice (1-2): "
    if "!choice!"=="1" set choice=2
    if "!choice!"=="2" set choice=3
)

if "%choice%"=="1" (
    if "%GUI_AVAILABLE%"=="true" (
        echo 🖥️ Starting GUI Launcher...
        %PYTHON_CMD% gui_launcher.py
    ) else (
        echo ❌ GUI not available on this system
        goto start
    )
) else if "%choice%"=="2" (
    echo 🚀 Quick Start - Starting application...
    if "%SHAREPOINT_MODE%"=="true" (
        echo 🌐 SharePoint mode detected - using configured Python environment
        if defined PYTHON_CMD if not "!PYTHON_CMD!"=="python" (
            echo 📦 Using virtual environment Python...
            %PYTHON_CMD% -c "import flask" >nul 2>&1
            if !errorlevel! equ 0 (
                echo ✅ Virtual environment is ready
                %PYTHON_CMD% run.py
            ) else (
                echo ⚠️ Virtual environment missing dependencies, trying anyway...
                %PYTHON_CMD% run.py
            )
        ) else (
            echo ⚠️ Virtual environment not available, using system Python...
            python -c "import flask" >nul 2>&1
            if !errorlevel! equ 0 (
                echo ✅ System Python has required packages
                python run.py
            ) else (
                echo ❌ Flask not available in system Python
                echo.
                echo 💡 Quick fixes:
                echo    1. Install manually: python -m pip install --user flask
                echo    2. Or try with all requirements: python -m pip install --user -r requirements.txt
                echo.
                set /p install_now="Would you like to try automatic package installation now? (y/n): "
                if /i "!install_now!"=="y" (
                    echo � Installing packages automatically...
                    python -m pip install --user -r requirements.txt >nul 2>&1
                    if !errorlevel! equ 0 (
                        echo ✅ Packages installed successfully
                        echo 🚀 Starting application...
                        python run.py
                    ) else (
                        python -m pip install --user flask >nul 2>&1
                        if !errorlevel! equ 0 (
                            echo ✅ Flask installed (minimal setup)
                            echo 🚀 Starting application...
                            python run.py
                        ) else (
                            echo ❌ Automatic installation failed
                            set /p start_anyway="Attempt to start anyway? (y/n): "
                            if /i "!start_anyway!"=="y" (
                                python run.py
                            ) else (
                                echo ❌ Application startup cancelled
                                goto start
                            )
                        )
                    )
                ) else (
                    set /p start_anyway="Attempt to start anyway? (y/n): "
                    if /i "!start_anyway!"=="y" (
                        python run.py
                    ) else (
                        echo ❌ Application startup cancelled
                        goto start
                    )
                )
            )
        )
    ) else (
        REM Local development environment
        %PYTHON_CMD% run.py
    )
) else if "%choice%"=="3" (
    echo.
    echo ❓ Help & Information:
    echo.
    echo 📋 What this application does:
    echo • Track daily tasks and time spent
    echo • Organize tasks by classification
    echo • Export data to CSV for reporting
    echo • Archive completed tasks
    echo.
    echo 🖥️ GUI Launcher Features:
    echo • Easy point-and-click interface
    echo • Automatic setup and configuration
    echo • Built-in diagnostics and repair tools
    echo • Real-time status and logging
    echo.
    echo 🌐 SharePoint Features:
    echo • User-specific virtual environments
    echo • Automatic data isolation
    echo • Cache management for shared drives
    echo • Cross-platform compatibility
    echo.
    echo 📁 Files:
    echo • run.bat - This Windows launcher
    echo • gui_launcher.py - Graphical interface
    echo • launch_app.sh - Unix/Linux launcher
    echo • data\ - Your task data storage
    if "%SHAREPOINT_MODE%"=="true" (
        echo • venvs\ - User-specific virtual environments
        echo • !USER_DATA_DIR!\ - Your isolated data directory
    )
    echo.
    echo 🆘 Troubleshooting:
    echo • If GUI doesn't work, try option 2
    echo • Check that Python 3.7+ is installed
    echo • Make sure you have internet connection for setup
    if "%SHAREPOINT_MODE%"=="true" (
        echo • SharePoint virtual environment: !VENV_DIR!
        echo • Data isolation: !USER_DATA_DIR!
    )
    echo.
    pause
    goto start
) else (
    echo ❌ Invalid option. Please try again.
    echo.
    goto start
)

goto :eof

REM Subroutine to install requirements in virtual environment
:install_requirements
set VENV_DIR=%~1
set VENV_PYTHON=%VENV_DIR%\Scripts\python.exe

echo 📦 Installing requirements in virtual environment...

REM Verify virtual environment is working
"%VENV_PYTHON%" --version >nul 2>&1
if !errorlevel! neq 0 (
    echo ❌ Virtual environment Python is not working
    goto :eof
)

REM Upgrade pip first
echo 🔧 Upgrading pip...
"%VENV_PYTHON%" -m pip install --upgrade pip >nul 2>&1
if !errorlevel! equ 0 (
    echo ✅ pip upgraded successfully
) else (
    "%VENV_PYTHON%" -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org --upgrade pip >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ pip upgraded with trusted hosts
    ) else (
        echo ⚠️ pip upgrade failed, continuing with existing version
    )
)

REM Check if requirements.txt exists
if not exist "requirements.txt" (
    echo ⚠️ requirements.txt not found
    goto :eof
)

REM Install requirements with multiple fallback strategies
echo 📋 Installing packages from requirements.txt...

REM Strategy 1: Standard installation
"%VENV_PYTHON%" -m pip install -r requirements.txt >nul 2>&1
if !errorlevel! equ 0 (
    echo ✅ Requirements installed successfully
    goto :eof
)

REM Strategy 2: With trusted hosts
echo ⚠️ Standard installation failed, trying with trusted hosts...
"%VENV_PYTHON%" -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org -r requirements.txt >nul 2>&1
if !errorlevel! equ 0 (
    echo ✅ Requirements installed with trusted hosts
    goto :eof
)

REM Strategy 3: With explicit index and trusted hosts
echo ⚠️ Trusted hosts failed, trying with explicit index...
"%VENV_PYTHON%" -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org --index-url https://pypi.org/simple/ -r requirements.txt >nul 2>&1
if !errorlevel! equ 0 (
    echo ✅ Requirements installed with explicit index
    goto :eof
)

echo ⚠️ Package installation failed
echo 💡 The application may still work with partial dependencies
goto :eof
