import json
import os
import getpass
from datetime import datetime
from typing import List, Dict, Optional
import sys
# Add parent directory to path to import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from app.config import Config

class DataManager:
    def __init__(self):
        self.config = Config()
        self.username = self._get_current_user()
        self.data_file = os.path.join(self.config.DATA_DIR, f'tasks_{self.username}.json')
        self.archive_file = os.path.join(self.config.DATA_DIR, f'archived_tasks_{self.username}.json')
        self._ensure_data_dir()

    def _get_current_user(self) -> str:
        """Auto-detect current system user"""
        try:
            return getpass.getuser()
        except Exception:
            return 'unknown_user'

    def _ensure_data_dir(self):
        """Ensure data directory exists"""
        if not os.path.exists(self.config.DATA_DIR):
            os.makedirs(self.config.DATA_DIR)

    def _load_tasks(self) -> List[Dict]:
        """Load tasks from JSON file"""
        if not os.path.exists(self.data_file):
            return []

        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return []

    def _save_tasks(self, tasks: List[Dict]):
        """Save tasks to JSON file"""
        with open(self.data_file, 'w', encoding='utf-8') as f:
            json.dump(tasks, f, indent=2, ensure_ascii=False)

    def _load_archived_tasks(self) -> List[Dict]:
        """Load archived tasks from JSON file"""
        if not os.path.exists(self.archive_file):
            return []

        try:
            with open(self.archive_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return []

    def _save_archived_tasks(self, archived_tasks: List[Dict]):
        """Save archived tasks to JSON file"""
        with open(self.archive_file, 'w', encoding='utf-8') as f:
            json.dump(archived_tasks, f, indent=2, ensure_ascii=False)

    def _get_next_id(self, tasks: List[Dict]) -> int:
        """Get next available ID"""
        if not tasks:
            return 1
        return max(task.get('id', 0) for task in tasks) + 1

    def get_all_tasks(self) -> List[Dict]:
        """Get all tasks"""
        return self._load_tasks()

    def get_task_by_id(self, task_id: int) -> Optional[Dict]:
        """Get a specific task by ID"""
        tasks = self._load_tasks()
        for task in tasks:
            if task.get('id') == task_id:
                return task
        return None

    def add_task(self, task_data: Dict) -> Dict:
        """Add a new task"""
        tasks = self._load_tasks()

        # Set auto-generated fields
        task_data['id'] = self._get_next_id(tasks)
        task_data['team_member'] = self.username

        # Set default date if not provided
        if not task_data.get('date'):
            task_data['date'] = datetime.now().strftime('%Y-%m-%d')

        # Auto-map category from classification
        classification = task_data.get('classification', '')
        task_data['category'] = self.config.CLASSIFICATION_MAPPING.get(classification, 'Other')

        tasks.append(task_data)
        self._save_tasks(tasks)
        return task_data

    def update_task(self, task_id: int, task_data: Dict) -> Optional[Dict]:
        """Update an existing task"""
        tasks = self._load_tasks()

        for i, task in enumerate(tasks):
            if task.get('id') == task_id:
                # Preserve ID and team_member
                task_data['id'] = task_id
                task_data['team_member'] = task.get('team_member', self.username)

                # Auto-map category from classification
                classification = task_data.get('classification', '')
                task_data['category'] = self.config.CLASSIFICATION_MAPPING.get(classification, 'Other')

                tasks[i] = task_data
                self._save_tasks(tasks)
                return task_data

        return None

    def delete_task(self, task_id: int) -> bool:
        """Archive a task (soft delete)"""
        tasks = self._load_tasks()
        archived_tasks = self._load_archived_tasks()

        # Find the task to archive
        task_to_archive = None
        for task in tasks:
            if task.get('id') == task_id:
                task_to_archive = task
                break

        if task_to_archive:
            # Add timestamp when archived
            task_to_archive['archived_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # Remove from active tasks
            tasks = [task for task in tasks if task.get('id') != task_id]

            # Add to archived tasks
            archived_tasks.append(task_to_archive)

            # Save both files
            self._save_tasks(tasks)
            self._save_archived_tasks(archived_tasks)
            return True

        return False

    def get_archived_tasks(self) -> List[Dict]:
        """Get all archived tasks"""
        archived_tasks = self._load_archived_tasks()
        # Sort by archived date (newest first)
        return sorted(archived_tasks, key=lambda x: x.get('archived_date', ''), reverse=True)

    def restore_task(self, task_id: int) -> bool:
        """Restore a task from archive"""
        tasks = self._load_tasks()
        archived_tasks = self._load_archived_tasks()

        # Find the task to restore
        task_to_restore = None
        for task in archived_tasks:
            if task.get('id') == task_id:
                task_to_restore = task
                break

        if task_to_restore:
            # Remove archived_date field
            if 'archived_date' in task_to_restore:
                del task_to_restore['archived_date']

            # Remove from archived tasks
            archived_tasks = [task for task in archived_tasks if task.get('id') != task_id]

            # Add back to active tasks
            tasks.append(task_to_restore)

            # Save both files
            self._save_tasks(tasks)
            self._save_archived_tasks(archived_tasks)
            return True

        return False

    def permanently_delete_task(self, task_id: int) -> bool:
        """Permanently delete a task from archive"""
        archived_tasks = self._load_archived_tasks()
        original_length = len(archived_tasks)

        archived_tasks = [task for task in archived_tasks if task.get('id') != task_id]

        if len(archived_tasks) < original_length:
            self._save_archived_tasks(archived_tasks)
            return True
        return False

    def filter_tasks(self, filters: Dict, page: int = 1, per_page: int = 10) -> Dict:
        """Filter tasks based on criteria with pagination"""
        tasks = self._load_tasks()
        filtered_tasks = tasks

        # Filter by date range
        if filters.get('start_date'):
            filtered_tasks = [t for t in filtered_tasks if t.get('date', '') >= filters['start_date']]

        if filters.get('end_date'):
            filtered_tasks = [t for t in filtered_tasks if t.get('date', '') <= filters['end_date']]

        # Filter by classification
        if filters.get('classification'):
            filtered_tasks = [t for t in filtered_tasks if t.get('classification') == filters['classification']]

        # Filter by search term (title or description)
        if filters.get('search'):
            search_term = filters['search'].lower()
            filtered_tasks = [
                t for t in filtered_tasks
                if search_term in t.get('title', '').lower() or search_term in t.get('description', '').lower()
            ]

        # Sort by date (newest first), then by ID (newest first)
        sorted_tasks = sorted(filtered_tasks, key=lambda x: (x.get('date', ''), x.get('id', 0)), reverse=True)

        # Calculate pagination
        total_tasks = len(sorted_tasks)
        total_pages = (total_tasks + per_page - 1) // per_page  # Ceiling division
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page

        paginated_tasks = sorted_tasks[start_idx:end_idx]

        return {
            'tasks': paginated_tasks,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_tasks,
                'total_pages': total_pages,
                'has_prev': page > 1,
                'has_next': page < total_pages,
                'prev_page': page - 1 if page > 1 else None,
                'next_page': page + 1 if page < total_pages else None
            }
        }

    def get_archived_tasks_paginated(self, page: int = 1, per_page: int = 10) -> Dict:
        """Get archived tasks with pagination"""
        archived_tasks = self._load_archived_tasks()
        # Sort by archived date (newest first)
        sorted_tasks = sorted(archived_tasks, key=lambda x: x.get('archived_date', ''), reverse=True)

        # Calculate pagination
        total_tasks = len(sorted_tasks)
        total_pages = (total_tasks + per_page - 1) // per_page  # Ceiling division
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page

        paginated_tasks = sorted_tasks[start_idx:end_idx]

        return {
            'tasks': paginated_tasks,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_tasks,
                'total_pages': total_pages,
                'has_prev': page > 1,
                'has_next': page < total_pages,
                'prev_page': page - 1 if page > 1 else None,
                'next_page': page + 1 if page < total_pages else None
            }
        }
