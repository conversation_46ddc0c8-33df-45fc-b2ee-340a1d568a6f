#!/usr/bin/env python3
"""
AdhocLog - Automated Setup and Repair Tool
Comprehensive setup and repair utility that works across all platforms
"""

import sys
import os
import platform
import subprocess
import shutil
from pathlib import Path
import json
import time

class SetupRepairTool:
    def __init__(self):
        self.platform = platform.system()
        self.errors = []
        self.warnings = []
        self.successes = []
        
    def log(self, message, level="INFO"):
        """Log a message with timestamp"""
        timestamp = time.strftime("%H:%M:%S")
        prefix = {
            "INFO": "ℹ️",
            "SUCCESS": "✅", 
            "WARNING": "⚠️",
            "ERROR": "❌"
        }.get(level, "ℹ️")
        
        full_message = f"[{timestamp}] {prefix} {message}"
        print(full_message)
        
        if level == "ERROR":
            self.errors.append(message)
        elif level == "WARNING":
            self.warnings.append(message)
        elif level == "SUCCESS":
            self.successes.append(message)
    
    def check_python(self):
        """Check Python installation"""
        self.log("Checking Python installation...")
        
        version = sys.version_info
        if version < (3, 7):
            self.log(f"Python {version.major}.{version.minor} is too old. Need 3.7+", "ERROR")
            return False
        
        self.log(f"Python {version.major}.{version.minor}.{version.micro} - Compatible", "SUCCESS")
        self.log(f"Python executable: {sys.executable}")
        return True
    
    def check_tkinter(self):
        """Check tkinter availability"""
        self.log("Checking tkinter availability...")
        
        try:
            import tkinter as tk
            # Try to create a test window
            root = tk.Tk()
            root.withdraw()
            root.destroy()
            self.log("tkinter is available and working", "SUCCESS")
            return True
        except ImportError:
            self.log("tkinter not installed", "ERROR")
            return False
        except Exception as e:
            self.log(f"tkinter installed but not working: {e}", "WARNING")
            return False
    
    def validate_virtual_environment(self):
        """Validate virtual environment"""
        self.log("Validating virtual environment...")
        
        venv_path = Path("venv")
        if not venv_path.exists():
            self.log("Virtual environment not found", "WARNING")
            return False
        
        # Get Python executable path
        if self.platform == "Windows":
            python_exe = venv_path / "Scripts" / "python.exe"
        else:
            python_exe = venv_path / "bin" / "python"
        
        if not python_exe.exists():
            self.log("Virtual environment Python executable missing", "ERROR")
            return False
        
        # Test if executable works
        try:
            result = subprocess.run([str(python_exe), "--version"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.log(f"Virtual environment Python working: {result.stdout.strip()}", "SUCCESS")
                return True
            else:
                self.log("Virtual environment Python not responding", "ERROR")
                return False
        except Exception as e:
            self.log(f"Virtual environment Python test failed: {e}", "ERROR")
            return False
    
    def create_virtual_environment(self):
        """Create or repair virtual environment"""
        self.log("Creating/repairing virtual environment...")
        
        venv_path = Path("venv")
        
        # Remove existing broken environment
        if venv_path.exists():
            self.log("Removing existing virtual environment...")
            try:
                shutil.rmtree(venv_path)
                self.log("Existing virtual environment removed", "SUCCESS")
            except Exception as e:
                self.log(f"Could not remove existing venv: {e}", "WARNING")
        
        # Create new virtual environment
        try:
            self.log("Creating new virtual environment...")
            subprocess.run([sys.executable, "-m", "venv", "venv"], 
                         check=True, timeout=120)
            self.log("Virtual environment created successfully", "SUCCESS")
            return True
        except subprocess.CalledProcessError as e:
            self.log(f"Failed to create virtual environment: {e}", "ERROR")
            return False
        except subprocess.TimeoutExpired:
            self.log("Virtual environment creation timed out", "ERROR")
            return False
    
    def get_venv_python(self):
        """Get virtual environment Python executable"""
        if self.platform == "Windows":
            return Path("venv/Scripts/python.exe")
        else:
            return Path("venv/bin/python")
    
    def configure_pip(self):
        """Configure pip for corporate/VPN environments"""
        self.log("Configuring pip for corporate/VPN environments...")
        
        if self.platform == "Windows":
            pip_dir = Path.home() / "AppData" / "Roaming" / "pip"
            pip_config = pip_dir / "pip.ini"
        else:
            pip_dir = Path.home() / ".pip"
            pip_config = pip_dir / "pip.conf"
        
        pip_dir.mkdir(exist_ok=True)
        
        config_content = """[global]
trusted-host = pypi.org
               pypi.python.org
               files.pythonhosted.org
timeout = 60
retries = 3
"""
        
        try:
            pip_config.write_text(config_content)
            self.log(f"pip configuration created: {pip_config}", "SUCCESS")
            return True
        except Exception as e:
            self.log(f"Failed to create pip config: {e}", "WARNING")
            return False
    
    def upgrade_pip(self, python_exe):
        """Upgrade pip with multiple strategies"""
        self.log("Upgrading pip...")
        
        commands = [
            [str(python_exe), "-m", "pip", "install", "--upgrade", "pip"],
            [str(python_exe), "-m", "pip", "install", "--trusted-host", "pypi.org",
             "--trusted-host", "pypi.python.org", "--trusted-host", "files.pythonhosted.org",
             "--upgrade", "pip"]
        ]
        
        for cmd in commands:
            try:
                subprocess.run(cmd, check=True, capture_output=True, timeout=120)
                self.log("pip upgraded successfully", "SUCCESS")
                return True
            except Exception:
                continue
        
        self.log("All pip upgrade attempts failed", "WARNING")
        return False
    
    def install_dependencies(self, python_exe):
        """Install dependencies with multiple strategies"""
        self.log("Installing dependencies...")
        
        if not Path("requirements.txt").exists():
            self.log("requirements.txt not found", "ERROR")
            return False
        
        commands = [
            [str(python_exe), "-m", "pip", "install", "-r", "requirements.txt"],
            [str(python_exe), "-m", "pip", "install", "--trusted-host", "pypi.org",
             "--trusted-host", "pypi.python.org", "--trusted-host", "files.pythonhosted.org",
             "-r", "requirements.txt"]
        ]
        
        for cmd in commands:
            try:
                subprocess.run(cmd, check=True, capture_output=True, timeout=300)
                self.log("Dependencies installed successfully", "SUCCESS")
                return True
            except Exception:
                continue
        
        # Try individual package installation
        self.log("Batch install failed, trying individual packages...", "WARNING")
        try:
            with open("requirements.txt", "r") as f:
                for line in f:
                    package = line.strip()
                    if package and not package.startswith("#"):
                        try:
                            subprocess.run([str(python_exe), "-m", "pip", "install", 
                                          "--trusted-host", "pypi.org", "--trusted-host", "pypi.python.org",
                                          "--trusted-host", "files.pythonhosted.org", package],
                                         check=True, capture_output=True, timeout=120)
                            self.log(f"Installed {package}", "SUCCESS")
                        except Exception:
                            self.log(f"Failed to install {package}", "WARNING")
            return True
        except Exception as e:
            self.log(f"Individual package installation failed: {e}", "ERROR")
            return False
    
    def create_data_directory(self):
        """Create data directory if missing"""
        self.log("Checking data directory...")
        
        data_dir = Path("data")
        if not data_dir.exists():
            try:
                data_dir.mkdir(exist_ok=True)
                self.log("Data directory created", "SUCCESS")
            except Exception as e:
                self.log(f"Failed to create data directory: {e}", "ERROR")
                return False
        else:
            self.log("Data directory exists", "SUCCESS")
        
        return True
    
    def test_flask_installation(self, python_exe):
        """Test Flask installation"""
        self.log("Testing Flask installation...")
        
        try:
            result = subprocess.run([str(python_exe), "-c", 
                                   "import flask; print(f'Flask {flask.__version__} ready')"],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.log(result.stdout.strip(), "SUCCESS")
                return True
            else:
                self.log("Flask test failed", "ERROR")
                return False
        except Exception as e:
            self.log(f"Flask test error: {e}", "ERROR")
            return False
    
    def run_comprehensive_setup(self):
        """Run comprehensive setup and repair"""
        self.log("=" * 60)
        self.log("AdhocLog - Comprehensive Setup and Repair")
        self.log("=" * 60)
        
        # Step 1: Check Python
        if not self.check_python():
            self.log("Python check failed - cannot continue", "ERROR")
            return False
        
        # Step 2: Check tkinter
        self.check_tkinter()
        
        # Step 3: Configure pip
        self.configure_pip()
        
        # Step 4: Validate/create virtual environment
        if not self.validate_virtual_environment():
            if not self.create_virtual_environment():
                self.log("Virtual environment setup failed", "ERROR")
                return False
        
        # Step 5: Get virtual environment Python
        python_exe = self.get_venv_python()
        if not python_exe.exists():
            self.log("Virtual environment Python not found after creation", "ERROR")
            return False
        
        # Step 6: Upgrade pip
        self.upgrade_pip(python_exe)
        
        # Step 7: Install dependencies
        if not self.install_dependencies(python_exe):
            self.log("Dependency installation failed", "ERROR")
            return False
        
        # Step 8: Create data directory
        self.create_data_directory()
        
        # Step 9: Test Flask
        self.test_flask_installation(python_exe)
        
        # Summary
        self.log("=" * 60)
        self.log("Setup and Repair Summary")
        self.log("=" * 60)
        self.log(f"✅ Successes: {len(self.successes)}")
        self.log(f"⚠️ Warnings: {len(self.warnings)}")
        self.log(f"❌ Errors: {len(self.errors)}")
        
        if self.errors:
            self.log("Errors encountered:")
            for error in self.errors:
                self.log(f"  • {error}")
        
        if self.warnings:
            self.log("Warnings:")
            for warning in self.warnings:
                self.log(f"  • {warning}")
        
        self.log("=" * 60)
        
        return len(self.errors) == 0

def main():
    """Main function"""
    tool = SetupRepairTool()
    
    try:
        success = tool.run_comprehensive_setup()
        if success:
            print("\n🎉 Setup and repair completed successfully!")
            print("You can now run the application.")
            return 0
        else:
            print("\n❌ Setup and repair completed with errors.")
            print("Please review the errors above and try manual fixes.")
            return 1
    except KeyboardInterrupt:
        print("\n\n👋 Setup interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error during setup: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
