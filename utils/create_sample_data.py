#!/usr/bin/env python3
"""
Script to create sample data for the AdhocLog
"""

import sys
import os
# Add parent directory to path to import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.data_manager import DataManager
from datetime import datetime, timedelta
import random

def create_sample_tasks():
    """Create sample tasks for demonstration"""
    dm = DataManager()

    # Sample task data
    sample_tasks = [
        {
            'title': 'Weekly team meeting and sprint planning',
            'classification': 'Planning',
            'description': 'Attended weekly team meeting to discuss project progress, upcoming deliverables, and resource allocation. Participated in sprint planning session.',
            'est_time': 60,
            'date': (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        },
        {
            'title': 'Create Excel report for monthly metrics',
            'classification': 'Offline Processing',
            'description': 'Generated comprehensive Excel report with formulas and pivot tables showing monthly performance metrics and KPIs.',
            'est_time': 90,
            'date': datetime.now().strftime('%Y-%m-%d')
        },
        {
            'title': 'Client system health check and monitoring',
            'classification': 'Execution',
            'description': 'Performed routine health checks on client systems, monitored performance metrics, and documented any issues found.',
            'est_time': 45,
            'date': datetime.now().strftime('%Y-%m-%d')
        },
        {
            'title': 'Attend company townhall meeting',
            'classification': 'Business Support Activities',
            'description': 'Participated in quarterly company townhall meeting covering business updates, strategic initiatives, and Q&A session.',
            'est_time': 90,
            'date': (datetime.now() - timedelta(days=2)).strftime('%Y-%m-%d')
        },
        {
            'title': 'Coordination meeting with other teams',
            'classification': 'Planning',
            'description': 'Cross-functional meeting to coordinate deliverables and dependencies between engineering teams for upcoming release.',
            'est_time': 45,
            'date': (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d')
        },
        {
            'title': 'Research and investigation on new technology',
            'classification': 'Offline Processing',
            'description': 'Conducted research on emerging technologies and their potential application to current projects. Documented findings and recommendations.',
            'est_time': 120,
            'date': (datetime.now() - timedelta(days=4)).strftime('%Y-%m-%d')
        },
        {
            'title': 'Assist colleague with high-priority issue',
            'classification': 'Operational Project Involvement',
            'description': 'Provided technical assistance to colleague working on critical production issue. Helped troubleshoot and implement solution.',
            'est_time': 75,
            'date': (datetime.now() - timedelta(days=5)).strftime('%Y-%m-%d')
        },
        {
            'title': 'Daily standup meeting',
            'classification': 'Business Support Activities',
            'description': 'Participated in daily standup meeting, shared progress updates, discussed blockers, and coordinated with team members.',
            'est_time': 15,
            'date': (datetime.now() - timedelta(days=6)).strftime('%Y-%m-%d')
        },
        {
            'title': 'Presenting project status to managers',
            'classification': 'Execution',
            'description': 'Delivered project status presentation to management team, highlighting achievements, challenges, and next steps.',
            'est_time': 60,
            'date': (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        },
        {
            'title': 'Technical training and certification',
            'classification': 'Offline Processing',
            'description': 'Completed online technical training course and certification exam to enhance skills in cloud technologies.',
            'est_time': 180,
            'date': (datetime.now() - timedelta(days=8)).strftime('%Y-%m-%d')
        }
    ]

    print(f"Creating sample data for user: {dm.username}")

    for task_data in sample_tasks:
        try:
            task = dm.add_task(task_data)
            print(f"✓ Created task: {task['title']}")
        except Exception as e:
            print(f"✗ Error creating task '{task_data['title']}': {e}")

    print(f"\nSample data creation complete!")
    print(f"Total tasks created: {len(sample_tasks)}")
    print(f"Data saved to: {dm.data_file}")

if __name__ == '__main__':
    create_sample_tasks()
